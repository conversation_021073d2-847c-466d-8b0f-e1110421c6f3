---
type: "always_apply"
description: "Analyzes all JavaScript files in the server/ directory to identify and remove unused functions while preserving route handlers, middleware, database functions, and core system functionality for the Hauling QR Trip System"
---
Analyze all JavaScript files in the `server/` directory and remove unused functions while preserving code functionality. Given the context of our Hauling QR Trip System with its complex shift management, assignment synchronization, and API endpoints, perform the following systematic cleanup:

**Scope and Files to Analyze:**
1. Analyze ALL `.js` files in the `server/` directory recursively, including:
   - `server/routes/*.js` (API route handlers)
   - `server/utils/*.js` (utility functions and helpers)
   - `server/middleware/*.js` (authentication and middleware)
   - `server/models/*.js` (database models if present)
   - `server/server.js` (main server file)

**Function Identification Process:**
1. Identify ALL function types:
   - Function declarations: `function functionName() {}`
   - Function expressions: `const functionName = function() {}`
   - Arrow functions: `const functionName = () => {}`
   - Method definitions in objects/classes: `methodName() {}`
   - Async functions: `async function functionName() {}`

**Usage Analysis - Check if each function is:**
1. **Exported** via any method:
   - `module.exports = functionName`
   - `module.exports.functionName = functionName`
   - `exports.functionName = functionName`
   - ES6 exports: `export function functionName()` or `export { functionName }`
2. **Called/invoked** within the same file:
   - Direct calls: `functionName()`
   - Method calls: `object.functionName()`
   - Callback usage: `array.map(functionName)`
3. **Referenced by name** (indirect usage):
   - Assigned to variables: `const handler = functionName`
   - Passed as parameters: `router.get('/path', functionName)`
   - Used in object literals: `{ handler: functionName }`
   - Referenced in template literals or string concatenation

**Critical Preservation Rules:**
1. **DO NOT REMOVE** functions that are:
   - Express.js route handlers (even if only referenced in router definitions)
   - Middleware functions (authentication, validation, error handling)
   - Database query functions used by routes
   - Utility functions supporting shift synchronization or assignment management
   - Event handlers or callback functions
   - Functions used in our SimpleShiftSyncMonitor or related monitoring systems
2. **PRESERVE** any function that supports:
   - Shift management operations (cancel, complete, create)
   - Assignment Management synchronization
   - Trip workflow (4-phase: assigned → loading_start → loading_end → unloading_start → unloading_end → trip_completed)
   - Authentication and authorization
   - Database connections and queries

**Removal Criteria - Remove ONLY functions that meet ALL conditions:**
1. Not exported from the module
2. Not called or referenced anywhere within the file
3. Not used as route handlers, middleware, or callbacks
4. Not supporting any core system functionality
5. Confirmed to be completely isolated with no dependencies

**Verification Requirements:**
1. **Cross-file analysis**: Check if functions are imported/required by other files
2. **Dynamic usage patterns**: Look for `eval()`, `this[functionName]()`, or string-based function calls
3. **Configuration objects**: Check if functions are referenced in config objects or arrays
4. **Route definitions**: Verify route handlers are not accidentally removed
5. **Test the system**: Ensure shift management, assignment synchronization, and monitoring systems remain functional

**Implementation Process:**
1. Create a comprehensive inventory of all functions across server files
2. Map dependencies and usage patterns between files
3. Identify truly unused functions with 100% certainty
4. Remove unused functions while maintaining code structure and formatting
5. Verify no breaking changes to existing functionality
6. Test critical workflows: shift operations, assignment sync, and monitoring

**Output Requirements:**
- Provide a detailed report of functions analyzed and removed
- List any functions that were borderline cases and why they were preserved
- Confirm that all core system functionality remains intact
- Maintain proper indentation, comments, and code structure