/**
 * Shift Management Service
 * Handles all shift-related API operations
 */

import { getApiBaseUrl } from '../utils/network-utils';

class ShiftService {
  constructor() {
    this.baseUrl = getApiBaseUrl();
  }

  /**
   * Get authentication headers
   */
  getAuthHeaders() {
    const token = localStorage.getItem('hauling_token');
    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    };
  }

  /**
   * Get all shifts with optional filtering
   */
  async getShifts(filters = {}) {
    try {
      const params = new URLSearchParams();
      
      Object.keys(filters).forEach(key => {
        if (filters[key] !== undefined && filters[key] !== null && filters[key] !== '') {
          params.append(key, filters[key]);
        }
      });

      const url = `${this.baseUrl}/shifts${params.toString() ? `?${params.toString()}` : ''}`;
      
      const response = await fetch(url, {
        method: 'GET',
        headers: this.getAuthHeaders()
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch shifts: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching shifts:', error);
      throw error;
    }
  }

  /**
   * Get current active shift for a truck
   */
  async getCurrentShift(truckId) {
    try {
      const response = await fetch(`${this.baseUrl}/shifts/current/${truckId}`, {
        method: 'GET',
        headers: this.getAuthHeaders()
      });

      if (response.status === 404) {
        return { success: true, data: null }; // No active shift
      }

      if (!response.ok) {
        throw new Error(`Failed to fetch current shift: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching current shift:', error);
      throw error;
    }
  }

  /**
   * Create a new shift
   */
  async createShift(shiftData) {
    try {

      const response = await fetch(`${this.baseUrl}/shifts`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(shiftData)
      });

      // Create response received

      if (!response.ok) {
        const errorData = await response.json();
        console.error('SHIFT_SERVICE_CREATE_ERROR', 'Create failed', {
          status: response.status,
          error_data: errorData,
          request_data: shiftData
        });
        throw new Error(errorData.message || `Failed to create shift: ${response.statusText}`);
      }

      const result = await response.json();

      return result;
    } catch (error) {
      console.error('SHIFT_SERVICE_CREATE_EXCEPTION', 'Create exception', {
        error: error.message,
        request_data: shiftData
      });
      throw error;
    }
  }

  /**
   * Update an existing shift
   */
  async updateShift(shiftId, updateData) {
    try {

      const response = await fetch(`${this.baseUrl}/shifts/${shiftId}`, {
        method: 'PUT',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(updateData)
      });

      // Update response received

      if (!response.ok) {
        const errorData = await response.json();
        console.error('SHIFT_SERVICE_UPDATE_ERROR', 'Update failed', {
          shift_id: shiftId,
          status: response.status,
          error_data: errorData,
          request_data: updateData
        });
        throw new Error(errorData.message || `Failed to update shift: ${response.statusText}`);
      }

      const result = await response.json();

      return result;
    } catch (error) {
      console.error('SHIFT_SERVICE_UPDATE_EXCEPTION', 'Update exception', {
        shift_id: shiftId,
        error: error.message,
        update_data: updateData
      });
      throw error;
    }
  }

  /**
   * Cancel a shift (soft delete - changes status to cancelled)
   */
  async cancelShift(shiftId) {
    try {
      const response = await fetch(`${this.baseUrl}/shifts/${shiftId}/cancel`, {
        method: 'PATCH',
        headers: this.getAuthHeaders()
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Failed to cancel shift: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error cancelling shift:', error);
      throw error;
    }
  }

  /**
   * Complete a shift (changes status to completed)
   */
  async completeShift(shiftId) {
    try {
      const response = await fetch(`${this.baseUrl}/shifts/${shiftId}/complete`, {
        method: 'PATCH',
        headers: this.getAuthHeaders()
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Failed to complete shift: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error completing shift:', error);
      throw error;
    }
  }

  /**
   * Delete a shift permanently (enhanced with related shifts option)
   */
  async deleteShift(shiftId, deleteRelated = false) {
    try {
      const url = deleteRelated
        ? `${this.baseUrl}/shifts/${shiftId}?delete_related=true`
        : `${this.baseUrl}/shifts/${shiftId}`;

      const response = await fetch(url, {
        method: 'DELETE',
        headers: this.getAuthHeaders()
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Failed to delete shift: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error deleting shift:', error);
      throw error;
    }
  }

  /**
   * Delete entire shift group (all related shifts created together)
   */
  async deleteShiftGroup(shiftId) {
    try {
      const response = await fetch(`${this.baseUrl}/shifts/group/${shiftId}`, {
        method: 'DELETE',
        headers: this.getAuthHeaders()
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Failed to delete shift group: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error deleting shift group:', error);
      throw error;
    }
  }

  /**
   * Create a shift handover
   */
  async createHandover(handoverData) {
    try {
      const response = await fetch(`${this.baseUrl}/shifts/handover`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(handoverData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Failed to create handover: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error creating handover:', error);
      throw error;
    }
  }

  /**
   * Get shift handovers with optional filtering
   */
  async getHandovers(filters = {}) {
    try {
      const params = new URLSearchParams();
      
      Object.keys(filters).forEach(key => {
        if (filters[key] !== undefined && filters[key] !== null && filters[key] !== '') {
          params.append(key, filters[key]);
        }
      });

      const url = `${this.baseUrl}/shifts/handovers${params.toString() ? `?${params.toString()}` : ''}`;
      
      const response = await fetch(url, {
        method: 'GET',
        headers: this.getAuthHeaders()
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch handovers: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching handovers:', error);
      throw error;
    }
  }

  /**
   * Manually activate a shift
   */
  async activateShift(shiftId) {
    try {
      const response = await fetch(`${this.baseUrl}/shifts/activate/${shiftId}`, {
        method: 'POST',
        headers: this.getAuthHeaders()
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Failed to activate shift: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error activating shift:', error);
      throw error;
    }
  }

  /**
   * Get shifts for a specific date range
   */
  async getShiftsByDateRange(startDate, endDate, filters = {}) {
    return this.getShifts({
      ...filters,
      start_date: startDate,
      end_date: endDate
    });
  }



  /**
   * Get shifts for today
   */
  async getTodayShifts(filters = {}) {
    const today = new Intl.DateTimeFormat('en-CA', { timeZone: 'Asia/Manila' }).format(new Date());
    return this.getShifts({
      ...filters,
      date_from: today,
      date_to: today
    });
  }

  /**
   * Get shifts for a specific truck
   */
  async getTruckShifts(truckId, filters = {}) {
    return this.getShifts({
      ...filters,
      truck_id: truckId
    });
  }

  /**
   * Get shifts for a specific driver
   */
  async getDriverShifts(driverId, filters = {}) {
    return this.getShifts({
      ...filters,
      driver_id: driverId
    });
  }

  /**
   * Get active shifts
   */
  async getActiveShifts(filters = {}) {
    return this.getShifts({
      ...filters,
      status: 'active'
    });
  }

  /**
   * Get scheduled shifts
   */


  /**
   * Utility function to format shift time for display
   */
  formatShiftTime(timeString) {
    if (!timeString) return '';
    
    const [hours, minutes] = timeString.split(':');
    const hour = parseInt(hours);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
    
    return `${displayHour}:${minutes} ${ampm}`;
  }

  /**
   * Utility function to get shift type display name
   */
  getShiftTypeDisplay(shiftType) {
    const types = {
      'day': 'Day Shift',
      'night': 'Night Shift',
      'custom': 'Custom Shift'
    };
    
    return types[shiftType] || shiftType;
  }

  /**
   * Utility function to get shift status display
   */
  getShiftStatusDisplay(status) {
    const statuses = {
      'active': 'Active',
      'completed': 'Completed',
      'cancelled': 'Cancelled'
    };
    
    return statuses[status] || status;
  }

  /**
   * Utility function to get shift status color
   */
  getShiftStatusColor(status) {
    const colors = {
      'active': 'text-green-600 bg-green-50',
      'completed': 'text-gray-600 bg-gray-50',
      'cancelled': 'text-red-600 bg-red-50'
    };
    
    return colors[status] || 'text-gray-600 bg-gray-50';
  }
}

// Create and export singleton instance
const shiftService = new ShiftService();
export default shiftService;
