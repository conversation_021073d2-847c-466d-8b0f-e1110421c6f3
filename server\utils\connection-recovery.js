/**
 * Database Connection Recovery Utilities
 * Handles transaction abort recovery and connection health monitoring
 */

const { getClient } = require('../config/database');

/**
 * Check if error is a transaction abort error
 */
function isTransactionAbortError(error) {
  return error && error.message && 
    error.message.includes('current transaction is aborted');
}

/**
 * Check if error is a connection error
 */
function isConnectionError(error) {
  const connectionErrorCodes = [
    'ECONNRESET',
    'ENOTFOUND', 
    'ECONNREFUSED',
    '57P01', // admin_shutdown
    '57P02', // crash_shutdown
    '57P03', // cannot_connect_now
    '08000', // connection_exception
    '08003', // connection_does_not_exist
    '08006'  // connection_failure
  ];
  
  return error && (
    connectionErrorCodes.includes(error.code) ||
    (error.message && error.message.includes('connection'))
  );
}

/**
 * Recover from transaction abort by rolling back and restarting
 */
async function recoverFromTransactionAbort(client) {
  try {
    console.log('🔄 Attempting transaction abort recovery...');
    
    // Try to rollback the aborted transaction
    await client.query('ROLLBACK');
    console.log('✅ Transaction rolled back successfully');
    
    // Start a new transaction
    await client.query('BEGIN');
    console.log('✅ New transaction started');
    
    return true;
  } catch (recoveryError) {
    console.error('❌ Transaction recovery failed:', recoveryError.message);
    return false;
  }
}

/**
 * Execute query with automatic transaction abort recovery
 */
async function executeWithRecovery(client, queryText, params = []) {
  try {
    return await client.query(queryText, params);
  } catch (error) {
    if (isTransactionAbortError(error)) {
      console.log('🔄 Transaction abort detected, attempting recovery...');
      
      const recovered = await recoverFromTransactionAbort(client);
      if (recovered) {
        console.log('✅ Transaction recovered, retrying query...');
        return await client.query(queryText, params);
      } else {
        throw new Error(`Transaction recovery failed: ${error.message}`);
      }
    } else {
      throw error;
    }
  }
}

/**
 * Safe transaction wrapper with automatic recovery
 */
async function safeTransaction(callback, maxRetries = 2) {
  let client;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      client = await getClient();
      await client.query('BEGIN');
      
      const result = await callback(client);
      
      await client.query('COMMIT');
      return result;
      
    } catch (error) {
      if (client) {
        try {
          await client.query('ROLLBACK');
        } catch (rollbackError) {
          console.error('Rollback failed:', rollbackError.message);
        }
      }
      
      if (attempt < maxRetries && (
        isTransactionAbortError(error) || 
        isConnectionError(error) ||
        error.code === '40001' || // serialization_failure
        error.code === '40P01'    // deadlock_detected
      )) {
        console.log(`🔄 Transaction failed (attempt ${attempt}/${maxRetries}), retrying...`);
        await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
        continue;
      }
      
      throw error;
    } finally {
      if (client) {
        client.release();
      }
    }
  }
}

/**
 * Health check for database connection
 */
async function checkDatabaseHealth() {
  try {
    const client = await getClient();
    try {
      const result = await client.query('SELECT 1 as health_check');
      return {
        healthy: true,
        timestamp: new Date().toISOString(),
        response_time: Date.now()
      };
    } finally {
      client.release();
    }
  } catch (error) {
    return {
      healthy: false,
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * Monitor connection health and log issues
 */
function startHealthMonitoring(intervalMs = 60000) {
  setInterval(async () => {
    const health = await checkDatabaseHealth();
    if (!health.healthy) {
      console.error('❌ Database health check failed:', health.error);
    } else {
      console.log('✅ Database health check passed');
    }
  }, intervalMs);
}

module.exports = {
  isTransactionAbortError,
  isConnectionError,
  recoverFromTransactionAbort,
  executeWithRecovery,
  safeTransaction,
  checkDatabaseHealth,
  startHealthMonitoring
};
