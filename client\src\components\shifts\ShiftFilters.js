import React from 'react';
import { getDaysInRange } from '../../utils/dateHelpers';

/**
 * Dedicated component for shift filtering UI
 * Extracted from SimplifiedShiftManagement for better maintainability
 */
const ShiftFilters = ({
  filters,
  trucks,
  drivers,
  onFilterChange,
  onClearFilters,
  onDatePreset
}) => {
  const getActiveFilterCount = () => {
    let count = 0;
    if (filters.truck_id) count++;
    if (filters.driver_id) count++;
    if (filters.status) count++;
    if (filters.shift_type) count++;
    return count;
  };

  const getFilterDaysInRange = () => getDaysInRange(filters.date_from, filters.date_to);

  return (
    <div className="mb-6 bg-white rounded-lg shadow border border-secondary-200 p-4">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-6 gap-4">
        
        {/* Date Range Filter */}
        <div className="lg:col-span-2">
          <label className="block text-sm font-medium text-secondary-700 mb-1">
            Date Range ({getFilterDaysInRange()} days)
          </label>
          <div className="space-y-2">
            <div className="flex space-x-1">
              <input
                type="date"
                value={filters.date_from}
                onChange={(e) => onFilterChange('date_from', e.target.value)}
                className="input text-sm flex-1"
                placeholder="From"
              />
              <input
                type="date"
                value={filters.date_to}
                onChange={(e) => onFilterChange('date_to', e.target.value)}
                className="input text-sm flex-1"
                placeholder="To"
              />
            </div>
            <div className="flex space-x-1">
              <button
                onClick={() => onDatePreset('today')}
                className="px-2 py-1 text-xs bg-secondary-100 text-secondary-600 rounded hover:bg-secondary-200 transition-colors"
              >
                Today
              </button>
              <button
                onClick={() => onDatePreset('week')}
                className="px-2 py-1 text-xs bg-secondary-100 text-secondary-600 rounded hover:bg-secondary-200 transition-colors"
              >
                Week
              </button>
              <button
                onClick={() => onDatePreset('month')}
                className="px-2 py-1 text-xs bg-secondary-100 text-secondary-600 rounded hover:bg-secondary-200 transition-colors"
              >
                Month
              </button>
            </div>
          </div>
        </div>

        {/* Truck Filter */}
        <div>
          <label className="block text-sm font-medium text-secondary-700 mb-1">
            Truck
          </label>
          <select
            value={filters.truck_id}
            onChange={(e) => onFilterChange('truck_id', e.target.value)}
            className="input text-sm w-full"
          >
            <option value="">All Trucks</option>
            {trucks.map(truck => (
              <option key={truck.id} value={truck.id}>
                {truck.truck_number}
              </option>
            ))}
          </select>
        </div>

        {/* Driver Filter */}
        <div>
          <label className="block text-sm font-medium text-secondary-700 mb-1">
            Driver
          </label>
          <select
            value={filters.driver_id}
            onChange={(e) => onFilterChange('driver_id', e.target.value)}
            className="input text-sm w-full"
          >
            <option value="">All Drivers</option>
            {drivers.map(driver => (
              <option key={driver.id} value={driver.id}>
                {driver.full_name}
              </option>
            ))}
          </select>
        </div>

        {/* Status Filter */}
        <div>
          <label className="block text-sm font-medium text-secondary-700 mb-1">
            Status
          </label>
          <select
            value={filters.status}
            onChange={(e) => onFilterChange('status', e.target.value)}
            className="input text-sm w-full"
          >
            <option value="">All Statuses</option>
            <option value="scheduled">Scheduled</option>
            <option value="active">Active</option>
            <option value="completed">Completed</option>
            <option value="cancelled">Cancelled</option>
          </select>
        </div>

        {/* Shift Type Filter */}
        <div>
          <label className="block text-sm font-medium text-secondary-700 mb-1">
            Shift Type
          </label>
          <select
            value={filters.shift_type}
            onChange={(e) => onFilterChange('shift_type', e.target.value)}
            className="input text-sm w-full"
          >
            <option value="">All Types</option>
            <option value="day">☀️ Day Shift</option>
            <option value="night">🌙 Night Shift</option>
          </select>
        </div>

        {/* Clear Filters */}
        <div className="flex items-end">
          <button
            onClick={onClearFilters}
            disabled={getActiveFilterCount() === 0}
            className="btn btn-secondary w-full disabled:opacity-50 disabled:cursor-not-allowed text-sm"
          >
            Clear Filters
            {getActiveFilterCount() > 0 && (
              <span className="ml-1 bg-primary-100 text-primary-800 text-xs rounded-full px-2 py-0.5">
                {getActiveFilterCount()}
              </span>
            )}
          </button>
        </div>
      </div>

      {/* Filter Summary */}
      {getActiveFilterCount() > 0 && (
        <div className="mt-3 pt-3 border-t border-secondary-200">
          <div className="text-sm text-secondary-600">
            <span className="font-medium">Active Filters:</span>
            {filters.truck_id && (
              <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs bg-blue-100 text-blue-800">
                Truck: {trucks.find(t => t.id === parseInt(filters.truck_id))?.truck_number || filters.truck_id}
              </span>
            )}
            {filters.driver_id && (
              <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs bg-green-100 text-green-800">
                Driver: {drivers.find(d => d.id === parseInt(filters.driver_id))?.full_name || filters.driver_id}
              </span>
            )}
            {filters.status && (
              <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs bg-yellow-100 text-yellow-800">
                Status: {filters.status}
              </span>
            )}
            {filters.shift_type && (
              <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs bg-purple-100 text-purple-800">
                Type: {filters.shift_type}
              </span>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default ShiftFilters;