#!/usr/bin/env node

/**
 * IP Detection Utility
 * Automatically detects the local network IP address and updates configuration
 */

const os = require('os');
const fs = require('fs');
const path = require('path');

class IPDetectionService {
  constructor() {
    this.envPath = path.join(__dirname, '..', '.env');
    this.detectedIP = null;
    this.fallbackIP = 'localhost';
  }

  /**
   * Get the local network IP address
   */
  getLocalNetworkIP() {
    const interfaces = os.networkInterfaces();
    
    // Priority order for interface selection
    const interfacePriority = [
      'Wi-Fi',           // Windows WiFi
      'Wireless',        // Windows Wireless
      'wlan0',          // Linux WiFi
      'en0',            // macOS WiFi
      'Ethernet',       // Windows Ethernet
      'eth0',           // Linux Ethernet
      'en1'             // macOS Ethernet
    ];

    // First, try priority interfaces
    for (const interfaceName of interfacePriority) {
      if (interfaces[interfaceName]) {
        const ip = this.getIPFromInterface(interfaces[interfaceName]);
        if (ip) {
          return ip;
        }
      }
    }

    // If no priority interface found, scan all interfaces
    for (const interfaceName in interfaces) {
      const ip = this.getIPFromInterface(interfaces[interfaceName]);
      if (ip) {
        return ip;
      }
    }

    return this.fallbackIP;
  }

  /**
   * Extract IPv4 address from network interface
   */
  getIPFromInterface(interfaceArray) {
    for (const iface of interfaceArray) {
      // Look for IPv4, non-internal addresses
      if (iface.family === 'IPv4' && !iface.internal) {
        // Prefer private network ranges
        if (this.isPrivateIP(iface.address)) {
          return iface.address;
        }
      }
    }
    return null;
  }

  /**
   * Check if IP is in private network range
   */
  isPrivateIP(ip) {
    const parts = ip.split('.').map(Number);
    
    // 192.168.x.x
    if (parts[0] === 192 && parts[1] === 168) return true;
    
    // 10.x.x.x
    if (parts[0] === 10) return true;
    
    // 172.16.x.x - 172.31.x.x
    if (parts[0] === 172 && parts[1] >= 16 && parts[1] <= 31) return true;
    
    return false;
  }

  /**
   * Read current .env file
   */
  readEnvFile() {
    try {
      if (fs.existsSync(this.envPath)) {
        return fs.readFileSync(this.envPath, 'utf8');
      }
    } catch (error) {
      console.error('❌ Error reading .env file:', error.message);
    }
    return '';
  }

  /**
   * Update .env file with detected IP
   */
  updateEnvFile(detectedIP) {
    try {
      let envContent = this.readEnvFile();
      
      if (!envContent) {
        envContent = this.createDefaultEnvContent(detectedIP);
      } else {
        envContent = this.updateEnvContent(envContent, detectedIP);
      }

      fs.writeFileSync(this.envPath, envContent, 'utf8');
      
      return true;
    } catch (error) {
      console.error('❌ Error updating .env file:', error.message);
      return false;
    }
  }

  /**
   * Update existing .env content with new IP
   */
  updateEnvContent(content, ip) {
    const useHttps = content.includes('ENABLE_HTTPS=true');
    const protocol = useHttps ? 'https' : 'http';
    const wsProtocol = useHttps ? 'wss' : 'ws';
    const backendPort = this.extractPortFromEnv(content, 'BACKEND_PORT') || '5000';
    
    // Update the auto-generated URLs
    content = this.updateEnvVariable(content, 'REACT_APP_API_URL', `${protocol}://${ip}:${backendPort}/api`);
    content = this.updateEnvVariable(content, 'REACT_APP_WS_URL', `${wsProtocol}://${ip}:${backendPort}`);
    content = this.updateEnvVariable(content, 'REACT_APP_LOCAL_NETWORK_IP', ip);
    content = this.updateEnvVariable(content, 'REACT_APP_USE_HTTPS', useHttps.toString());

    return content;
  }

  /**
   * Extract port number from .env content
   */
  extractPortFromEnv(content, portVar) {
    const match = content.match(new RegExp(`${portVar}=(.+)`));
    return match ? match[1].trim() : null;
  }

  /**
   * Update or add environment variable in content
   */
  updateEnvVariable(content, varName, value) {
    const regex = new RegExp(`^${varName}=.*$`, 'm');
    const newLine = `${varName}=${value}`;
    
    if (regex.test(content)) {
      return content.replace(regex, newLine);
    } else {
      // Add new variable at the end
      return content + `\n${newLine}`;
    }
  }

  /**
   * Create default .env content
   */
  createDefaultEnvContent(ip) {
    return `# Hauling QR Trip System - Auto-generated Configuration
NODE_ENV=development
AUTO_DETECT_IP=true
ENABLE_HTTPS=false

# Port Configuration
FRONTEND_PORT=3000
BACKEND_PORT=5000

# Simplified CORS
ALLOWED_ORIGINS=localhost,127.0.0.1,0.0.0.0

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=hauling_qr_system
DB_USER=postgres
DB_PASSWORD=PostgreSQLPassword

# Auto-generated URLs
REACT_APP_API_URL=http://${ip}:5000/api
REACT_APP_WS_URL=ws://${ip}:5000
REACT_APP_USE_HTTPS=false
REACT_APP_LOCAL_NETWORK_IP=${ip}
`;
  }

  /**
   * Generate simplified CORS origins
   */
  generateCorsOrigins(ip, frontendPort = '3000', backendPort = '5000') {
    const baseOrigins = ['localhost', '127.0.0.1', '0.0.0.0'];
    const protocols = ['http', 'https'];
    const ports = [frontendPort, backendPort];
    
    const origins = [];
    
    // Add base origins with ports
    for (const origin of baseOrigins) {
      for (const protocol of protocols) {
        for (const port of ports) {
          origins.push(`${protocol}://${origin}:${port}`);
        }
      }
    }
    
    // Add wildcard for development
    origins.push('*');
    
    return origins;
  }

  /**
   * Main detection and update process
   */
  async detectAndUpdate() {
    // Load environment variables
    require('dotenv').config({ path: this.envPath });

    // Check if auto-detection is enabled
    const autoDetectEnabled = process.env.AUTO_DETECT_IP !== 'false';
    if (!autoDetectEnabled) {
      return false;
    }

    // Detect IP
    this.detectedIP = this.getLocalNetworkIP();
    
    if (!this.detectedIP || this.detectedIP === this.fallbackIP) {
      // Using fallback IP
    }

    // Update .env file
    const success = this.updateEnvFile(this.detectedIP);
    
    if (success) {
      // IP detection and configuration update completed successfully
    }

    return success;
  }

  /**
   * Get current configuration summary
   */
  getConfigSummary() {
    const envContent = this.readEnvFile();
    const apiUrl = this.extractEnvVariable(envContent, 'REACT_APP_API_URL');
    const wsUrl = this.extractEnvVariable(envContent, 'REACT_APP_WS_URL');
    const ip = this.extractEnvVariable(envContent, 'REACT_APP_LOCAL_NETWORK_IP');
    
    return {
      detectedIP: ip || this.detectedIP,
      apiUrl: apiUrl || 'Not configured',
      wsUrl: wsUrl || 'Not configured',
      autoDetectEnabled: !envContent.includes('AUTO_DETECT_IP=false')
    };
  }

  /**
   * Extract environment variable value
   */
  extractEnvVariable(content, varName) {
    const match = content.match(new RegExp(`^${varName}=(.+)$`, 'm'));
    return match ? match[1].trim() : null;
  }
}

// Export for use as module
module.exports = IPDetectionService;

// Run directly if called as script
if (require.main === module) {
  const ipDetection = new IPDetectionService();
  ipDetection.detectAndUpdate().then(success => {
    process.exit(success ? 0 : 1);
  });
}
