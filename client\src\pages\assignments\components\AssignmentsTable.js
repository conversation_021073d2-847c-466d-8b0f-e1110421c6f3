import React from 'react';

const AssignmentsTable = ({
  assignments,
  loading,
  pagination,
  filters,
  onPageChange,
  onSort,
  onEdit,
  onDelete
}) => {
  // Driver information now comes directly from backend with shift integration
  // Get status badge for assignment status
  const getStatusBadge = (status) => {
    const statusStyles = {
      'assigned': 'bg-blue-100 text-blue-800',
      'in_progress': 'bg-yellow-100 text-yellow-800',
      'completed': 'bg-success-100 text-success-800',
      'cancelled': 'bg-danger-100 text-danger-800'
    };

    const statusLabels = {
      'assigned': 'Assigned',
      'in_progress': 'In Progress',
      'completed': 'Completed',
      'cancelled': 'Cancelled'
    };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusStyles[status] || 'bg-secondary-100 text-secondary-800'}`}>
        {statusLabels[status] || status}
      </span>
    );
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Format datetime for display
  const formatDateTime = (dateString) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Sortable header component
  const SortableHeader = ({ column, children }) => {
    const isSorted = filters.sortBy === column;
    const isAsc = filters.sortOrder === 'asc';

    return (
      <th 
        scope="col" 
        className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider cursor-pointer hover:bg-secondary-50 select-none"
        onClick={() => onSort(column)}
      >
        <div className="flex items-center space-x-1">
          <span>{children}</span>
          <div className="flex flex-col">
            <svg 
              className={`w-3 h-3 ${isSorted && isAsc ? 'text-primary-600' : 'text-secondary-400'}`} 
              fill="currentColor" 
              viewBox="0 0 20 20"
            >
              <path fillRule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clipRule="evenodd" />
            </svg>
            <svg 
              className={`w-3 h-3 -mt-1 ${isSorted && !isAsc ? 'text-primary-600' : 'text-secondary-400'}`} 
              fill="currentColor" 
              viewBox="0 0 20 20"
            >
              <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </div>
        </div>
      </th>
    );
  };

  // Pagination component
  const Pagination = () => {
    if (pagination.totalPages <= 1) return null;

    const getPageNumbers = () => {
      const pages = [];
      const { currentPage, totalPages } = pagination;
      const maxVisible = 5;

      if (totalPages <= maxVisible) {
        for (let i = 1; i <= totalPages; i++) {
          pages.push(i);
        }
      } else {
        if (currentPage <= 3) {
          for (let i = 1; i <= 4; i++) pages.push(i);
          pages.push('...');
          pages.push(totalPages);
        } else if (currentPage >= totalPages - 2) {
          pages.push(1);
          pages.push('...');
          for (let i = totalPages - 3; i <= totalPages; i++) pages.push(i);
        } else {
          pages.push(1);
          pages.push('...');
          for (let i = currentPage - 1; i <= currentPage + 1; i++) pages.push(i);
          pages.push('...');
          pages.push(totalPages);
        }
      }
      return pages;
    };

    return (
      <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-secondary-200 sm:px-6">
        <div className="flex-1 flex justify-between sm:hidden">
          <button
            onClick={() => onPageChange(pagination.currentPage - 1)}
            disabled={!pagination.hasPrevPage}
            className="relative inline-flex items-center px-4 py-2 border border-secondary-300 text-sm font-medium rounded-md text-secondary-700 bg-white hover:bg-secondary-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Previous
          </button>
          <button
            onClick={() => onPageChange(pagination.currentPage + 1)}
            disabled={!pagination.hasNextPage}
            className="ml-3 relative inline-flex items-center px-4 py-2 border border-secondary-300 text-sm font-medium rounded-md text-secondary-700 bg-white hover:bg-secondary-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Next
          </button>
        </div>
        <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
          <div>
            <p className="text-sm text-secondary-700">
              Showing{' '}
              <span className="font-medium">
                {(pagination.currentPage - 1) * pagination.itemsPerPage + 1}
              </span>{' '}
              to{' '}
              <span className="font-medium">
                {Math.min(pagination.currentPage * pagination.itemsPerPage, pagination.totalItems)}
              </span>{' '}
              of{' '}
              <span className="font-medium">{pagination.totalItems}</span>{' '}
              results
            </p>
          </div>
          <div>
            <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
              <button
                onClick={() => onPageChange(pagination.currentPage - 1)}
                disabled={!pagination.hasPrevPage}
                className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-secondary-300 bg-white text-sm font-medium text-secondary-500 hover:bg-secondary-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span className="sr-only">Previous</span>
                <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </button>
              
              {getPageNumbers().map((page, index) => (
                page === '...' ? (
                  <span key={index} className="relative inline-flex items-center px-4 py-2 border border-secondary-300 bg-white text-sm font-medium text-secondary-700">
                    ...
                  </span>
                ) : (
                  <button
                    key={index}
                    onClick={() => onPageChange(page)}
                    className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                      page === pagination.currentPage
                        ? 'z-10 bg-primary-50 border-primary-500 text-primary-600'
                        : 'bg-white border-secondary-300 text-secondary-500 hover:bg-secondary-50'
                    }`}
                  >
                    {page}
                  </button>
                )
              ))}
              
              <button
                onClick={() => onPageChange(pagination.currentPage + 1)}
                disabled={!pagination.hasNextPage}
                className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-secondary-300 bg-white text-sm font-medium text-secondary-500 hover:bg-secondary-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span className="sr-only">Next</span>
                <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                </svg>
              </button>
            </nav>
          </div>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="bg-white shadow overflow-hidden sm:rounded-md">
        <div className="px-4 py-5 sm:p-6">
          <div className="animate-pulse space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex space-x-4">
                <div className="h-4 bg-secondary-200 rounded w-1/6"></div>
                <div className="h-4 bg-secondary-200 rounded w-1/6"></div>
                <div className="h-4 bg-secondary-200 rounded w-1/6"></div>
                <div className="h-4 bg-secondary-200 rounded w-1/6"></div>
                <div className="h-4 bg-secondary-200 rounded w-1/6"></div>
                <div className="h-4 bg-secondary-200 rounded w-1/6"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }  return (
    <div className="bg-white shadow-lg overflow-hidden sm:rounded-lg border border-secondary-200">
      {/* Enhanced responsive table container */}
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-secondary-200">
          <thead className="bg-gradient-to-r from-secondary-50 to-secondary-100">
            <tr>
              <SortableHeader column="assigned_date">
                <span className="hidden sm:inline">Assignment </span>Date
              </SortableHeader>
              <th scope="col" className="px-4 sm:px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                <span className="hidden lg:inline">Truck & </span>Driver
              </th>
              <th scope="col" className="px-4 sm:px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                Route
              </th>
              <SortableHeader column="status">Status</SortableHeader>
              <th scope="col" className="hidden md:table-cell px-4 sm:px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                Schedule
              </th>
              <th scope="col" className="hidden lg:table-cell px-4 sm:px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                Loads
              </th>
              <th scope="col" className="px-4 sm:px-6 py-3 text-right text-xs font-medium text-secondary-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-secondary-200">
            {assignments.length === 0 ? (
              <tr>
                <td colSpan="7" className="px-6 py-12 text-center">
                  <div className="flex flex-col items-center">
                    <svg className="w-12 h-12 text-secondary-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                    </svg>
                    <h3 className="text-lg font-medium text-secondary-900 mb-2">No assignments found</h3>
                    <p className="text-secondary-500">
                      {filters.search || filters.status || filters.date_from || filters.date_to
                        ? 'Try adjusting your search criteria' 
                        : 'Get started by creating your first assignment'
                      }
                    </p>
                  </div>
                </td>
              </tr>
            ) : (              assignments.map((assignment) => (
                <tr key={assignment.id} className="hover:bg-secondary-50 transition-colors duration-150">
                  <td className="px-4 sm:px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-secondary-900">
                      {formatDate(assignment.assigned_date)}
                    </div>
                    {/* Mobile: Show status here on small screens */}
                    <div className="md:hidden mt-2">
                      {getStatusBadge(assignment.status)}
                    </div>
                  </td>
                  <td className="px-4 sm:px-6 py-4">
                    <div className="flex items-start">
                      <span className="text-2xl mr-2 sm:mr-3 flex-shrink-0">🚛</span>
                      <div className="min-w-0 flex-1">
                        <div className="text-sm font-medium text-secondary-900 truncate">
                          {assignment.truck_number} • {assignment.license_plate}
                        </div>
                        <div className="text-sm text-secondary-500 truncate">
                          {assignment.current_shift_driver_name ? (
                            <>
                              <span className="text-green-600 font-medium">👤 {assignment.current_shift_driver_name}</span>
                              <span className="text-secondary-400 ml-2">({assignment.current_shift_employee_id})</span>
                              <span className="ml-2 inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                {assignment.current_shift_type === 'day' ? '☀️ Day' : '🌙 Night'} Shift
                              </span>
                            </>
                          ) : assignment.driver_name ? (
                            <>
                              <span className="text-amber-600 font-medium">👤 {assignment.driver_name}</span>
                              <span className="text-secondary-400 ml-2">({assignment.employee_id})</span>
                              <span className="ml-2 inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800">
                                {assignment.active_shift_status || '⚠️ No Active Shift'}
                              </span>
                            </>
                          ) : (
                            <span className="text-red-600 font-medium">⚠️ No driver assigned</span>
                          )}
                        </div>
                        {/* Mobile: Show loads info here */}
                        <div className="lg:hidden mt-1 text-xs text-secondary-500">
                          {assignment.expected_loads_per_day || 1} loads/day
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-4 sm:px-6 py-4">
                    <div className="text-sm text-secondary-900 space-y-1">
                      <div className="flex items-center">
                        <span className="text-green-600 mr-1 flex-shrink-0">📍</span>
                        <span className="truncate">
                          {assignment.loading_location_name || 'Unknown'} 
                          <span className="hidden sm:inline"> ({assignment.loading_code || 'N/A'})</span>
                        </span>
                      </div>
                      <div className="flex items-center">
                        <span className="text-red-600 mr-1 flex-shrink-0">📍</span>
                        <span className="truncate">
                          {assignment.unloading_location_name || 'Unknown'} 
                          <span className="hidden sm:inline"> ({assignment.unloading_code || 'N/A'})</span>
                        </span>
                      </div>
                      {/* Mobile: Show schedule here */}
                      <div className="md:hidden mt-2 text-xs text-secondary-500">
                        {assignment.start_time && (
                          <div>Start: {formatDateTime(assignment.start_time)}</div>
                        )}
                        {assignment.end_time && (
                          <div>End: {formatDateTime(assignment.end_time)}</div>
                        )}
                        {!assignment.start_time && !assignment.end_time && (
                          <span>Not scheduled</span>
                        )}
                      </div>
                    </div>
                  </td>
                  <td className="hidden md:table-cell px-4 sm:px-6 py-4 whitespace-nowrap">
                    {getStatusBadge(assignment.status)}
                  </td>
                  <td className="hidden md:table-cell px-4 sm:px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-secondary-900">
                      {assignment.start_time && (
                        <div>Start: {formatDateTime(assignment.start_time)}</div>
                      )}
                      {assignment.end_time && (
                        <div>End: {formatDateTime(assignment.end_time)}</div>
                      )}
                      {!assignment.start_time && !assignment.end_time && (
                        <span className="text-secondary-500">Not scheduled</span>
                      )}
                    </div>
                  </td>
                  <td className="hidden lg:table-cell px-4 sm:px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-secondary-900">
                      {assignment.expected_loads_per_day || 1} loads/day
                    </div>
                  </td>
                  <td className="px-4 sm:px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex justify-end space-x-2">
                      <button
                        onClick={() => onEdit(assignment)}
                        className="text-indigo-600 hover:text-indigo-900 transition-colors p-1 rounded-md hover:bg-indigo-50"
                        title="Edit Assignment"
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                      </button>
                      <button
                        onClick={() => onDelete(assignment)}
                        className="text-danger-600 hover:text-danger-900 transition-colors p-1 rounded-md hover:bg-red-50"
                        title="Delete Assignment"
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </button>
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
      <Pagination />
    </div>
  );
};

export default AssignmentsTable;