import React from 'react';

const TrendChart = ({ title, data, type = 'bar' }) => {
  if (!data || data.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow border border-secondary-200 p-6">
        <h3 className="text-lg font-medium text-secondary-900 mb-4">{title}</h3>
        <div className="text-center py-8 text-secondary-500">
          <span className="text-4xl block mb-2">📊</span>
          No trend data available
        </div>
      </div>
    );
  }

  // Calculate max values for scaling
  const maxTrips = Math.max(...data.map(d => Math.max(d.totalTrips, d.completedTrips)));
  const maxDuration = Math.max(...data.map(d => d.avgDuration));

  return (
    <div className="bg-white rounded-lg shadow border border-secondary-200 p-6">
      <h3 className="text-lg font-medium text-secondary-900 mb-4">{title}</h3>
      
      {type === 'bar' && (
        <div className="space-y-4">
          {/* Legend */}
          <div className="flex items-center space-x-4 text-sm">
            <div className="flex items-center">
              <div className="w-3 h-3 bg-blue-500 rounded mr-2"></div>
              <span>Total Trips</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-green-500 rounded mr-2"></div>
              <span>Completed Trips</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-orange-500 rounded mr-2"></div>
              <span>Avg Duration (min)</span>
            </div>
          </div>
          
          {/* Chart */}
          <div className="h-64 flex items-end justify-between space-x-2">
            {data.map((item, index) => {
              const date = new Date(item.date);
              const dateLabel = date.toLocaleDateString('en-US', { 
                month: 'short', 
                day: 'numeric' 
              });
              
              const totalHeight = maxTrips > 0 ? (item.totalTrips / maxTrips) * 200 : 0;
              const completedHeight = maxTrips > 0 ? (item.completedTrips / maxTrips) * 200 : 0;
              const durationHeight = maxDuration > 0 ? (item.avgDuration / maxDuration) * 200 : 0;
              
              return (
                <div key={index} className="flex-1 flex flex-col items-center space-y-1">
                  <div className="w-full flex justify-center items-end space-x-1">
                    {/* Total trips bar */}
                    <div 
                      className="w-3 bg-blue-500 rounded-t"
                      style={{ height: `${totalHeight}px` }}
                      title={`${item.totalTrips} total trips`}
                    ></div>
                    
                    {/* Completed trips bar */}
                    <div 
                      className="w-3 bg-green-500 rounded-t"
                      style={{ height: `${completedHeight}px` }}
                      title={`${item.completedTrips} completed trips`}
                    ></div>
                    
                    {/* Average duration bar (scaled differently) */}
                    <div 
                      className="w-3 bg-orange-500 rounded-t"
                      style={{ height: `${durationHeight}px` }}
                      title={`${item.avgDuration} min avg duration`}
                    ></div>
                  </div>
                  
                  <span className="text-xs text-secondary-600 transform -rotate-45 origin-left">
                    {dateLabel}
                  </span>
                  
                  {/* Data values */}
                  <div className="text-xs text-center text-secondary-500">
                    <div>{item.totalTrips}T</div>
                    <div>{item.completedTrips}C</div>
                    <div>{item.avgDuration}m</div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}
      
      {type === 'line' && (
        <div className="space-y-4">
          {/* Simple line chart representation */}
          <div className="h-48 relative">
            <svg className="w-full h-full">
              {/* Grid lines */}
              {[0, 25, 50, 75, 100].map(y => (
                <line
                  key={y}
                  x1="0"
                  y1={`${y}%`}
                  x2="100%"
                  y2={`${y}%`}
                  stroke="#e5e7eb"
                  strokeWidth="1"
                />
              ))}
              
              {/* Completion rate line */}
              <polyline
                fill="none"
                stroke="#22c55e"
                strokeWidth="2"
                points={data.map((item, index) => {
                  const x = data.length > 1 ? (index / (data.length - 1)) * 100 : 50;
                  const completionRate = parseFloat(item.completionRate) || 0;
                  const y = 100 - completionRate;
                  return `${isNaN(x) ? 50 : x},${isNaN(y) ? 50 : y}`;
                }).join(' ')}
              />

              {/* Data points */}
              {data.map((item, index) => {
                const x = data.length > 1 ? (index / (data.length - 1)) * 100 : 50;
                const completionRate = parseFloat(item.completionRate) || 0;
                const y = 100 - completionRate;
                const safeX = isNaN(x) ? 50 : x;
                const safeY = isNaN(y) ? 50 : y;
                return (
                  <circle
                    key={index}
                    cx={`${safeX}%`}
                    cy={`${safeY}%`}
                    r="3"
                    fill="#22c55e"
                    title={`${item.completionRate || '0'}% completion rate`}
                  />
                );
              })}
            </svg>
          </div>
          
          {/* X-axis labels */}
          <div className="flex justify-between text-xs text-secondary-500">
            {data.map((item, index) => {
              const date = new Date(item.date);
              const dateLabel = date.toLocaleDateString('en-US', { 
                month: 'short', 
                day: 'numeric' 
              });
              return <span key={index}>{dateLabel}</span>;
            })}
          </div>
        </div>
      )}
    </div>
  );
};

const TrendCharts = ({ data, loading }) => {
  if (loading) {
    return (
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {[...Array(2)].map((_, i) => (
          <div key={i} className="animate-pulse bg-secondary-200 h-64 rounded-lg"></div>
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <TrendChart 
        title="Trip Volume Trends" 
        data={data} 
        type="bar"
      />
      
      <TrendChart 
        title="Completion Rate Trends" 
        data={data} 
        type="line"
      />
      
      {/* Summary Statistics */}
      <div className="lg:col-span-2">
        <div className="bg-white rounded-lg shadow border border-secondary-200 p-6">
          <h3 className="text-lg font-medium text-secondary-900 mb-4">
            7-Day Summary
          </h3>
          
          {data && data.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {data.reduce((sum, item) => sum + item.totalTrips, 0)}
                </div>
                <div className="text-sm text-secondary-500">Total Trips</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {data.reduce((sum, item) => sum + item.completedTrips, 0)}
                </div>
                <div className="text-sm text-secondary-500">Completed Trips</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">
                  {Math.round(data.reduce((sum, item) => sum + item.avgDuration, 0) / data.length)}m
                </div>
                <div className="text-sm text-secondary-500">Avg Duration</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {data.length > 0 ? (data.reduce((sum, item) => sum + (parseFloat(item.completionRate) || 0), 0) / data.length).toFixed(1) : '0.0'}%
                </div>
                <div className="text-sm text-secondary-500">Avg Completion Rate</div>
              </div>
            </div>
          ) : (
            <div className="text-center py-4 text-secondary-500">
              No summary data available
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TrendCharts;
