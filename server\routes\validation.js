const express = require('express');
const router = express.Router();
const auth = require('../middleware/auth');
const TripLogsValidationService = require('../services/TripLogsValidationService');
const { logger } = require('../utils/logger');

/**
 * Trip Logs Data Quality Validation API Routes
 * Provides endpoints for validating trip_logs field population completeness
 */

// @route   GET /api/validation/trip-logs/driver-info
// @desc    Validate driver information completeness in trip_logs
// @access  Private (Admin/Supervisor)
router.get('/trip-logs/driver-info', auth, async (req, res) => {
  try {
    // Check user permissions
    if (!['admin', 'supervisor'].includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        error: 'Insufficient permissions',
        message: 'Only admin and supervisor users can access validation reports'
      });
    }

    const timeRangeHours = parseInt(req.query.hours) || 24;
    
    logger.info('Driver information validation requested', {
      context: 'VALIDATION_API.DRIVER_INFO',
      user_id: req.user.id,
      time_range_hours: timeRangeHours
    });

    const validation = await TripLogsValidationService.validateDriverInformationCompleteness(timeRangeHours);

    res.json({
      success: true,
      data: validation,
      metadata: {
        time_range_hours: timeRangeHours,
        generated_at: new Date().toISOString(),
        generated_by: req.user.username
      }
    });

  } catch (error) {
    logger.error('Error in driver information validation endpoint', {
      context: 'VALIDATION_API.DRIVER_INFO_ERROR',
      error: error.message,
      user_id: req.user?.id
    });

    res.status(500).json({
      success: false,
      error: 'Validation Error',
      message: 'Failed to validate driver information completeness'
    });
  }
});

// @route   GET /api/validation/trip-logs/notes-quality
// @desc    Validate notes field quality in trip_logs
// @access  Private (Admin/Supervisor)
router.get('/trip-logs/notes-quality', auth, async (req, res) => {
  try {
    // Check user permissions
    if (!['admin', 'supervisor'].includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        error: 'Insufficient permissions',
        message: 'Only admin and supervisor users can access validation reports'
      });
    }

    const timeRangeHours = parseInt(req.query.hours) || 24;
    
    logger.info('Notes quality validation requested', {
      context: 'VALIDATION_API.NOTES_QUALITY',
      user_id: req.user.id,
      time_range_hours: timeRangeHours
    });

    const validation = await TripLogsValidationService.validateNotesFieldQuality(timeRangeHours);

    res.json({
      success: true,
      data: validation,
      metadata: {
        time_range_hours: timeRangeHours,
        generated_at: new Date().toISOString(),
        generated_by: req.user.username
      }
    });

  } catch (error) {
    logger.error('Error in notes quality validation endpoint', {
      context: 'VALIDATION_API.NOTES_QUALITY_ERROR',
      error: error.message,
      user_id: req.user?.id
    });

    res.status(500).json({
      success: false,
      error: 'Validation Error',
      message: 'Failed to validate notes field quality'
    });
  }
});

// @route   GET /api/validation/trip-logs/location-sequence
// @desc    Validate location sequence accuracy in trip_logs
// @access  Private (Admin/Supervisor)
router.get('/trip-logs/location-sequence', auth, async (req, res) => {
  try {
    // Check user permissions
    if (!['admin', 'supervisor'].includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        error: 'Insufficient permissions',
        message: 'Only admin and supervisor users can access validation reports'
      });
    }

    const timeRangeHours = parseInt(req.query.hours) || 24;
    
    logger.info('Location sequence validation requested', {
      context: 'VALIDATION_API.LOCATION_SEQUENCE',
      user_id: req.user.id,
      time_range_hours: timeRangeHours
    });

    const validation = await TripLogsValidationService.validateLocationSequenceAccuracy(timeRangeHours);

    res.json({
      success: true,
      data: validation,
      metadata: {
        time_range_hours: timeRangeHours,
        generated_at: new Date().toISOString(),
        generated_by: req.user.username
      }
    });

  } catch (error) {
    logger.error('Error in location sequence validation endpoint', {
      context: 'VALIDATION_API.LOCATION_SEQUENCE_ERROR',
      error: error.message,
      user_id: req.user?.id
    });

    res.status(500).json({
      success: false,
      error: 'Validation Error',
      message: 'Failed to validate location sequence accuracy'
    });
  }
});

// @route   GET /api/validation/trip-logs/missing-fields
// @desc    Detect missing required fields in trip_logs
// @access  Private (Admin/Supervisor)
router.get('/trip-logs/missing-fields', auth, async (req, res) => {
  try {
    // Check user permissions
    if (!['admin', 'supervisor'].includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        error: 'Insufficient permissions',
        message: 'Only admin and supervisor users can access validation reports'
      });
    }

    const timeRangeHours = parseInt(req.query.hours) || 24;
    
    logger.info('Missing fields detection requested', {
      context: 'VALIDATION_API.MISSING_FIELDS',
      user_id: req.user.id,
      time_range_hours: timeRangeHours
    });

    const validation = await TripLogsValidationService.detectMissingRequiredFields(timeRangeHours);

    res.json({
      success: true,
      data: validation,
      metadata: {
        time_range_hours: timeRangeHours,
        generated_at: new Date().toISOString(),
        generated_by: req.user.username
      }
    });

  } catch (error) {
    logger.error('Error in missing fields detection endpoint', {
      context: 'VALIDATION_API.MISSING_FIELDS_ERROR',
      error: error.message,
      user_id: req.user?.id
    });

    res.status(500).json({
      success: false,
      error: 'Validation Error',
      message: 'Failed to detect missing required fields'
    });
  }
});

// @route   GET /api/validation/trip-logs/comprehensive
// @desc    Run comprehensive trip_logs validation report
// @access  Private (Admin/Supervisor)
router.get('/trip-logs/comprehensive', auth, async (req, res) => {
  try {
    // Check user permissions
    if (!['admin', 'supervisor'].includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        error: 'Insufficient permissions',
        message: 'Only admin and supervisor users can access validation reports'
      });
    }

    const timeRangeHours = parseInt(req.query.hours) || 24;
    
    logger.info('Comprehensive validation requested', {
      context: 'VALIDATION_API.COMPREHENSIVE',
      user_id: req.user.id,
      time_range_hours: timeRangeHours
    });

    const validation = await TripLogsValidationService.runComprehensiveValidation(timeRangeHours);

    res.json({
      success: true,
      data: validation,
      metadata: {
        time_range_hours: timeRangeHours,
        generated_at: new Date().toISOString(),
        generated_by: req.user.username
      }
    });

  } catch (error) {
    logger.error('Error in comprehensive validation endpoint', {
      context: 'VALIDATION_API.COMPREHENSIVE_ERROR',
      error: error.message,
      user_id: req.user?.id
    });

    res.status(500).json({
      success: false,
      error: 'Validation Error',
      message: 'Failed to run comprehensive validation'
    });
  }
});

// @route   POST /api/validation/trip-logs/alerts
// @desc    Generate data quality alerts with custom thresholds
// @access  Private (Admin/Supervisor)
router.post('/trip-logs/alerts', auth, async (req, res) => {
  try {
    // Check user permissions
    if (!['admin', 'supervisor'].includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        error: 'Insufficient permissions',
        message: 'Only admin and supervisor users can generate alerts'
      });
    }

    const customThresholds = req.body.thresholds || {};
    
    logger.info('Data quality alerts generation requested', {
      context: 'VALIDATION_API.ALERTS',
      user_id: req.user.id,
      custom_thresholds: customThresholds
    });

    const alertsReport = await TripLogsValidationService.createDataQualityAlerts(customThresholds);

    res.json({
      success: true,
      data: alertsReport,
      metadata: {
        generated_at: new Date().toISOString(),
        generated_by: req.user.username,
        custom_thresholds_used: Object.keys(customThresholds).length > 0
      }
    });

  } catch (error) {
    logger.error('Error in data quality alerts endpoint', {
      context: 'VALIDATION_API.ALERTS_ERROR',
      error: error.message,
      user_id: req.user?.id
    });

    res.status(500).json({
      success: false,
      error: 'Validation Error',
      message: 'Failed to generate data quality alerts'
    });
  }
});

// @route   GET /api/validation/trip-logs/health-check
// @desc    Quick health check for trip_logs data quality
// @access  Private (Admin/Supervisor)
router.get('/trip-logs/health-check', auth, async (req, res) => {
  try {
    // Check user permissions
    if (!['admin', 'supervisor'].includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        error: 'Insufficient permissions',
        message: 'Only admin and supervisor users can access health checks'
      });
    }

    // Quick validation for last 1 hour
    const timeRangeHours = 1;
    
    logger.info('Trip logs health check requested', {
      context: 'VALIDATION_API.HEALTH_CHECK',
      user_id: req.user.id
    });

    const [driverValidation, notesValidation, sequenceValidation, missingFieldsValidation] = 
      await Promise.all([
        TripLogsValidationService.validateDriverInformationCompleteness(timeRangeHours),
        TripLogsValidationService.validateNotesFieldQuality(timeRangeHours),
        TripLogsValidationService.validateLocationSequenceAccuracy(timeRangeHours),
        TripLogsValidationService.detectMissingRequiredFields(timeRangeHours)
      ]);

    // Determine overall health status
    const driverRate = parseFloat(driverValidation.success_rate.replace('%', ''));
    const notesRate = parseFloat(notesValidation.success_rate.replace('%', ''));
    const sequenceRate = parseFloat(sequenceValidation.success_rate.replace('%', ''));
    const completenessRate = parseFloat(missingFieldsValidation.completeness_rate.replace('%', ''));

    let healthStatus = 'HEALTHY';
    if (driverRate < 95 || sequenceRate < 95 || completenessRate < 98) {
      healthStatus = 'CRITICAL';
    } else if (notesRate < 90) {
      healthStatus = 'WARNING';
    }

    const healthCheck = {
      status: healthStatus,
      timestamp: new Date().toISOString(),
      time_range_hours: timeRangeHours,
      total_trips_analyzed: driverValidation.total_trips,
      metrics: {
        driver_info_success_rate: driverValidation.success_rate,
        notes_quality_success_rate: notesValidation.success_rate,
        location_sequence_success_rate: sequenceValidation.success_rate,
        overall_completeness_rate: missingFieldsValidation.completeness_rate
      },
      issues: []
    };

    // Add specific issues
    if (driverRate < 95) {
      healthCheck.issues.push(`Driver information capture rate (${driverValidation.success_rate}) below 95%`);
    }
    if (notesRate < 90) {
      healthCheck.issues.push(`Notes quality rate (${notesValidation.success_rate}) below 90%`);
    }
    if (sequenceRate < 95) {
      healthCheck.issues.push(`Location sequence accuracy (${sequenceValidation.success_rate}) below 95%`);
    }
    if (completenessRate < 98) {
      healthCheck.issues.push(`Overall completeness rate (${missingFieldsValidation.completeness_rate}) below 98%`);
    }

    res.json({
      success: true,
      data: healthCheck,
      metadata: {
        generated_at: new Date().toISOString(),
        generated_by: req.user.username
      }
    });

  } catch (error) {
    logger.error('Error in trip logs health check endpoint', {
      context: 'VALIDATION_API.HEALTH_CHECK_ERROR',
      error: error.message,
      user_id: req.user?.id
    });

    res.status(500).json({
      success: false,
      error: 'Health Check Error',
      message: 'Failed to perform trip logs health check'
    });
  }
});

module.exports = router;