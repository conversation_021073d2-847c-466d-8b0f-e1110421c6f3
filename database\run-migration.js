const fs = require('fs');
const path = require('path');
const { Pool } = require('pg');
require('dotenv').config();

const MIGRATIONS_DIR = path.join(__dirname, 'migrations');

const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'hauling_qr_system',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD,
};

// Create migrations tracking table
const createMigrationsTable = `
  CREATE TABLE IF NOT EXISTS migrations (
    id SERIAL PRIMARY KEY,
    filename VARCHAR(255) UNIQUE NOT NULL,
    executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
  );
`;

async function getExecutedMigrations(client) {
  try {
    const result = await client.query('SELECT filename FROM migrations ORDER BY filename');
    return result.rows.map(row => row.filename);
  } catch (error) {
    // Table might not exist yet
    return [];
  }
}

async function markMigrationAsExecuted(client, filename) {
  try {
    await client.query(
      'INSERT INTO migrations (filename) VALUES ($1) ON CONFLICT (filename) DO NOTHING',
      [filename]
    );
  } catch (error) {
    console.warn(`⚠️  Warning: Could not mark migration as executed: ${filename}`);
    console.warn('Error:', error.message);
    // Don't throw - this is not critical enough to stop the migration
  }
}

async function runMigrations() {
  // Check if migrations directory exists
  if (!fs.existsSync(MIGRATIONS_DIR)) {
    console.error('❌ Migrations directory not found:', MIGRATIONS_DIR);
    process.exit(1);
  }

  // Get all migration files
  const migrationFiles = fs.readdirSync(MIGRATIONS_DIR)
    .filter(file => file.endsWith('.sql'))
    .sort(); // Sort to ensure proper order (001_, 002_, etc.)

  if (migrationFiles.length === 0) {
    console.log('📁 No migration files found in:', MIGRATIONS_DIR);
    return;
  }

  console.log('📋 Found migration files:', migrationFiles);

  // Check for duplicate migration numbers (safety check)
  const migrationNumbers = migrationFiles.map(f => f.split('_')[0]);
  const duplicates = migrationNumbers.filter((num, index) => migrationNumbers.indexOf(num) !== index);
  if (duplicates.length > 0) {
    console.error('❌ Duplicate migration numbers detected:', duplicates);
    console.error('Please ensure each migration has a unique number prefix');
    process.exit(1);
  }

  const pool = new Pool(dbConfig);
  const client = await pool.connect();

  try {
    // Create migrations tracking table
    await client.query(createMigrationsTable);
    console.log('✅ Migrations tracking table ready');

    // Get already executed migrations
    const executedMigrations = await getExecutedMigrations(client);
    console.log('📊 Already executed migrations:', executedMigrations);

    // Process each migration file
    let skippedCount = 0;
    let executedCount = 0;

    for (const filename of migrationFiles) {
      if (executedMigrations.includes(filename)) {
        console.log(`⏭️  Skipping already executed migration: ${filename}`);
        skippedCount++;
        continue;
      }

      const migrationPath = path.join(MIGRATIONS_DIR, filename);
      const sql = fs.readFileSync(migrationPath, 'utf8');

      console.log(`🚀 Running migration: ${filename}`);
      
      try {
        await client.query('BEGIN');
        await client.query(sql);
        await markMigrationAsExecuted(client, filename);
        await client.query('COMMIT');
        console.log(`✅ Migration completed successfully: ${filename}`);
        executedCount++;
      } catch (migrationError) {
        await client.query('ROLLBACK');
        console.error(`❌ Migration failed: ${filename}`);
        console.error('Error details:', migrationError.message);
        throw migrationError; // Stop execution on first failure
      }
    }

    // Summary report
    console.log('\n📊 Migration Summary:');
    console.log(`   ✅ Executed: ${executedCount} migrations`);
    console.log(`   ⏭️  Skipped: ${skippedCount} migrations (already executed)`);
    console.log(`   📁 Total: ${migrationFiles.length} migration files found`);

    if (executedCount > 0) {
      console.log('🎉 All new migrations completed successfully!');
    } else if (skippedCount > 0) {
      console.log('✅ All migrations are up to date - no new migrations to run');
    } else {
      console.log('📁 No migrations found to execute');
    }

  } catch (error) {
    console.error('❌ Migration process failed:', error.message);
    process.exit(1);
  } finally {
    client.release();
    await pool.end();
  }
}

// Support for running specific migration file (backward compatibility)
if (process.argv[2]) {
  const specificFile = process.argv[2];
  const migrationPath = path.isAbsolute(specificFile) 
    ? specificFile 
    : path.join(MIGRATIONS_DIR, specificFile);
  
  console.log(`🎯 Running specific migration: ${specificFile}`);
  
  async function runSpecificMigration() {
    if (!fs.existsSync(migrationPath)) {
      console.error('❌ Migration file not found:', migrationPath);
      process.exit(1);
    }

    const sql = fs.readFileSync(migrationPath, 'utf8');
    const pool = new Pool(dbConfig);
    const client = await pool.connect();

    try {
      console.log('🚀 Running migration:', migrationPath);
      await client.query('BEGIN');
      await client.query(sql);
      await client.query('COMMIT');
      console.log('✅ Migration completed successfully!');
    } catch (err) {
      await client.query('ROLLBACK');
      console.error('❌ Migration failed:', err.message);
      process.exit(1);
    } finally {
      client.release();
      await pool.end();
    }
  }

  runSpecificMigration();
} else {
  // Run all pending migrations
  runMigrations();
}
