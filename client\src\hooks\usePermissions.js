import { useState, useEffect, useContext } from 'react';
import { AuthContext } from '../context/AuthContext';
import { permissionsAPI } from '../services/api';
import { toast } from 'react-hot-toast';

// Define all available pages/routes in the system
export const AVAILABLE_PAGES = {
  DASHBOARD: 'dashboard',
  USERS: 'users',
  TRIPS: 'trips',
  ASSIGNMENTS: 'assignments',
  SHIFTS: 'shifts',
  ANALYTICS: 'analytics',
  SETTINGS: 'settings'
};

// Custom hook for permission checking
export const usePermissions = () => {
  const { user } = useContext(AuthContext);
  const [permissions, setPermissions] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Load user permissions
  useEffect(() => {
    const loadPermissions = async () => {
      if (!user || !user.role) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const response = await permissionsAPI.getByRole(user.role);
        
        if (response.data && response.data.success) {
          setPermissions(response.data.data.permissions || {});
        } else {
          setError('Failed to load permissions');
        }
      } catch (err) {
        console.error('Error loading permissions:', err);
        setError('Failed to load user permissions');
        
        // Don't show toast for permission errors to avoid spam
        if (err.response?.status !== 401 && err.response?.status !== 403) {
          toast.error('Failed to load user permissions');
        }
      } finally {
        setLoading(false);
      }
    };

    loadPermissions();
  }, [user]);

  // Check if user has permission for a specific page
  const hasPermission = (pageKey) => {
    if (!user || !user.role) {
      return false;
    }

    // Admin always has all permissions
    if (user.role === 'admin') {
      return true;
    }

    // Check specific permission
    return permissions[pageKey] === true;
  };

  // Check multiple permissions at once
  const hasAnyPermission = (pageKeys) => {
    return pageKeys.some(pageKey => hasPermission(pageKey));
  };

  // Check if user has all specified permissions
  const hasAllPermissions = (pageKeys) => {
    return pageKeys.every(pageKey => hasPermission(pageKey));
  };

  // Get all pages user has access to
  const getAccessiblePages = () => {
    if (!user || !user.role) {
      return [];
    }

    if (user.role === 'admin') {
      return Object.values(AVAILABLE_PAGES);
    }

    return Object.values(AVAILABLE_PAGES).filter(pageKey => permissions[pageKey] === true);
  };

  // Refresh permissions (useful after role changes)
  const refreshPermissions = async () => {
    if (!user || !user.role) {
      return;
    }

    try {
      const response = await permissionsAPI.getByRole(user.role);
      if (response.data && response.data.success) {
        setPermissions(response.data.data.permissions || {});
        setError(null);
      }
    } catch (err) {
      console.error('Error refreshing permissions:', err);
      setError('Failed to refresh permissions');
    }
  };

  return {
    permissions,
    loading,
    error,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    getAccessiblePages,
    refreshPermissions,
    userRole: user?.role
  };
};

// Higher-order component for route protection
export const withPermission = (WrappedComponent, requiredPermission) => {
  return function PermissionProtectedComponent(props) {
    const { hasPermission, loading } = usePermissions();

    if (loading) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-secondary-600">Loading...</span>
        </div>
      );
    }

    if (!hasPermission(requiredPermission)) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="text-6xl mb-4">🔒</div>
            <h1 className="text-2xl font-bold text-secondary-900 mb-2">Access Denied</h1>
            <p className="text-secondary-600 mb-4">
              You don't have permission to access this page.
            </p>
            <button
              onClick={() => window.history.back()}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              Go Back
            </button>
          </div>
        </div>
      );
    }

    return <WrappedComponent {...props} />;
  };
};

// Hook for checking specific permission (simpler version)
export const usePermission = (pageKey) => {
  const { hasPermission, loading } = usePermissions();
  return { hasPermission: hasPermission(pageKey), loading };
};

export default usePermissions;