import { useCallback, useRef, useEffect, useState } from 'react';

// Audio configuration constants
const AUDIO_CONFIG = {
  SUCCESS_NOTES: [
    { frequency: 523.25, duration: 0.1, delay: 0 },    // C5
    { frequency: 659.25, duration: 0.1, delay: 100 },  // E5
    { frequency: 783.99, duration: 0.2, delay: 200 }   // G5
  ],
  ERROR_NOTES: [
    { frequency: 349.23, duration: 0.2, delay: 0 },    // F4
    { frequency: 293.66, duration: 0.3, delay: 150 }   // D4
  ],
  VOLUME: 0.3,
  FADE_DURATION: 0.01
};

/**
 * Custom hook for providing audio feedback in the application
 * 
 * @returns {Object} Audio feedback functions
 * @returns {Function} playSuccessSound - Plays a pleasant ascending tone sequence
 * @returns {Function} playErrorSound - Plays a descending tone sequence for errors
 * @returns {Function} enableAudio - Enables audio after user interaction
 * @returns {boolean} audioEnabled - Whether audio is currently enabled
 * 
 * @example
 * const { playSuccessSound, playErrorSound, enableAudio } = useAudioFeedback();
 * 
 * // Enable audio after user interaction
 * await enableAudio();
 * 
 * // Play success sound after successful QR scan
 * playSuccessSound();
 * 
 * // Play error sound when scan fails
 * playErrorSound();
 */
export const useAudioFeedback = () => {
  const audioContextRef = useRef(null);
  const [audioEnabled, setAudioEnabled] = useState(false);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (audioContextRef.current) {
        audioContextRef.current.close();
        audioContextRef.current = null;
      }
    };
  }, []);

  const createAudioContext = useCallback(() => {
    if (!audioContextRef.current) {
      // Use proper type checking for browser compatibility
      const AudioContextClass = window.AudioContext || 
        window.webkitAudioContext || 
        window.mozAudioContext;
      
      if (AudioContextClass) {
        audioContextRef.current = new AudioContextClass();
      }
    }
    return audioContextRef.current;
  }, []);

  const enableAudio = useCallback(async () => {
    try {
      const audioContext = createAudioContext();
      if (audioContext && audioContext.state === 'suspended') {
        await audioContext.resume();
      }
      setAudioEnabled(true);
      return true;
    } catch (error) {
      console.warn('Could not enable audio:', error);
      return false;
    }
  }, [createAudioContext]);

  const playSequence = useCallback((notes) => {
    if (!audioEnabled) return false;

    try {
      const audioContext = createAudioContext();
      if (!audioContext) {
        throw new Error('AudioContext not available');
      }

      notes.forEach(({ frequency, duration, delay }) => {
        const startTime = audioContext.currentTime + (delay / 1000);
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        oscillator.frequency.value = frequency;
        oscillator.type = 'sine';

        gainNode.gain.setValueAtTime(AUDIO_CONFIG.VOLUME, startTime);
        gainNode.gain.exponentialRampToValueAtTime(AUDIO_CONFIG.FADE_DURATION, startTime + duration);

        oscillator.start(startTime);
        oscillator.stop(startTime + duration);
      });

      return true;
    } catch (error) {
      console.warn('Audio feedback not available:', error);
      return false;
    }
  }, [audioEnabled, createAudioContext]);

  const playSuccessSound = useCallback(() => {
    return playSequence(AUDIO_CONFIG.SUCCESS_NOTES);
  }, [playSequence]);

  const playErrorSound = useCallback(() => {
    return playSequence(AUDIO_CONFIG.ERROR_NOTES);
  }, [playSequence]);

  return {
    playSuccessSound,
    playErrorSound,
    enableAudio,
    audioEnabled
  };
};