import React, { useState, useEffect } from 'react';
import { getApiBaseUrl } from '../../utils/network-utils';
import toast from 'react-hot-toast';

const ShiftAssignmentIntegration = () => {
  const [integrationData, setIntegrationData] = useState(null);
  const [loading, setLoading] = useState(false);

  const loadIntegrationData = async () => {
    try {
      setLoading(true);
      
      // Get current shifts and assignments
      const apiUrl = getApiBaseUrl();
      const [shiftsResponse, assignmentsResponse] = await Promise.all([
        fetch(`${apiUrl}/shifts?status=active`, {
          headers: { 'Authorization': `Bearer ${localStorage.getItem('hauling_token')}` }
        }),
        fetch(`${apiUrl}/assignments`, {
          headers: { 'Authorization': `Bearer ${localStorage.getItem('hauling_token')}` }
        })
      ]);

      if (shiftsResponse.ok && assignmentsResponse.ok) {
        const shiftsData = await shiftsResponse.json();
        const assignmentsData = await assignmentsResponse.json();
        
        setIntegrationData({
          activeShifts: shiftsData.data || [],
          assignments: assignmentsData.data || []
        });
      }
    } catch (error) {
      console.error('Error loading integration data:', error);
      toast.error('Failed to load integration data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadIntegrationData();
  }, []);

  const getShiftAssignments = (shiftId) => {
    if (!integrationData) return [];
    return integrationData.assignments.filter(a => a.shift_id === shiftId);
  };

  const getRegularAssignments = () => {
    if (!integrationData) return [];
    return integrationData.assignments.filter(a => !a.shift_id || !a.is_shift_assignment);
  };

  return (
    <div className="min-h-screen bg-secondary-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-secondary-900">
            🔗 Shift & Assignment Integration
          </h1>
          <p className="mt-2 text-secondary-600">
            Understanding how multi-driver shifts integrate with assignment management
          </p>
        </div>

        {/* Integration Overview */}
        <div className="bg-white rounded-lg shadow border border-secondary-200 p-6 mb-6">
          <h2 className="text-xl font-semibold text-secondary-900 mb-4">
            How Shifts Affect Assignment Management
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Before Shifts */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="text-lg font-medium text-gray-900 mb-3">
                📋 Traditional Assignment Model
              </h3>
              <div className="space-y-2 text-sm text-gray-700">
                <div className="flex items-center">
                  <span className="w-2 h-2 bg-gray-400 rounded-full mr-2"></span>
                  One driver per truck per assignment
                </div>
                <div className="flex items-center">
                  <span className="w-2 h-2 bg-gray-400 rounded-full mr-2"></span>
                  Assignment tied directly to truck + driver
                </div>
                <div className="flex items-center">
                  <span className="w-2 h-2 bg-gray-400 rounded-full mr-2"></span>
                  No time-based driver changes
                </div>
                <div className="flex items-center">
                  <span className="w-2 h-2 bg-gray-400 rounded-full mr-2"></span>
                  Manual driver reassignment required
                </div>
              </div>
            </div>

            {/* After Shifts */}
            <div className="bg-blue-50 rounded-lg p-4">
              <h3 className="text-lg font-medium text-blue-900 mb-3">
                🔄 Multi-Driver Shift Model
              </h3>
              <div className="space-y-2 text-sm text-blue-700">
                <div className="flex items-center">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                  Multiple drivers per truck with time slots
                </div>
                <div className="flex items-center">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                  Shift-aware assignments auto-created
                </div>
                <div className="flex items-center">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                  Automatic driver handovers
                </div>
                <div className="flex items-center">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                  Seamless trip continuity
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Integration Flow */}
        <div className="bg-white rounded-lg shadow border border-secondary-200 p-6 mb-6">
          <h2 className="text-xl font-semibold text-secondary-900 mb-4">
            Integration Workflow
          </h2>
          
          <div className="space-y-4">
            <div className="flex items-start">
              <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-semibold text-sm mr-4">
                1
              </div>
              <div>
                <h3 className="font-medium text-secondary-900">Shift Creation</h3>
                <p className="text-secondary-600 text-sm">
                  When you create a shift, it defines when a specific driver will operate a specific truck.
                  No assignments are created yet - just the schedule.
                </p>
              </div>
            </div>

            <div className="flex items-start">
              <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-semibold text-sm mr-4">
                2
              </div>
              <div>
                <h3 className="font-medium text-secondary-900">Shift Activation</h3>
                <p className="text-secondary-600 text-sm">
                  When a shift becomes active (automatically at start time or manually), the system can 
                  create shift-aware assignments that link to the active shift.
                </p>
              </div>
            </div>

            <div className="flex items-start">
              <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-semibold text-sm mr-4">
                3
              </div>
              <div>
                <h3 className="font-medium text-secondary-900">Assignment Integration</h3>
                <p className="text-secondary-600 text-sm">
                  The scanner automatically detects the current active shift and uses the correct driver 
                  for trip creation. Existing assignments continue to work normally.
                </p>
              </div>
            </div>

            <div className="flex items-start">
              <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-semibold text-sm mr-4">
                4
              </div>
              <div>
                <h3 className="font-medium text-secondary-900">Seamless Handovers</h3>
                <p className="text-secondary-600 text-sm">
                  When shifts change during an active trip, the system creates a handover record and 
                  the new driver continues the trip without disruption to the 4-phase workflow.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Current Status */}
        {loading ? (
          <div className="bg-white rounded-lg shadow border border-secondary-200 p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-secondary-500">Loading integration status...</p>
          </div>
        ) : integrationData ? (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Active Shifts */}
            <div className="bg-white rounded-lg shadow border border-secondary-200 p-6">
              <h3 className="text-lg font-medium text-secondary-900 mb-4">
                🔄 Active Shifts ({integrationData.activeShifts.length})
              </h3>
              
              {integrationData.activeShifts.length === 0 ? (
                <p className="text-secondary-500 text-sm">No active shifts currently</p>
              ) : (
                <div className="space-y-3">
                  {integrationData.activeShifts.map((shift) => (
                    <div key={shift.id} className="border border-secondary-200 rounded-lg p-3">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <div className="font-medium text-secondary-900">
                            {shift.truck_number} - {shift.driver_name}
                          </div>
                          <div className="text-sm text-secondary-500">
                            {shift.shift_type} shift • {shift.start_time} - {shift.end_time}
                          </div>
                        </div>
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          Active
                        </span>
                      </div>
                      
                      {/* Shift Assignments */}
                      <div className="mt-2">
                        <div className="text-xs text-secondary-500 mb-1">
                          Shift Assignments: {getShiftAssignments(shift.id).length}
                        </div>
                        {getShiftAssignments(shift.id).map((assignment) => (
                          <div key={assignment.id} className="text-xs text-secondary-600 bg-blue-50 rounded px-2 py-1 mb-1">
                            {assignment.assignment_code} • {assignment.loading_location_name} → {assignment.unloading_location_name}
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Regular Assignments */}
            <div className="bg-white rounded-lg shadow border border-secondary-200 p-6">
              <h3 className="text-lg font-medium text-secondary-900 mb-4">
                📋 Regular Assignments ({getRegularAssignments().length})
              </h3>
              
              {getRegularAssignments().length === 0 ? (
                <p className="text-secondary-500 text-sm">No regular assignments currently</p>
              ) : (
                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {getRegularAssignments().slice(0, 10).map((assignment) => (
                    <div key={assignment.id} className="border border-secondary-200 rounded-lg p-3">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <div className="font-medium text-secondary-900">
                            {assignment.assignment_code}
                          </div>
                          <div className="text-sm text-secondary-500">
                            {assignment.truck_number} - {assignment.driver_name}
                          </div>
                          <div className="text-xs text-secondary-400">
                            {assignment.loading_location_name} → {assignment.unloading_location_name}
                          </div>
                        </div>
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                          assignment.status === 'assigned' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'
                        }`}>
                          {assignment.status}
                        </span>
                      </div>
                    </div>
                  ))}
                  {getRegularAssignments().length > 10 && (
                    <p className="text-xs text-secondary-500 text-center">
                      ... and {getRegularAssignments().length - 10} more assignments
                    </p>
                  )}
                </div>
              )}
            </div>
          </div>
        ) : null}

        {/* Key Benefits */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-6 mt-6">
          <h3 className="text-lg font-medium text-green-800 mb-4">
            ✅ Key Benefits of Shift Integration
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium text-green-800 mb-2">For Operations</h4>
              <ul className="text-sm text-green-700 space-y-1">
                <li>• Automatic driver handovers</li>
                <li>• No trip interruptions</li>
                <li>• Real-time shift tracking</li>
                <li>• Seamless 24/7 operations</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-green-800 mb-2">For Management</h4>
              <ul className="text-sm text-green-700 space-y-1">
                <li>• Driver performance by shift</li>
                <li>• Shift-based analytics</li>
                <li>• Automated scheduling</li>
                <li>• Audit trail for handovers</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ShiftAssignmentIntegration;
