{"mcpServers": {"fetch-mcp": {"command": "node", "args": ["C:\\Users\\<USER>\\Documents\\Cline\\MCP\\fetch-mcp\\dist\\index.js"], "disabled": false, "autoApprove": ["fetch_html", "fetch_markdown", "fetch_txt", "fetch_json"]}, "memory": {"command": "node", "args": ["C:\\Users\\<USER>\\Documents\\Cline\\MCP\\memory-server\\node_modules\\@modelcontextprotocol\\server-memory\\dist\\index.js"], "disabled": false, "autoApprove": ["create_entities", "create_relations", "add_observations", "delete_entities", "delete_observations", "delete_relations", "read_graph", "search_nodes", "open_nodes"]}, "filesystem": {"command": "node", "args": ["C:\\Users\\<USER>\\Documents\\Cline\\MCP\\filesystem-server\\node_modules\\@modelcontextprotocol\\server-filesystem\\dist\\index.js", "C:\\Users\\<USER>\\Documents\\Cline\\MCP\\filesystem-server", "C:\\Users\\<USER>\\AppData", "C:\\Users\\<USER>\\Documents", "C:\\Users\\<USER>"], "disabled": false, "autoApprove": ["read_file", "read_multiple_files", "write_file", "edit_file", "list_directory", "directory_tree", "move_file", "search_files", "get_file_info", "list_allowed_directories", "create_directory"]}, "sequential-thinking-server": {"command": "node", "args": ["C:\\Users\\<USER>\\Documents\\Cline\\MCP\\sequential-thinking-server\\build\\index.js"], "disabled": false, "autoApprove": ["create_note"]}, "brave-search": {"command": "node", "args": ["C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\@modelcontextprotocol\\server-brave-search\\dist\\index.js"], "env": {"BRAVE_API_KEY": "BSAM-A26MezLPwUSYsBxfyGkkD634zW"}, "disabled": false, "autoApprove": ["brave_web_search", "brave_local_search"]}, "context7-mcp": {"command": "cmd", "args": ["/c", "npx", "-y", "@upstash/context7-mcp"], "disabled": false, "autoApprove": []}, "postgres": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-postgres", "postgresql://postgres:PostgreSQLPassword@localhost:5432/hauling_qr_system"], "disabled": false, "autoApprove": ["query", "query"]}, "desktop-commander": {"command": "npx", "args": ["-y", "@wonderwhy-er/desktop-commander"], "disabled": false, "autoApprove": ["write_file", "start_process", "read_process_output", "move_file", "list_directory", "get_config", "search_files", "read_file"]}}}