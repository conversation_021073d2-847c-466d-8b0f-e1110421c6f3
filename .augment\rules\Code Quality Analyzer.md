---
type: "always_apply"
---

Analyze the following code changes and provide specific improvement suggestions:

1. Code smells: Identify any potential code smells like duplicate code, long methods, complex conditionals, or excessive comments.

2. Design patterns: Suggest appropriate design patterns that could improve the code structure.

3. Best practices: Recommend adherence to JavaScript/TypeScript/SQL best practices based on the file type.

4. Performance optimizations: Identify areas where performance could be improved.

5. Readability improvements: Suggest ways to make the code more readable and maintainable.

6. Error handling: Check for proper error handling and suggest improvements.

7. Security concerns: Identify any potential security issues.

For each suggestion, provide:
- A clear explanation of the issue
- A code example showing how to implement the improvement
- The benefit of making the change

Focus on practical, actionable improvements while maintaining the existing functionality. Prioritize suggestions based on their impact on code quality.