# Non-Interactive Deployment Mode Implementation

## Overview

The non-interactive deployment mode allows the Hauling QR Trip Management System to be deployed without user intervention, making it suitable for CI/CD pipelines and automated deployments. This document outlines the implementation details for this feature.

## Configuration File Format

The deployment script will support both YAML and JSON configuration files with the following structure:

### YAML Format Example
```yaml
# Hauling QR Trip Management System Deployment Configuration
domain: truckhaul.top
ssl:
  mode: cloudflare  # Options: none, letsencrypt, cloudflare, custom
  email: <EMAIL>  # Required for Let's Encrypt
  custom_cert_path: /path/to/cert.crt  # Required for custom mode
  custom_key_path: /path/to/key.key    # Required for custom mode
database:
  password: strong-db-password  # Optional, will generate if not provided
  host: localhost
  port: 5432
  name: hauling_qr_system
admin:
  username: admin
  password: strong-admin-password  # Optional, will generate if not provided
  email: <EMAIL>
repository:
  url: https://github.com/your-org/hauling-qr-trip-system.git
  branch: main
environment: production  # Options: production, staging, development
features:
  monitoring: true
  backups: true
  backup_retention_days: 7
security:
  jwt_secret: your-jwt-secret  # Optional, will generate if not provided
  firewall_enabled: true
  fail2ban_enabled: true
```

### JSON Format Example
```json
{
  "domain": "truckhaul.top",
  "ssl": {
    "mode": "cloudflare",
    "email": "<EMAIL>",
    "custom_cert_path": "/path/to/cert.crt",
    "custom_key_path": "/path/to/key.key"
  },
  "database": {
    "password": "strong-db-password",
    "host": "localhost",
    "port": 5432,
    "name": "hauling_qr_system"
  },
  "admin": {
    "username": "admin",
    "password": "strong-admin-password",
    "email": "<EMAIL>"
  },
  "repository": {
    "url": "https://github.com/your-org/hauling-qr-trip-system.git",
    "branch": "main"
  },
  "environment": "production",
  "features": {
    "monitoring": true,
    "backups": true,
    "backup_retention_days": 7
  },
  "security": {
    "jwt_secret": "your-jwt-secret",
    "firewall_enabled": true,
    "fail2ban_enabled": true
  }
}
```

## Configuration Validation

The deployment script will validate the configuration file before proceeding with the deployment:

1. **Required Parameters**: Check that all required parameters are present
2. **Parameter Types**: Validate that parameters have the correct data types
3. **Enum Values**: Ensure that enum values (e.g., SSL mode, environment) are valid
4. **File Existence**: Verify that any referenced files (e.g., custom SSL certificates) exist
5. **Password Strength**: Check that provided passwords meet minimum security requirements

## Command-Line Arguments

Command-line arguments will take precedence over configuration file values, allowing for flexible deployment scenarios:

```bash
./deploy-hauling-qr-ubuntu.sh --config config.yaml --domain custom-domain.com --env staging
```

In this example, the domain and environment values from the command line will override those in the configuration file.

## CI/CD Integration

### Structured Output

The deployment script will support structured output in JSON format for easy parsing by CI/CD systems:

```json
{
  "status": "success",
  "timestamp": "2025-07-19T14:30:00Z",
  "duration_seconds": 180,
  "components": {
    "nginx": {"status": "installed", "version": "1.18.0"},
    "postgresql": {"status": "installed", "version": "14.5"},
    "nodejs": {"status": "installed", "version": "18.12.1"},
    "application": {"status": "deployed", "version": "1.0.0"}
  },
  "urls": {
    "frontend": "https://truckhaul.top",
    "api": "https://truckhaul.top/api"
  },
  "warnings": [
    {"component": "ssl", "message": "Using self-signed certificate with Cloudflare"}
  ]
}
```

### Exit Codes

The script will use standardized exit codes to indicate different failure scenarios:

- `0`: Successful deployment
- `1`: General error
- `2`: Configuration error
- `3`: System requirements not met
- `4`: Network/connectivity error
- `5`: Repository error
- `6`: Build error
- `7`: Database error
- `8`: Web server error
- `9`: Application error

### Progress Indicators

For CI/CD systems that support it, the script will output progress indicators:

```
[20%] System preparation complete
[40%] Database setup complete
[60%] Application deployed
[80%] Web server configured
[100%] Deployment complete
```

## Implementation Tasks

1. Create configuration file parser for YAML and JSON formats
2. Implement comprehensive validation for all configuration parameters
3. Add command-line argument handling with precedence over config file
4. Implement structured JSON output format
5. Add quiet mode with minimal console output
6. Create progress indicators for CI/CD systems
7. Implement detailed exit codes for different failure scenarios
8. Add dry-run mode to validate configuration without making changes