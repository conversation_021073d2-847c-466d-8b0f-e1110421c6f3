-- Migration 027: Add performance indexes for trips query
-- This addresses the 10-second timeout issue in TripMonitoring

-- Index for trip_logs main queries
CREATE INDEX IF NOT EXISTS idx_trip_logs_assignment_status_created 
ON trip_logs (assignment_id, status, created_at DESC);

-- Index for trip_logs search operations
CREATE INDEX IF NOT EXISTS idx_trip_logs_search_fields 
ON trip_logs (trip_number, status, created_at DESC);

-- Index for assignments with truck relationship
CREATE INDEX IF NOT EXISTS idx_assignments_truck_driver 
ON assignments (truck_id, driver_id, assigned_date DESC);

-- Index for driver_shifts active lookup (most expensive join)
CREATE INDEX IF NOT EXISTS idx_driver_shifts_active_truck_date 
ON driver_shifts (truck_id, status, start_date, end_date) 
WHERE status = 'active';

-- Index for locations lookup
CREATE INDEX IF NOT EXISTS idx_locations_id_name 
ON locations (id, name);

-- Composite index for trip filtering
CREATE INDEX IF NOT EXISTS idx_trip_logs_composite_filter 
ON trip_logs (status, is_exception, created_at DESC, assignment_id);

-- Index for actual location lookups
CREATE INDEX IF NOT EXISTS idx_trip_logs_actual_locations 
ON trip_logs (actual_loading_location_id, actual_unloading_location_id);