import React, { useState, useEffect } from 'react';
import { getApiBaseUrl } from '../../utils/network-utils';
import toast from 'react-hot-toast';

const TripNumberManager = () => {
  const [statistics, setStatistics] = useState(null);
  const [loading, setLoading] = useState(false);
  const [fixing, setFixing] = useState(false);

  const loadStatistics = async () => {
    try {
      setLoading(true);
      
      const apiUrl = getApiBaseUrl();
      const response = await fetch(`${apiUrl}/trips/trip-numbers/statistics`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('hauling_token')}`
        }
      });

      if (response.ok) {
        const result = await response.json();
        setStatistics(result.data);
      } else {
        throw new Error('Failed to load statistics');
      }
    } catch (error) {
      console.error('Error loading statistics:', error);
      toast.error('Failed to load trip number statistics');
    } finally {
      setLoading(false);
    }
  };

  const fixDuplicates = async () => {
    try {
      setFixing(true);
      
      const apiUrl = getApiBaseUrl();
      const response = await fetch(`${apiUrl}/trips/trip-numbers/fix-duplicates`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('hauling_token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const result = await response.json();
        toast.success(result.message);
        
        // Reload statistics
        await loadStatistics();
      } else {
        throw new Error('Failed to fix duplicates');
      }
    } catch (error) {
      console.error('Error fixing duplicates:', error);
      toast.error('Failed to fix trip number duplicates');
    } finally {
      setFixing(false);
    }
  };

  useEffect(() => {
    loadStatistics();
  }, []);

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-8">
        <h2 className="text-2xl font-bold text-secondary-900">
          🔢 Trip Number Manager
        </h2>
        <p className="mt-2 text-secondary-600">
          Manage and fix trip number duplications to ensure globally unique trip numbers
        </p>
      </div>

      {/* Statistics Card */}
      <div className="bg-white rounded-lg shadow border border-secondary-200 p-6 mb-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-xl font-semibold text-secondary-900">
            Trip Number Statistics
          </h3>
          <button
            onClick={loadStatistics}
            disabled={loading}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50"
          >
            {loading ? 'Loading...' : 'Refresh'}
          </button>
        </div>

        {loading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-secondary-500">Loading statistics...</p>
          </div>
        ) : statistics ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Total Trips */}
            <div className="bg-blue-50 rounded-lg p-4">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-blue-600 font-semibold text-sm">📊</span>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-blue-900">Total Trips</p>
                  <p className="text-2xl font-bold text-blue-600">{statistics.totalTrips}</p>
                </div>
              </div>
            </div>

            {/* Unique Trip Numbers */}
            <div className="bg-green-50 rounded-lg p-4">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                    <span className="text-green-600 font-semibold text-sm">✅</span>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-green-900">Unique Numbers</p>
                  <p className="text-2xl font-bold text-green-600">{statistics.uniqueTripNumbers}</p>
                </div>
              </div>
            </div>

            {/* Duplicates */}
            <div className={`${statistics.duplicates > 0 ? 'bg-red-50' : 'bg-green-50'} rounded-lg p-4`}>
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className={`w-8 h-8 ${statistics.duplicates > 0 ? 'bg-red-100' : 'bg-green-100'} rounded-full flex items-center justify-center`}>
                    <span className={`${statistics.duplicates > 0 ? 'text-red-600' : 'text-green-600'} font-semibold text-sm`}>
                      {statistics.duplicates > 0 ? '⚠️' : '✅'}
                    </span>
                  </div>
                </div>
                <div className="ml-4">
                  <p className={`text-sm font-medium ${statistics.duplicates > 0 ? 'text-red-900' : 'text-green-900'}`}>
                    Duplicates
                  </p>
                  <p className={`text-2xl font-bold ${statistics.duplicates > 0 ? 'text-red-600' : 'text-green-600'}`}>
                    {statistics.duplicates}
                  </p>
                </div>
              </div>
            </div>

            {/* Range */}
            <div className="bg-purple-50 rounded-lg p-4 md:col-span-2 lg:col-span-1">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                    <span className="text-purple-600 font-semibold text-sm">📈</span>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-purple-900">Number Range</p>
                  <p className="text-lg font-bold text-purple-600">
                    {statistics.minTripNumber} - {statistics.maxTripNumber}
                  </p>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <p className="text-secondary-500 text-center py-4">No statistics available</p>
        )}
      </div>

      {/* Action Card */}
      {statistics && statistics.duplicates > 0 && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 mb-6">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                <span className="text-red-600 font-bold">⚠️</span>
              </div>
            </div>
            <div className="ml-4 flex-1">
              <h4 className="text-lg font-medium text-red-900 mb-2">
                Duplicate Trip Numbers Detected
              </h4>
              <p className="text-red-700 mb-4">
                Found {statistics.duplicates} duplicate trip numbers. This can cause confusion in trip tracking and reporting.
                Click the button below to fix all duplicates by making trip numbers globally unique.
              </p>
              <button
                onClick={fixDuplicates}
                disabled={fixing}
                className="bg-red-600 text-white px-6 py-2 rounded-md hover:bg-red-700 disabled:opacity-50 flex items-center"
              >
                {fixing ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Fixing Duplicates...
                  </>
                ) : (
                  <>
                    🔧 Fix All Duplicates
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Success Card */}
      {statistics && statistics.duplicates === 0 && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                <span className="text-green-600 font-bold">✅</span>
              </div>
            </div>
            <div className="ml-4">
              <h4 className="text-lg font-medium text-green-900 mb-2">
                All Trip Numbers Are Unique
              </h4>
              <p className="text-green-700">
                Great! All trip numbers in the system are globally unique. No duplicates found.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Info Card */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h4 className="text-lg font-medium text-blue-900 mb-2">
          📋 How Global Unique Trip Numbers Work
        </h4>
        <div className="text-blue-700 space-y-2">
          <p>• <strong>Before:</strong> Trip numbers were unique per assignment (Assignment A: Trip 1, 2, 3; Assignment B: Trip 1, 2, 3)</p>
          <p>• <strong>After:</strong> Trip numbers are globally unique across the entire system (Trip 1001, 1002, 1003, 1004, etc.)</p>
          <p>• <strong>Benefit:</strong> No confusion when viewing Trip Monitoring - each Trip# is completely unique</p>
          <p>• <strong>Implementation:</strong> New trips use global MAX(trip_number) + 1 for guaranteed uniqueness</p>
        </div>
      </div>
    </div>
  );
};

export default TripNumberManager;
