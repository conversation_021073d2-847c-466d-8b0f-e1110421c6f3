# Deployment Script Maintenance Guide

## Overview

This document tracks the maintenance requirements for the Ubuntu deployment script to ensure it stays aligned with the current codebase structure and dependencies.

## Current Status

**Last Updated**: January 2025  
**Script Version**: 2.0.1  
**Codebase Alignment**: ⚠️ Requires Updates

## Key Maintenance Areas

### 1. Package Dependencies Synchronization

The deployment script must be updated whenever package.json files change:

- **Root package.json**: System-level dependencies
- **server/package.json**: Backend API dependencies  
- **client/package.json**: Frontend React dependencies

**Current Misalignments**:
- Missing newer dependencies like `@supabase/mcp-server-postgrest`, `joi`, `multer`, `sharp`, `uuid`
- Outdated version specifications for existing packages
- Missing client-side QR scanning libraries

### 2. Database Schema Updates

**Current Database**: `hauling_qr_system` (consolidated init.sql v4.0)
**Migration System**: Custom Node.js runner with 63+ migrations

**Required Updates**:
- Ensure deployment uses current `database/init.sql`
- Update database name references from old naming conventions
- Include proper migration runner setup

### 3. Environment Configuration

**Unified Configuration System**: Single `.env` file with auto-distribution

**Key Variables to Verify**:
```bash
# Core Configuration
NODE_ENV=production
DB_NAME=hauling_qr_system
JWT_SECRET=hauling_qr_jwt_secret_2025_secure_key_for_production

# Domain Configuration
DOMAIN_NAME=truckhaul.top
SSL_MODE=cloudflare

# Auto-generated Client Variables
REACT_APP_API_URL=https://truckhaul.top/api
REACT_APP_WS_URL=wss://truckhaul.top/ws
```

### 4. Startup Commands

**Current Correct Commands**:
- Server: `node server.js` (from server/ directory)
- Client Build: `npm run build` (from client/ directory)
- PM2 Target: `server/server.js`

### 5. Directory Structure

**Required Directories**:
```
/opt/hauling-qr-system/
├── client/build/              # React production build
├── server/                    # Node.js server
├── server/logs/               # Application logs
├── uploads/                   # File upload storage
├── ssl/dev/                   # Development certificates
├── ssl/production/            # Production certificates
└── .env                       # Environment configuration
```

## Maintenance Checklist

### Before Each Deployment

- [ ] Verify package.json dependencies match deployment script
- [ ] Check database schema version and migration requirements
- [ ] Validate environment variable completeness
- [ ] Test startup commands in development environment
- [ ] Verify SSL certificate generation process

### After Codebase Changes

- [ ] Update deployment script dependencies
- [ ] Test deployment in staging environment
- [ ] Update deployment documentation
- [ ] Validate health check endpoints
- [ ] Test rollback functionality

### Monthly Maintenance

- [ ] Review security updates for dependencies
- [ ] Check SSL certificate expiration
- [ ] Validate backup and monitoring systems
- [ ] Update deployment script version
- [ ] Test complete deployment process

## Related Documentation

- [Deployment Script Updates](../deploy-hauling-qr-ubuntu/DEPLOYMENT_SCRIPT_UPDATES.md) - Detailed update requirements
- [Deployment Requirements](../.kiro/specs/deployment-script-updates/requirements.md) - Formal requirements document
- [Deployment Tasks](../.kiro/specs/auto-deployment-ubuntu/tasks.md) - Implementation task list
- [Ubuntu Deployment README](../deploy-hauling-qr-ubuntu/README.md) - Deployment directory overview

## Update Process

1. **Identify Changes**: Compare current codebase with deployment script
2. **Update Script**: Modify `deploy-hauling-qr-ubuntu-fixed.sh`
3. **Test Deployment**: Validate in staging environment
4. **Update Documentation**: Sync all related documentation
5. **Version Bump**: Increment script version number
6. **Deploy**: Release updated deployment resources

## Contact

For deployment script maintenance questions, refer to the project documentation or create an issue in the project repository.