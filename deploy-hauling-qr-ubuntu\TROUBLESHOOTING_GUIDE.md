# Troubleshooting Guide - Hauling QR Trip System Deployment

## 🔧 Comprehensive Troubleshooting for Ubuntu VPS Deployment

This guide covers common issues and solutions for the Hauling QR Trip System deployment with automatic IP detection.

---

## 🚨 Emergency Quick Fixes

### Immediate Actions for Failed Deployment
```bash
# Check deployment log
tail -f /var/log/hauling-deployment/auto-deploy-*.log

# Check all services status
systemctl status nginx postgresql
pm2 status

# Restart all services
systemctl restart nginx postgresql
pm2 restart all

# Check firewall
ufw status
```

---
## Complete Clean Restart Process for PM2

# 1. Stop PM2
pm2 stop hauling-qr-server

# 2. Kill all Node.js processes
sudo pkill -f "node.*server"

# 3. Verify port 5000 is free
sudo lsof -i :5000
# Should show nothing

# 4. Start PM2 fresh
pm2 start server/server.js --name hauling-qr-server

# 5. Check status
pm2 status

# 6. Test application
curl http://localhost:5000/health



---

## 🔄 Complete Service Restart Procedures

### Full System Restart (Recommended for Most Issues)
Use this when you need to restart everything cleanly:

```bash
# 1. Stop all services in proper order
echo "Stopping PM2 applications..."
pm2 stop all

echo "Stopping Nginx..."
sudo systemctl stop nginx

echo "Stopping PostgreSQL..."
sudo systemctl stop postgresql

# 2. Wait a moment for clean shutdown
sleep 5

# 3. Start services in proper order
echo "Starting PostgreSQL..."
sudo systemctl start postgresql
sudo systemctl status postgresql --no-pager

echo "Starting Nginx..."
sudo systemctl start nginx
sudo systemctl status nginx --no-pager

echo "Starting PM2 applications..."
pm2 start all
pm2 status

# 4. Verify everything is working
echo "Testing services..."
curl -I http://localhost/
curl -I http://localhost:5000/health
```

### Quick Service Restart (For Minor Issues)
Use this for faster restarts when services are mostly working:

```bash
# Restart all services simultaneously
sudo systemctl restart nginx postgresql && pm2 restart all

# Check status
echo "Service Status:"
sudo systemctl is-active nginx postgresql
pm2 status
```

### Individual Service Restart Commands

#### PostgreSQL Database
```bash
# Restart PostgreSQL
sudo systemctl restart postgresql

# Check status and logs
sudo systemctl status postgresql
sudo tail -f /var/log/postgresql/postgresql-*.log

# Test database connection
sudo -u postgres psql -d hauling_qr_system -c "SELECT version();"
```

#### PM2 Application Server
```bash
# Restart PM2 application
pm2 restart hauling-qr-server

# Or restart all PM2 processes
pm2 restart all

# Check status and logs
pm2 status
pm2 logs hauling-qr-server --lines 20

# Test application health
curl http://localhost:5000/health
```

#### Nginx Web Server
```bash
# Restart Nginx
sudo systemctl restart nginx

# Check configuration first (recommended)
sudo nginx -t && sudo systemctl restart nginx

# Check status and logs
sudo systemctl status nginx
sudo tail -f /var/log/nginx/error.log

# Test web server
curl -I http://localhost/
```

### Service Restart with Health Checks

#### Complete Restart with Verification
```bash
#!/bin/bash
# Complete service restart with health checks

echo "🔄 Starting complete service restart..."

# Function to check service health
check_service() {
    local service=$1
    local check_cmd=$2
    
    echo "Checking $service..."
    if eval $check_cmd > /dev/null 2>&1; then
        echo "✅ $service is healthy"
        return 0
    else
        echo "❌ $service is not responding"
        return 1
    fi
}

# Stop services
echo "🛑 Stopping services..."
pm2 stop all
sudo systemctl stop nginx
sudo systemctl stop postgresql

sleep 3

# Start PostgreSQL
echo "🗄️ Starting PostgreSQL..."
sudo systemctl start postgresql
sleep 2
check_service "PostgreSQL" "sudo -u postgres psql -d hauling_qr_system -c 'SELECT 1;'"

# Start Application
echo "🚀 Starting Application..."
pm2 start all
sleep 5
check_service "Application" "curl -f http://localhost:5000/health"

# Start Nginx
echo "🌐 Starting Nginx..."
sudo systemctl start nginx
sleep 2
check_service "Nginx" "curl -f -I http://localhost/"

echo "🎉 Service restart complete!"
pm2 status
sudo systemctl status nginx postgresql --no-pager
```

### Emergency Recovery Restart
Use this when normal restart procedures fail:

```bash
# Force kill all processes
echo "🚨 Emergency restart - force killing processes..."

# Kill PM2 processes
pm2 kill
pkill -f "node.*server"

# Force restart system services
sudo systemctl kill nginx
sudo systemctl kill postgresql
sleep 2
sudo systemctl start postgresql
sudo systemctl start nginx

# Restart application from scratch
cd /var/www/hauling-qr-system
pm2 start ecosystem.config.js

# Verify recovery
echo "Checking recovery status..."
sleep 5
curl -I http://localhost/ && echo "✅ Web server OK"
curl -I http://localhost:5000/health && echo "✅ Application OK"
sudo -u postgres psql -d hauling_qr_system -c "SELECT 1;" && echo "✅ Database OK"
```

### Automated Restart Script
Create a reusable restart script:

```bash
# Create restart script
sudo tee /usr/local/bin/restart-hauling-system.sh > /dev/null << 'EOF'
#!/bin/bash
# Hauling QR System - Complete Restart Script

set -e

echo "🔄 Restarting Hauling QR Trip System..."

# Change to application directory
cd /var/www/hauling-qr-system

# Stop services
echo "Stopping services..."
pm2 stop all 2>/dev/null || true
sudo systemctl stop nginx
sudo systemctl stop postgresql

# Wait for clean shutdown
sleep 3

# Start services in order
echo "Starting PostgreSQL..."
sudo systemctl start postgresql

echo "Starting Nginx..."
sudo systemctl start nginx

echo "Starting Application..."
pm2 start ecosystem.config.js 2>/dev/null || pm2 start all

# Health checks
sleep 5
echo "Running health checks..."

# Check PostgreSQL
if sudo -u postgres psql -d hauling_qr_system -c "SELECT 1;" > /dev/null 2>&1; then
    echo "✅ PostgreSQL: OK"
else
    echo "❌ PostgreSQL: FAILED"
fi

# Check Application
if curl -f http://localhost:5000/health > /dev/null 2>&1; then
    echo "✅ Application: OK"
else
    echo "❌ Application: FAILED"
fi

# Check Nginx
if curl -f -I http://localhost/ > /dev/null 2>&1; then
    echo "✅ Nginx: OK"
else
    echo "❌ Nginx: FAILED"
fi

echo "🎉 Restart complete!"
pm2 status
EOF

# Make script executable
sudo chmod +x /usr/local/bin/restart-hauling-system.sh

# Usage: sudo restart-hauling-system.sh
```

### Service Restart Troubleshooting

#### If PostgreSQL Won't Start
```bash
# Check PostgreSQL logs
sudo tail -f /var/log/postgresql/postgresql-*.log

# Check disk space
df -h /var/lib/postgresql

# Check PostgreSQL configuration
sudo -u postgres /usr/lib/postgresql/*/bin/postgres --check-config

# Reset PostgreSQL if corrupted
sudo systemctl stop postgresql
sudo -u postgres /usr/lib/postgresql/*/bin/pg_resetwal /var/lib/postgresql/*/main
sudo systemctl start postgresql
```

#### If PM2 Won't Start
```bash
# Clear PM2 processes
pm2 kill
pm2 flush

# Check application manually
cd /var/www/hauling-qr-system
node server/server.js

# Restart with fresh PM2
pm2 start ecosystem.config.js
pm2 save
```

#### If Nginx Won't Start
```bash
# Test Nginx configuration
sudo nginx -t

# Check for port conflicts
sudo netstat -tlnp | grep :80
sudo fuser -k 80/tcp

# Restart with verbose logging
sudo systemctl restart nginx
sudo journalctl -u nginx -f
```

### Monitoring Service Health
```bash
# Create monitoring script
sudo tee /usr/local/bin/check-hauling-health.sh > /dev/null << 'EOF'
#!/bin/bash
# Health check script for Hauling QR System

echo "🏥 Hauling QR System Health Check"
echo "================================"

# Check system resources
echo "💾 System Resources:"
echo "Memory: $(free -h | grep Mem | awk '{print $3"/"$2}')"
echo "Disk: $(df -h / | tail -1 | awk '{print $3"/"$2" ("$5" used)"}')"
echo "Load: $(uptime | awk -F'load average:' '{print $2}')"
echo ""

# Check services
echo "🔧 Service Status:"
services=("postgresql" "nginx")
for service in "${services[@]}"; do
    if systemctl is-active --quiet $service; then
        echo "✅ $service: Running"
    else
        echo "❌ $service: Stopped"
    fi
done

# Check PM2
if pm2 list | grep -q "online"; then
    echo "✅ PM2 Application: Running"
else
    echo "❌ PM2 Application: Stopped"
fi
echo ""

# Check endpoints
echo "🌐 Endpoint Health:"
if curl -f -s http://localhost/ > /dev/null; then
    echo "✅ Web Frontend: Accessible"
else
    echo "❌ Web Frontend: Not accessible"
fi

if curl -f -s http://localhost:5000/health > /dev/null; then
    echo "✅ API Backend: Accessible"
else
    echo "❌ API Backend: Not accessible"
fi

# Check database
if sudo -u postgres psql -d hauling_qr_system -c "SELECT 1;" > /dev/null 2>&1; then
    echo "✅ Database: Connected"
else
    echo "❌ Database: Connection failed"
fi

echo ""
echo "🕐 Last updated: $(date)"
EOF

sudo chmod +x /usr/local/bin/check-hauling-health.sh

# Usage: sudo check-hauling-health.sh
```

---

## 🔍 IP Detection Issues

### Problem: IP Detection Fails
**Symptoms:**
- Script shows "❌ Failed to detect VPS IP using all methods"
- Network connectivity errors during deployment

**Solutions:**

#### 1. Check Internet Connectivity
```bash
# Test basic connectivity
ping -c 4 *******
curl -I https://google.com

# Test specific IP detection services
curl -s https://ipinfo.io/ip
curl -s https://api.ipify.org
```

#### 2. Manual IP Override
```bash
# Find your IP manually
curl -s https://ipinfo.io/ip

# Set manual override
export MANUAL_IP="your.actual.ip.here"
sudo -E ./auto-deploy-enhanced.sh
```

#### 3. Network Configuration Issues
```bash
# Check network interfaces
ip addr show

# Check routing
ip route show

# Check DNS resolution
nslookup google.com
```

#### 4. Firewall Blocking Outbound Connections
```bash
# Check UFW status
ufw status

# Temporarily allow all outbound (for testing)
ufw allow out 80
ufw allow out 443

# Check iptables
iptables -L OUTPUT
```

---

## 📦 Repository and GitHub Issues

### Problem: Repository Clone Fails
**Symptoms:**
- "❌ Failed to clone repository"
- Authentication errors
- Permission denied errors

**Solutions:**

#### 1. Private Repository Access
```bash
# Generate GitHub Personal Access Token
# Go to GitHub → Settings → Developer settings → Personal access tokens
# Create token with 'repo' scope

# Set PAT environment variable
export GITHUB_PAT="your_github_personal_access_token"
sudo -E ./auto-deploy-enhanced.sh
```

#### 2. Repository URL Issues
```bash
# Use HTTPS URL (not SSH)
sudo -E ./auto-deploy-enhanced.sh --repo-url https://github.com/user/repo.git

# For public repositories
sudo -E ./auto-deploy-enhanced.sh --repo-url https://github.com/mightybadz18/hauling-qr-trip-management.git
```

#### 3. Network/Proxy Issues
```bash
# Check if behind corporate proxy
echo $http_proxy
echo $https_proxy

# Configure git proxy if needed
git config --global http.proxy http://proxy.company.com:8080
git config --global https.proxy https://proxy.company.com:8080
```

---

## 🗄️ Database Issues

### Problem: PostgreSQL Setup Fails
**Symptoms:**
- Database connection errors
- "role does not exist" errors
- Migration failures

**Solutions:**

#### 1. PostgreSQL Service Issues
```bash
# Check PostgreSQL status
systemctl status postgresql

# Start PostgreSQL
systemctl start postgresql

# Check PostgreSQL logs
tail -f /var/log/postgresql/postgresql-*.log

# Restart PostgreSQL
systemctl restart postgresql
```

#### 2. Database User/Role Issues
```bash
# Connect as postgres user
sudo -u postgres psql

# Create user manually
CREATE ROLE hauling_app LOGIN PASSWORD 'PostgreSQLPassword123';
CREATE DATABASE hauling_qr_system OWNER hauling_app;
GRANT ALL PRIVILEGES ON DATABASE hauling_qr_system TO hauling_app;
\q
```

#### 3. Connection Issues
```bash
# Test database connection
sudo -u postgres psql -d hauling_qr_system -c "SELECT version();"

# Check PostgreSQL configuration
sudo nano /etc/postgresql/*/main/postgresql.conf
sudo nano /etc/postgresql/*/main/pg_hba.conf

# Restart after config changes
systemctl restart postgresql
```

#### 4. Migration Failures
```bash
# Run migrations manually
cd /var/www/hauling-qr-system
node ./database/run-migration.js

# Check migration table
sudo -u postgres psql -d hauling_qr_system -c "SELECT * FROM migrations;"

# Reset migrations (if needed)
sudo -u postgres psql -d hauling_qr_system -c "DROP TABLE IF EXISTS migrations;"
```

---

## 🏗️ Build and Dependencies Issues

### Problem: npm Install Fails
**Symptoms:**
- Package installation errors
- Node.js version conflicts
- Build failures

**Solutions:**

#### 1. Node.js Version Issues
```bash
# Check Node.js version
node -v
npm -v

# Install specific Node.js version
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt-get install -y nodejs

# Clear npm cache
npm cache clean --force
```

#### 2. Build Dependencies
```bash
# Install build essentials
sudo apt-get install -y build-essential python3-dev

# Install specific packages that commonly fail
cd /var/www/hauling-qr-system/server
npm install --build-from-source

cd /var/www/hauling-qr-system/client
npm install --legacy-peer-deps
```

#### 3. React Build Issues
```bash
# Build with specific settings
cd /var/www/hauling-qr-system/client
GENERATE_SOURCEMAP=false npm run build

# Clear build cache
rm -rf node_modules/.cache
rm -rf build
npm run build
```

#### 4. Memory Issues During Build
```bash
# Increase Node.js memory limit
export NODE_OPTIONS="--max-old-space-size=4096"
npm run build

# Or use swap file
sudo fallocate -l 2G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
```

---

## 🚀 PM2 Process Issues

### Problem: PM2 Won't Start
**Symptoms:**
- "❌ Failed to start PM2 application"
- Process exits immediately
- PM2 shows "errored" status

**Solutions:**

#### 1. PM2 Process Debugging
```bash
# Check PM2 status
pm2 status

# View detailed logs
pm2 logs hauling-qr-server

# Show process details
pm2 show hauling-qr-server

# Restart with verbose logging
pm2 restart hauling-qr-server --log-type all
```

#### 2. Application Startup Issues
```bash
# Test application manually
cd /var/www/hauling-qr-system
node server/server.js

# Check environment variables
cat .env | grep -E "(NODE_ENV|DB_|PORT)"

# Test with specific environment
NODE_ENV=production node server/server.js
```

#### 3. PM2 Configuration Issues
```bash
# Recreate PM2 ecosystem
cd /var/www/hauling-qr-system
pm2 delete all
pm2 start ecosystem.config.js

# Or start manually
pm2 start server/server.js --name hauling-qr-server --env production
```

#### 4. Permission Issues
```bash
# Fix file permissions
sudo chown -R root:root /var/www/hauling-qr-system
sudo chmod -R 755 /var/www/hauling-qr-system

# Fix PM2 permissions
pm2 kill
pm2 start ecosystem.config.js
pm2 save
```

---

## 🌐 Nginx Configuration Issues

### Problem: Nginx Won't Start or Serve Content
**Symptoms:**
- 502 Bad Gateway errors
- Nginx test failures
- Static files not loading

**Solutions:**

#### 1. Nginx Configuration Testing
```bash
# Test Nginx configuration
nginx -t

# Check Nginx status
systemctl status nginx

# View Nginx error logs
tail -f /var/log/nginx/error.log

# Restart Nginx
systemctl restart nginx
```

#### 2. Configuration File Issues
```bash
# Check site configuration
cat /etc/nginx/sites-available/hauling-qr-system

# Verify symlink
ls -la /etc/nginx/sites-enabled/

# Recreate configuration
sudo rm /etc/nginx/sites-enabled/hauling-qr-system
sudo ln -s /etc/nginx/sites-available/hauling-qr-system /etc/nginx/sites-enabled/
nginx -t && systemctl reload nginx
```

#### 3. Backend Connection Issues
```bash
# Test backend directly
curl http://localhost:5000/health

# Check if backend is listening
netstat -tlnp | grep :5000
ss -tlnp | grep :5000

# Test proxy connection
curl -H "Host: truckhaul.top" http://localhost/api/health
```

#### 4. File Permissions
```bash
# Fix Nginx permissions
sudo chown -R www-data:www-data /var/www/hauling-qr-system/client/build
sudo chmod -R 755 /var/www/hauling-qr-system/client/build

# Check Nginx user
ps aux | grep nginx
```

---

## 🔒 CORS and Security Issues

### Problem: CORS Errors in Browser
**Symptoms:**
- "Access to fetch blocked by CORS policy"
- API calls failing from frontend
- Preflight request failures

**Solutions:**

#### 1. Verify CORS Configuration
```bash
# Check environment variables
cat /var/www/hauling-qr-system/.env | grep -E "(CORS|DEV_ENABLE|DETECTED_VPS_IP)"

# Test CORS manually
curl -i -X OPTIONS http://localhost:5000/api/auth/login \
  -H "Origin: http://your-detected-ip" \
  -H "Access-Control-Request-Method: POST"
```

#### 2. Development-Style CORS Issues
```bash
# Verify development settings are preserved
grep -E "(DEV_ENABLE_CORS_ALL|DEV_DISABLE_RATE_LIMITING)" /var/www/hauling-qr-system/.env

# Should show:
# DEV_ENABLE_CORS_ALL=true
# DEV_DISABLE_RATE_LIMITING=true
```

#### 3. Server CORS Logic
```bash
# Check server logs for CORS messages
pm2 logs hauling-qr-server | grep -i cors

# Look for development-style CORS messages
pm2 logs hauling-qr-server | grep "dev-style"
```

#### 4. Update CORS Origins
```bash
# Add missing origins to .env
echo "ALLOWED_ORIGINS=localhost,127.0.0.1,0.0.0.0,your-detected-ip,yourdomain.com" >> /var/www/hauling-qr-system/.env

# Restart application
pm2 restart hauling-qr-server
```

---

## 🔥 Firewall Issues

### Problem: Services Not Accessible
**Symptoms:**
- Connection timeouts
- Services unreachable from outside
- Port binding issues

**Solutions:**

#### 1. UFW Firewall Check
```bash
# Check UFW status
ufw status verbose

# Allow required ports
ufw allow 22/tcp
ufw allow 80/tcp
ufw allow 443/tcp
ufw allow 5000/tcp

# Reload UFW
ufw reload
```

#### UFW Status: Inactive - Is This Okay?

**When UFW Inactive is ACCEPTABLE:**
- ✅ **Cloud Provider Firewall**: Your VPS provider (DigitalOcean, AWS, Linode) has network-level firewall protection
- ✅ **Development Environment**: Testing or development server, not production
- ✅ **Behind Cloudflare Proxy**: Domain is proxied (orange cloud) through Cloudflare
- ✅ **Behind Load Balancer**: Server is behind AWS ALB, Google Cloud Load Balancer, or similar proxy
- ✅ **Minimal Services**: Only running essential services (Nginx, PostgreSQL, Node.js)

**When UFW Inactive is RISKY:**
- ❌ **Production Server**: Directly exposed to internet without other firewall protection
- ❌ **No Cloud Firewall**: VPS provider doesn't offer network-level firewall
- ❌ **Multiple Services**: Running SSH, FTP, databases, and other potentially vulnerable services
- ❌ **Sensitive Data**: Handling customer data, financial information, or business-critical systems

**Quick Security Assessment:**
```bash
# Check what services are listening on all interfaces
netstat -tlnp | grep "0.0.0.0"
ss -tlnp | grep "0.0.0.0"

# Check for unnecessary services
systemctl list-units --type=service --state=running

# Scan your own server for open ports (from another machine)
nmap -sS your-server-ip
```

**Enable UFW for Production (Recommended):**
```bash
# Enable UFW with essential rules
ufw --force reset
ufw default deny incoming
ufw default allow outgoing

# Allow essential services
ufw allow 22/tcp comment 'SSH'
ufw allow 80/tcp comment 'HTTP'
ufw allow 443/tcp comment 'HTTPS'

# Optional: Allow development port (remove in production)
ufw allow 5000/tcp comment 'Development API'

# Enable firewall
ufw --force enable

# Verify rules
ufw status verbose
```

**Cloud Provider Firewall Check:**

**DigitalOcean:**
```bash
# Check if Droplet has Cloud Firewall attached
curl -X GET "https://api.digitalocean.com/v2/firewalls" \
  -H "Authorization: Bearer YOUR_API_TOKEN"
```

**AWS EC2:**
```bash
# Check Security Groups (if using AWS CLI)
aws ec2 describe-security-groups --group-ids sg-xxxxxxxxx
```

**Cloudflare Proxied Setup - UFW Considerations:**

**With Cloudflare Proxied (Orange Cloud):**
```bash
# Check if your domain is proxied through Cloudflare
dig yourdomain.com
# Should show Cloudflare IPs, not your VPS IP

# Verify Cloudflare is working
curl -I https://yourdomain.com
# Should show "cf-ray" header indicating Cloudflare
```

**UFW Inactive with Cloudflare - Security Analysis:**
- ✅ **Web Traffic Protected**: All HTTP/HTTPS goes through Cloudflare
- ✅ **Real IP Hidden**: Attackers can't easily find your VPS IP
- ✅ **DDoS Protection**: Cloudflare blocks malicious traffic
- ⚠️ **SSH Still Exposed**: Port 22 accessible if real IP discovered
- ⚠️ **Port 5000 Exposed**: API port accessible via direct IP (but hidden)
- ⚠️ **IP Discovery Risk**: If real IP leaked, all ports accessible

**Recommended Cloudflare + Minimal UFW Setup:**
```bash
# Minimal UFW rules for Cloudflare setup
ufw default deny incoming
ufw default allow outgoing

# Allow SSH (essential for server management)
ufw allow 22/tcp comment 'SSH access'

# Allow HTTP/HTTPS (Cloudflare traffic)
ufw allow 80,443/tcp comment 'Web traffic via Cloudflare'

# Block direct API access (optional security layer)
# NOTE: This blocks external access to port 5000, but internal Nginx → API still works
ufw deny 5000/tcp comment 'Block direct API access'

# Enable UFW
ufw --force enable
```

**Traffic Flow with UFW Rules:**
```
✅ WORKS: User → api.yourdomain.com → Cloudflare → Port 443 → Nginx → localhost:5000 → API
❌ BLOCKED: User → **************:5000 → Direct API access
✅ WORKS: Server internal → localhost:5000 → API (for Nginx proxy)
```

**Why Your Website Still Works:**
- External users access `https://api.yourdomain.com/api/endpoint` (port 443)
- Cloudflare forwards to your VPS port 443
- Nginx receives the request and proxies to `localhost:5000`
- `localhost` connections are internal and bypass UFW rules
- API responds through the same path back to user

**Test Your Setup:**
```bash
# These should work (through proper channels):
curl https://api.yourdomain.com/api/health
curl https://yourdomain.com/

# These should be blocked (direct access):
curl http://**************:5000/api/health
curl http://api.yourdomain.com:5000/api/health

# This should work (internal access):
curl http://localhost:5000/api/health
```

**Alternative: Use Cloud Firewall Instead of UFW**
Many prefer cloud-level firewalls because:
- Blocks traffic before it reaches your server
- Better performance (less CPU usage)
- Centralized management across multiple servers
- DDoS protection integration

**Hybrid Approach (Most Secure):**
```bash
# Use both cloud firewall AND UFW
# Cloud firewall: Block obvious threats, allow legitimate traffic
# UFW: Additional layer, specific port restrictions

# Example UFW rules for hybrid setup
ufw default deny incoming
ufw allow from 10.0.0.0/8 to any port 5432 comment 'Internal DB access'
ufw allow 80,443/tcp comment 'Web traffic'
ufw allow 22/tcp comment 'SSH'
ufw enable
```

#### 2. iptables Issues
```bash
# Check iptables rules
iptables -L -n

# Flush iptables (if safe to do so)
iptables -F
iptables -X
iptables -t nat -F
iptables -t nat -X
```

#### 3. Cloud Provider Firewall
Many VPS providers have additional firewalls:

**DigitalOcean:**
- Check Droplet → Networking → Firewalls
- Ensure HTTP (80), HTTPS (443), SSH (22) are allowed

**AWS EC2:**
- Check Security Groups
- Ensure inbound rules allow ports 22, 80, 443

**Linode:**
- Check Cloud Firewall settings
- Verify port access rules

#### 4. Port Binding Issues
```bash
# Check what's listening on ports
netstat -tlnp | grep -E ":(80|443|5000|5432)"
ss -tlnp | grep -E ":(80|443|5000|5432)"

# Kill processes using required ports
sudo fuser -k 80/tcp
sudo fuser -k 5000/tcp
```

---

## 🌍 DNS and Domain Issues

### Problem: Domain Not Resolving
**Symptoms:**
- Domain doesn't point to VPS
- DNS propagation issues
- SSL certificate problems

**Solutions:**

#### 1. DNS Propagation Check
```bash
# Check DNS resolution
dig yourdomain.com
nslookup yourdomain.com

# Check from different locations
# Use online tools: whatsmydns.net, dnschecker.org
```

#### 2. Cloudflare Issues
```bash
# Check Cloudflare DNS records
# Ensure A record points to detected VPS IP
# Verify proxy status (orange cloud) is enabled

# Test direct IP access
curl -H "Host: yourdomain.com" http://your-detected-ip/
```

#### 3. SSL Certificate Issues
```bash
# Test SSL certificate
curl -I https://yourdomain.com

# Check certificate details
openssl s_client -connect yourdomain.com:443 -servername yourdomain.com

# Verify Cloudflare SSL mode is "Full" (not "Full Strict")
```

---

## 📊 Performance Issues

### Problem: Slow Application Response
**Symptoms:**
- Long page load times
- API timeouts
- High server load

**Solutions:**

#### 1. Resource Monitoring
```bash
# Check system resources
htop
free -h
df -h

# Check process usage
ps aux --sort=-%cpu | head -10
ps aux --sort=-%mem | head -10
```

#### 2. Database Performance
```bash
# Check PostgreSQL performance
sudo -u postgres psql -d hauling_qr_system -c "
SELECT query, calls, total_time, mean_time 
FROM pg_stat_statements 
ORDER BY total_time DESC LIMIT 10;"

# Check database connections
sudo -u postgres psql -c "SELECT * FROM pg_stat_activity;"
```

#### 3. Node.js Performance
```bash
# Check PM2 monitoring
pm2 monit

# Increase PM2 memory limit
pm2 restart hauling-qr-server --max-memory-restart 2G

# Enable PM2 clustering (if needed)
pm2 start ecosystem.config.js --instances max
```

#### 4. Nginx Performance
```bash
# Check Nginx access logs for slow requests
tail -f /var/log/nginx/access.log | grep -E " [5-9][0-9]{2} "

# Enable Nginx caching
# Add to Nginx config:
# proxy_cache_path /var/cache/nginx levels=1:2 keys_zone=my_cache:10m;
```

---

## 🔄 Recovery Procedures

### Complete System Recovery
If deployment is completely broken:

#### 1. Clean Slate Recovery
```bash
# Stop all services
pm2 kill
systemctl stop nginx

# Remove application directory
rm -rf /var/www/hauling-qr-system

# Reset database (if needed)
sudo -u postgres dropdb hauling_qr_system
sudo -u postgres dropuser hauling_app

# Re-run deployment
sudo -E ./auto-deploy-enhanced.sh
```

#### 2. Partial Recovery
```bash
# Keep database, rebuild application
pm2 kill
rm -rf /var/www/hauling-qr-system/client/build
rm -rf /var/www/hauling-qr-system/node_modules

# Re-run deployment with existing database
sudo -E ./auto-deploy-enhanced.sh
```

#### 3. Configuration-Only Recovery
```bash
# Keep everything, just fix configuration
cd /var/www/hauling-qr-system

# Backup current .env
cp .env .env.backup

# Recreate .env with detected IP
export DETECTED_VPS_IP=$(curl -s https://ipinfo.io/ip)
# ... recreate .env file ...

# Restart services
pm2 restart hauling-qr-server
systemctl restart nginx
```

---

## 📞 Getting Help

### Log Collection for Support
```bash
# Collect all relevant logs
mkdir -p ~/debug-logs
cp /var/log/hauling-deployment/auto-deploy-*.log ~/debug-logs/
pm2 logs hauling-qr-server --lines 100 > ~/debug-logs/pm2.log
cp /var/log/nginx/error.log ~/debug-logs/nginx-error.log
cp /var/log/postgresql/postgresql-*.log ~/debug-logs/postgres.log
systemctl status nginx postgresql > ~/debug-logs/services-status.log

# Create system info
uname -a > ~/debug-logs/system-info.log
df -h >> ~/debug-logs/system-info.log
free -h >> ~/debug-logs/system-info.log
```

### Common Support Information Needed
- VPS provider and specifications
- Ubuntu version: `lsb_release -a`
- Auto-detected IP address
- Domain name being used
- Error messages from logs
- Steps that led to the issue

### Self-Diagnosis Checklist
- ✅ Internet connectivity working
- ✅ VPS IP detected correctly
- ✅ GitHub repository accessible
- ✅ All services running (PostgreSQL, PM2, Nginx)
- ✅ Firewall configured properly
- ✅ DNS pointing to correct IP
- ✅ SSL certificate valid
- ✅ Application responding to health checks

This troubleshooting guide covers the most common issues encountered during deployment. Most problems can be resolved by following these systematic approaches and checking the relevant logs for specific error messages.