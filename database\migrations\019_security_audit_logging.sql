-- Migration: Enhanced Security Audit Logging
-- Description: Add security_logs table for comprehensive audit trail and abuse monitoring
-- Date: 2025-01-27

-- Create security_logs table for audit trail
CREATE TABLE IF NOT EXISTS security_logs (
    id SERIAL PRIMARY KEY,
    activity_type VARCHAR(100) NOT NULL,
    ip_address INET,
    user_agent TEXT,
    endpoint VARCHAR(255),
    details JSONB,
    risk_level VARCHAR(20) DEFAULT 'LOW',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- <PERSON><PERSON> indexes separately
CREATE INDEX IF NOT EXISTS idx_security_logs_activity_type ON security_logs (activity_type);
CREATE INDEX IF NOT EXISTS idx_security_logs_ip_address ON security_logs (ip_address);
CREATE INDEX IF NOT EXISTS idx_security_logs_created_at ON security_logs (created_at);
CREATE INDEX IF NOT EXISTS idx_security_logs_risk_level ON security_logs (risk_level);

-- Create GIN index for JSONB details column
CREATE INDEX IF NOT EXISTS idx_security_logs_details_gin ON security_logs USING GIN (details);

-- Add security audit fields to existing driver_shifts table
ALTER TABLE driver_shifts 
ADD COLUMN IF NOT EXISTS security_context JSONB,
ADD COLUMN IF NOT EXISTS audit_trail JSONB DEFAULT '[]'::jsonb;

-- Create index for security context searches
CREATE INDEX IF NOT EXISTS idx_driver_shifts_security_context_gin ON driver_shifts USING GIN (security_context);

-- Create view for security dashboard
CREATE OR REPLACE VIEW security_dashboard AS
SELECT 
    DATE(created_at) as log_date,
    activity_type,
    COUNT(*) as incident_count,
    COUNT(DISTINCT ip_address) as unique_ips,
    risk_level,
    MAX(created_at) as latest_incident
FROM security_logs 
WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY DATE(created_at), activity_type, risk_level
ORDER BY log_date DESC, incident_count DESC;

-- Create function to clean old security logs (retention policy)
CREATE OR REPLACE FUNCTION cleanup_security_logs()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- Delete logs older than 90 days (adjust as needed)
    DELETE FROM security_logs 
    WHERE created_at < CURRENT_DATE - INTERVAL '90 days';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- Log the cleanup operation
    INSERT INTO security_logs (activity_type, details, created_at)
    VALUES ('LOG_CLEANUP', jsonb_build_object('deleted_count', deleted_count), CURRENT_TIMESTAMP);
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Create stored procedure for security incident analysis
CREATE OR REPLACE FUNCTION analyze_security_incidents(
    p_start_date DATE DEFAULT CURRENT_DATE - INTERVAL '7 days',
    p_end_date DATE DEFAULT CURRENT_DATE
)
RETURNS TABLE (
    activity_type VARCHAR(100),
    incident_count BIGINT,
    unique_ips BIGINT,
    high_risk_count BIGINT,
    top_ip INET,
    top_ip_count BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        sl.activity_type,
        COUNT(*) as incident_count,
        COUNT(DISTINCT sl.ip_address) as unique_ips,
        COUNT(*) FILTER (WHERE sl.risk_level IN ('HIGH', 'CRITICAL')) as high_risk_count,
        (
            SELECT ip_address 
            FROM security_logs sl2 
            WHERE sl2.activity_type = sl.activity_type 
              AND sl2.created_at BETWEEN p_start_date AND p_end_date + INTERVAL '1 day'
            GROUP BY ip_address 
            ORDER BY COUNT(*) DESC 
            LIMIT 1
        ) as top_ip,
        (
            SELECT COUNT(*) 
            FROM security_logs sl3 
            WHERE sl3.activity_type = sl.activity_type 
              AND sl3.ip_address = (
                  SELECT ip_address 
                  FROM security_logs sl4 
                  WHERE sl4.activity_type = sl.activity_type 
                    AND sl4.created_at BETWEEN p_start_date AND p_end_date + INTERVAL '1 day'
                  GROUP BY ip_address 
                  ORDER BY COUNT(*) DESC 
                  LIMIT 1
              )
              AND sl3.created_at BETWEEN p_start_date AND p_end_date + INTERVAL '1 day'
        ) as top_ip_count
    FROM security_logs sl
    WHERE sl.created_at BETWEEN p_start_date AND p_end_date + INTERVAL '1 day'
    GROUP BY sl.activity_type
    ORDER BY incident_count DESC;
END;
$$ LANGUAGE plpgsql;

-- Add comments for documentation
COMMENT ON TABLE security_logs IS 'Comprehensive security audit log for tracking suspicious activities and abuse patterns';
COMMENT ON COLUMN security_logs.activity_type IS 'Type of security event (e.g., FAILED_AUTHENTICATION, RATE_LIMIT_EXCEEDED, SUSPICIOUS_ACTIVITY)';
COMMENT ON COLUMN security_logs.details IS 'JSON details about the security event including context and indicators';
COMMENT ON COLUMN security_logs.risk_level IS 'Risk assessment level: LOW, MEDIUM, HIGH, CRITICAL';

COMMENT ON VIEW security_dashboard IS 'Daily security incident summary for monitoring dashboard';
COMMENT ON FUNCTION cleanup_security_logs() IS 'Automated cleanup function for security log retention policy';
COMMENT ON FUNCTION analyze_security_incidents(DATE, DATE) IS 'Security incident analysis for specified date range';

-- Grant appropriate permissions (skip if roles don't exist)
-- GRANT SELECT ON security_logs TO hauling_read_role;
-- GRANT INSERT ON security_logs TO hauling_write_role;
-- GRANT SELECT ON security_dashboard TO hauling_read_role;

-- Insert initial security configuration
INSERT INTO security_logs (activity_type, details, risk_level) 
VALUES ('SECURITY_SYSTEM_INITIALIZED', 
        jsonb_build_object(
            'version', '1.0',
            'features', jsonb_build_array('audit_logging', 'tamper_detection', 'rate_limiting', 'abuse_monitoring'),
            'initialized_at', CURRENT_TIMESTAMP
        ), 
        'LOW')
ON CONFLICT DO NOTHING;