const express = require('express');
const Joi = require('joi');
const auth = require('../middleware/auth');
const { query } = require('../config/database');
const DriverQRCodeGenerator = require('../utils/DriverQRCodeGenerator');
const { logError, logInfo } = require('../utils/logger');
const { notifyDriverManualCheckout } = require('../websocket');

const router = express.Router();

// All routes require authentication
router.use(auth);

// Reusable schema helpers
const createNumericField = (constraints = {}) => {
  const { min, max, positive = false } = constraints;
  let numberSchema = Joi.number().integer();
  
  if (positive) numberSchema = numberSchema.positive();
  if (min !== undefined) numberSchema = numberSchema.min(min);
  if (max !== undefined) numberSchema = numberSchema.max(max);
  
  return Joi.alternatives().try(
    numberSchema,
    Joi.string().allow('').custom((value, helpers) => {
      // Handle empty strings
      if (value === '' || value === undefined || value === null) {
        return undefined;
      }
      
      // Check if it's a valid number string
      if (!/^\d+$/.test(value)) {
        return helpers.error('number.base');
      }
      
      const parsed = parseInt(value, 10);
      if (positive && parsed <= 0) {
        return helpers.error('number.positive');
      }
      if (min !== undefined && parsed < min) {
        return helpers.error('number.min', { limit: min });
      }
      if (max !== undefined && parsed > max) {
        return helpers.error('number.max', { limit: max });
      }
      return parsed;
    })
  );
};

const createDateField = () => {
  return Joi.alternatives().try(
    Joi.date(),
    Joi.string().allow('').custom((value, helpers) => {
      // Handle empty strings
      if (value === '' || value === undefined || value === null) {
        return undefined;
      }
      
      // Check if it matches date format
      if (!/^\d{4}-\d{2}-\d{2}$/.test(value)) {
        return helpers.error('date.format');
      }
      
      const date = new Date(value);
      if (isNaN(date.getTime())) {
        return helpers.error('date.base');
      }
      return value; // Return as string for consistency
    })
  );
};

// Validation schemas
const generateQRSchema = Joi.object({
  driverId: Joi.number().integer().positive().required()
});

const attendanceQuerySchema = Joi.object({
  driver_id: createNumericField({ positive: true }).optional(),
  date_from: createDateField().optional(),
  date_to: createDateField().optional(),
  truck_id: createNumericField({ positive: true }).optional(),
  status: Joi.string().valid('all', 'active', 'completed', 'scheduled').optional().allow(''),
  sort_by: Joi.string().valid('start_date', 'start_time', 'end_date', 'end_time', 'driver_name', 'truck_number').optional().allow(''),
  sort_order: Joi.string().valid('asc', 'desc').optional().allow(''),
  limit: createNumericField({ min: 1, max: 100 }).default(50),
  offset: createNumericField({ min: 0 }).default(0)
}).options({ stripUnknown: true });

const manualCheckoutSchema = Joi.object({
  reason: Joi.string().required(),
  notes: Joi.string().optional()
});

/**
 * @route   POST /api/driver-admin/generate-qr/:driverId
 * @desc    Generate QR code for driver
 * @access  Private (Admin/Supervisor)
 */
router.post('/generate-qr/:driverId', async (req, res) => {
  try {
    // Validate driver ID
    const { error } = generateQRSchema.validate({ driverId: parseInt(req.params.driverId) });
    if (error) {
      return res.status(400).json({
        success: false,
        error: 'VALIDATION_ERROR',
        message: 'Invalid driver ID',
        details: error.details[0].message
      });
    }

    const driverId = parseInt(req.params.driverId);

    // Get driver info first
    const driverResult = await query(
      'SELECT id, employee_id, full_name, status FROM drivers WHERE id = $1',
      [driverId]
    );

    if (driverResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'DRIVER_NOT_FOUND',
        message: 'Driver not found'
      });
    }

    const driver = driverResult.rows[0];

    // Generate QR code
    const qrResult = await DriverQRCodeGenerator.generateDriverQR(driverId, driver.employee_id);

    // Log the operation
    logInfo('ADMIN_QR_GENERATED', `Admin generated QR code for driver ${driver.employee_id}`, {
      admin_user_id: req.user.id,
      driver_id: driverId,
      employee_id: driver.employee_id
    });

    res.json({
      success: true,
      data: {
        employee_id: driver.employee_id,
        full_name: driver.full_name,
        qr_code: qrResult.qr_code_image,
        qr_data: qrResult.qr_data
      }
    });

  } catch (error) {
    logError('ADMIN_QR_GENERATION_ERROR', error, {
      admin_user_id: req.user?.id,
      driver_id: req.params.driverId
    });

    res.status(500).json({
      success: false,
      error: 'INTERNAL_ERROR',
      message: 'Failed to generate QR code. Please try again.'
    });
  }
});

/**
 * @route   GET /api/driver-admin/attendance
 * @desc    Get driver attendance records with filtering
 * @access  Private (Admin/Supervisor)
 */
router.get('/attendance', async (req, res) => {
  try {
    // Clean empty strings from query parameters
    const cleanQuery = Object.fromEntries(
      Object.entries(req.query).map(([key, value]) => [key, value === '' ? undefined : value])
    );

    // Validate query parameters
    const { error, value } = attendanceQuerySchema.validate(cleanQuery);
    if (error) {
      console.log('ATTENDANCE_VALIDATION_ERROR:', error.details[0].message);
      return res.status(400).json({
        success: false,
        error: 'VALIDATION_ERROR',
        message: 'Invalid query parameters',
        details: error.details[0].message
      });
    }

    const { driver_id, date_from, date_to, truck_id, status, sort_by, sort_order, limit, offset } = value;

    // Build dynamic query - Show all shifts for attendance tracking
    let whereClause = 'WHERE 1=1';
    let params = [];
    let paramCount = 0;

    if (driver_id) {
      paramCount++;
      whereClause += ` AND ds.driver_id = $${paramCount}`;
      params.push(driver_id);
    }

    // Enhanced date filtering with overlap detection for overnight shifts
    if (date_from || date_to) {
      if (date_from && date_to) {
        // Both dates provided - use overlap detection
        paramCount++;
        whereClause += ` AND (
          -- Shift starts within the date range
          (ds.start_date BETWEEN $${paramCount} AND $${paramCount + 1}) OR
          -- Shift ends within the date range
          (ds.end_date BETWEEN $${paramCount} AND $${paramCount + 1}) OR
          -- Shift spans the entire date range
          (ds.start_date <= $${paramCount} AND ds.end_date >= $${paramCount + 1})
        )`;
        params.push(date_from, date_to);
        paramCount++; // Increment for the second parameter
      } else if (date_from) {
        // Only start date provided
        paramCount++;
        whereClause += ` AND (ds.start_date >= $${paramCount} OR ds.end_date >= $${paramCount})`;
        params.push(date_from);
      } else if (date_to) {
        // Only end date provided
        paramCount++;
        whereClause += ` AND (ds.start_date <= $${paramCount} OR ds.end_date <= $${paramCount})`;
        params.push(date_to);
      }
    }

    if (truck_id) {
      paramCount++;
      whereClause += ` AND ds.truck_id = $${paramCount}`;
      params.push(truck_id);
    }

    // Add status filter if not 'all'
    if (status && status !== 'all') {
      paramCount++;
      whereClause += ` AND ds.status = $${paramCount}`;
      params.push(status);
    }

    // Get attendance records
    const attendanceQuery = `
      SELECT 
        ds.id as shift_id,
        ds.driver_id,
        d.employee_id,
        d.full_name as driver_name,
        ds.truck_id,
        dt.truck_number,
        ds.start_date,
        ds.start_time,
        ds.end_date,
        ds.end_time,
        ds.status,
        ds.shift_type,
        ds.auto_created,
        ds.handover_notes,
        ds.completion_notes,
        ds.created_at,
        ds.updated_at,
        -- Calculate duration for completed shifts, null for others
        CASE
          WHEN ds.status = 'completed' AND ds.end_date IS NOT NULL AND ds.end_time IS NOT NULL
          THEN EXTRACT(EPOCH FROM (
            (ds.end_date + ds.end_time) - (ds.start_date + ds.start_time)
          )) / 3600
          ELSE NULL
        END as duration_hours
      FROM driver_shifts ds
      JOIN drivers d ON ds.driver_id = d.id
      JOIN dump_trucks dt ON ds.truck_id = dt.id
      ${whereClause}
      ORDER BY ds.start_date DESC, ds.start_time DESC
      LIMIT $${paramCount + 1} OFFSET $${paramCount + 2}
    `;

    params.push(limit, offset);

    console.log('ATTENDANCE_DEBUG: Query params:', params);
    console.log('ATTENDANCE_DEBUG: Where clause:', whereClause);

    const attendanceResult = await query(attendanceQuery, params);
    console.log('ATTENDANCE_DEBUG: Found', attendanceResult.rows.length, 'records');

    // Get total count for pagination
    const countQuery = `
      SELECT COUNT(*) as total
      FROM driver_shifts ds
      JOIN drivers d ON ds.driver_id = d.id
      JOIN dump_trucks dt ON ds.truck_id = dt.id
      ${whereClause}
    `;

    const countResult = await query(countQuery, params.slice(0, paramCount));
    const totalRecords = parseInt(countResult.rows[0].total);
    console.log('ATTENDANCE_DEBUG: Total records:', totalRecords);

    // Format the results with proper duration logic
    const formattedRecords = attendanceResult.rows.map(record => {
      let durationFormatted = null;

      if (record.status === 'completed' && record.duration_hours) {
        // Show calculated duration for completed shifts
        const hours = Math.floor(record.duration_hours);
        const minutes = Math.floor((record.duration_hours % 1) * 60);
        durationFormatted = `${hours}h ${minutes}m`;
      } else if (record.status === 'active' || record.status === 'scheduled') {
        // Show "In Progress" for active or scheduled shifts
        durationFormatted = 'In Progress';
      }

      return {
        ...record,
        duration_formatted: durationFormatted,
        is_active: record.status === 'active'
      };
    });

    res.json({
      success: true,
      data: {
        records: formattedRecords,
        pagination: {
          total: totalRecords,
          limit,
          offset,
          has_more: offset + limit < totalRecords
        },
        filters: {
          driver_id,
          date_from,
          date_to,
          truck_id,
          status
        }
      }
    });

  } catch (error) {
    console.error('ATTENDANCE_ERROR:', error);
    logError('ADMIN_ATTENDANCE_ERROR', error, {
      admin_user_id: req.user?.id,
      query_params: req.query
    });

    res.status(500).json({
      success: false,
      error: 'INTERNAL_ERROR',
      message: 'Failed to retrieve attendance records. Please try again.',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @route   POST /api/driver-admin/manual-checkout/:shiftId
 * @desc    Emergency manual checkout for supervisors
 * @access  Private (Admin/Supervisor)
 */
router.post('/manual-checkout/:shiftId', async (req, res) => {
  try {
    const shiftId = parseInt(req.params.shiftId);
    
    if (!shiftId || isNaN(shiftId)) {
      return res.status(400).json({
        success: false,
        error: 'VALIDATION_ERROR',
        message: 'Invalid shift ID'
      });
    }

    // Validate request body
    const { error, value } = manualCheckoutSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        error: 'VALIDATION_ERROR',
        message: 'Invalid request data',
        details: error.details[0].message
      });
    }

    const { reason, notes } = value;

    // Get shift info
    const shiftResult = await query(
      `SELECT ds.id, ds.driver_id, ds.truck_id, ds.status, ds.start_date, ds.start_time,
              d.employee_id, d.full_name, dt.truck_number
       FROM driver_shifts ds
       JOIN drivers d ON ds.driver_id = d.id
       JOIN dump_trucks dt ON ds.truck_id = dt.id
       WHERE ds.id = $1`,
      [shiftId]
    );

    if (shiftResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'SHIFT_NOT_FOUND',
        message: 'Shift not found'
      });
    }

    const shift = shiftResult.rows[0];

    if (shift.status !== 'active') {
      return res.status(400).json({
        success: false,
        error: 'SHIFT_NOT_ACTIVE',
        message: 'Shift is not active and cannot be manually checked out'
      });
    }

    // Perform manual checkout
    const currentTimestamp = new Date();
    const currentDate = currentTimestamp.toISOString().split('T')[0];
    const currentTime = currentTimestamp.toTimeString().split(' ')[0];

    await query(
      `UPDATE driver_shifts 
       SET status = 'completed', 
           end_date = $1, 
           end_time = $2, 
           completion_notes = $3,
           updated_at = $4
       WHERE id = $5`,
      [
        currentDate,
        currentTime,
        `Manual checkout by supervisor: ${reason}${notes ? '. Notes: ' + notes : ''}`,
        currentTimestamp,
        shiftId
      ]
    );

    // Calculate duration
    const startDateTime = new Date(`${shift.start_date}T${shift.start_time}`);
    const endDateTime = currentTimestamp;
    const durationMs = endDateTime - startDateTime;
    const durationHours = Math.floor(durationMs / (1000 * 60 * 60));
    const durationMinutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60));

    // Send WebSocket notification
    try {
      notifyDriverManualCheckout(
        {
          id: shift.driver_id,
          employee_id: shift.employee_id,
          full_name: shift.full_name
        },
        {
          id: shift.truck_id,
          truck_number: shift.truck_number
        },
        {
          shift_id: shiftId,
          reason,
          notes,
          checkout_time: currentTimestamp.toISOString(),
          duration: `${durationHours}h ${durationMinutes}m`
        },
        {
          id: req.user.id,
          username: req.user.username || req.user.email
        }
      );
    } catch (wsError) {
      // Don't fail the request if WebSocket notification fails
      logError('WEBSOCKET_NOTIFICATION_ERROR', wsError, {
        shift_id: shiftId,
        supervisor_id: req.user.id
      });
    }

    // Log the manual checkout
    logInfo('MANUAL_CHECKOUT', `Supervisor manually checked out driver ${shift.employee_id}`, {
      supervisor_user_id: req.user.id,
      shift_id: shiftId,
      driver_id: shift.driver_id,
      employee_id: shift.employee_id,
      truck_id: shift.truck_id,
      truck_number: shift.truck_number,
      reason,
      notes,
      duration: `${durationHours}h ${durationMinutes}m`
    });

    res.json({
      success: true,
      message: `Successfully checked out ${shift.full_name} from ${shift.truck_number}`,
      data: {
        shift_id: shiftId,
        driver: {
          employee_id: shift.employee_id,
          full_name: shift.full_name
        },
        truck: shift.truck_number,
        checkout_time: currentTimestamp.toISOString(),
        duration: `${durationHours}h ${durationMinutes}m`,
        reason,
        notes,
        checked_out_by: req.user.username || req.user.email
      }
    });

  } catch (error) {
    logError('MANUAL_CHECKOUT_ERROR', error, {
      supervisor_user_id: req.user?.id,
      shift_id: req.params.shiftId,
      request_body: req.body
    });

    res.status(500).json({
      success: false,
      error: 'INTERNAL_ERROR',
      message: 'Failed to perform manual checkout. Please try again.'
    });
  }
});

/**
 * @route   GET /api/driver-admin/qr-info/:driverId
 * @desc    Get driver QR code information
 * @access  Private (Admin/Supervisor)
 */
router.get('/qr-info/:driverId', async (req, res) => {
  try {
    const driverId = parseInt(req.params.driverId);
    
    if (!driverId || isNaN(driverId)) {
      return res.status(400).json({
        success: false,
        error: 'VALIDATION_ERROR',
        message: 'Invalid driver ID'
      });
    }

    const qrInfo = await DriverQRCodeGenerator.getDriverQRInfo(driverId);

    res.json({
      success: true,
      data: qrInfo
    });

  } catch (error) {
    if (error.message === 'Driver not found') {
      return res.status(404).json({
        success: false,
        error: 'DRIVER_NOT_FOUND',
        message: 'Driver not found'
      });
    }

    logError('DRIVER_QR_INFO_ERROR', error, {
      admin_user_id: req.user?.id,
      driver_id: req.params.driverId
    });

    res.status(500).json({
      success: false,
      error: 'INTERNAL_ERROR',
      message: 'Failed to retrieve QR code information. Please try again.'
    });
  }
});

/**
 * @route   GET /api/driver-admin/attendance-summary
 * @desc    Get attendance summary for a specific period
 * @access  Private (Admin/Supervisor)
 */
router.get('/attendance-summary', async (req, res) => {
  try {
    // Clean empty strings from query parameters
    const cleanQuery = Object.fromEntries(
      Object.entries(req.query).map(([key, value]) => [key, value === '' ? undefined : value])
    );

    // Validate query parameters
    const summarySchema = Joi.object({
      period: Joi.string().valid('daily', 'weekly', 'monthly').default('weekly'),
      driver_id: createNumericField({ positive: true }).optional(),
      date_from: createDateField().optional(),
      date_to: createDateField().optional(),
      limit: createNumericField({ min: 1, max: 100 }).default(50),
      offset: createNumericField({ min: 0 }).default(0)
    });

    const { error, value } = summarySchema.validate(cleanQuery);
    if (error) {
      return res.status(400).json({
        success: false,
        error: 'VALIDATION_ERROR',
        message: 'Invalid query parameters',
        details: error.details[0].message
      });
    }

    const { period, driver_id, date_from, date_to } = value;
    
    const DriverAttendanceService = require('../services/DriverAttendanceService');
    
    const summary = await DriverAttendanceService.generateAttendanceSummary(
      period,
      driver_id || null,
      date_from,
      date_to
    );

    res.json(summary);

  } catch (error) {
    console.error('ATTENDANCE_SUMMARY_ERROR:', error);
    logError('ATTENDANCE_SUMMARY_ERROR', error, {
      admin_user_id: req.user?.id,
      query_params: req.query
    });

    res.status(500).json({
      success: false,
      error: 'INTERNAL_ERROR',
      message: 'Failed to generate attendance summary. Please try again.',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @route   GET /api/driver-admin/export-attendance
 * @desc    Export attendance data for payroll processing
 * @access  Private (Admin/Supervisor)
 */
router.get('/export-attendance', async (req, res) => {
  try {
    // Clean empty strings from query parameters
    const cleanQuery = Object.fromEntries(
      Object.entries(req.query).map(([key, value]) => [key, value === '' ? undefined : value])
    );

    // Validate export parameters - use same schema as attendance query
    const exportSchema = Joi.object({
      driver_id: createNumericField({ positive: true }).optional(),
      date_from: createDateField().optional(),
      date_to: createDateField().optional(),
      truck_id: createNumericField({ positive: true }).optional(),
      status: Joi.string().valid('all', 'active', 'completed', 'scheduled').optional().allow(''),
      sort_by: Joi.string().valid('start_date', 'start_time', 'end_date', 'end_time', 'driver_name', 'truck_number').optional().allow(''),
      sort_order: Joi.string().valid('asc', 'desc').optional().allow('')
    }).options({ stripUnknown: true });

    const { error, value } = exportSchema.validate(cleanQuery);
    if (error) {
      return res.status(400).json({
        success: false,
        error: 'VALIDATION_ERROR',
        message: 'Invalid export parameters',
        details: error.details[0].message
      });
    }

    const DriverAttendanceService = require('../services/DriverAttendanceService');
    
    const exportData = await DriverAttendanceService.exportAttendanceForPayroll(value);

    res.json(exportData);

  } catch (error) {
    logError('EXPORT_ATTENDANCE_ERROR', error, {
      admin_user_id: req.user?.id,
      query_params: req.query
    });

    res.status(500).json({
      success: false,
      error: 'INTERNAL_ERROR',
      message: 'Failed to export attendance data. Please try again.',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @route   GET /api/driver-admin/debug
 * @desc    Debug endpoint to check driver_shifts data
 * @access  Private (Admin/Supervisor) - Development only
 */
router.get('/debug', async (req, res) => {
  // Restrict debug endpoint to development environment
  if (process.env.NODE_ENV === 'production') {
    return res.status(404).json({
      success: false,
      error: 'NOT_FOUND',
      message: 'Endpoint not available'
    });
  }

  try {
    // Get all debug information in a single query using CTEs
    const debugQuery = `
      WITH counts AS (
        SELECT 
          (SELECT COUNT(*) FROM driver_shifts) as total_shifts,
          (SELECT COUNT(*) FROM drivers) as total_drivers,
          (SELECT COUNT(*) FROM dump_trucks) as total_trucks
      ),
      sample_shifts AS (
        SELECT ds.id, ds.driver_id, ds.truck_id, ds.status, ds.start_date, ds.shift_type,
               d.full_name, dt.truck_number
        FROM driver_shifts ds
        LEFT JOIN drivers d ON ds.driver_id = d.id
        LEFT JOIN dump_trucks dt ON ds.truck_id = dt.id
        ORDER BY ds.created_at DESC
        LIMIT 5
      )
      SELECT 
        c.total_shifts,
        c.total_drivers,
        c.total_trucks,
        COALESCE(
          json_agg(
            json_build_object(
              'id', ss.id,
              'driver_id', ss.driver_id,
              'truck_id', ss.truck_id,
              'status', ss.status,
              'start_date', ss.start_date,
              'shift_type', ss.shift_type,
              'full_name', ss.full_name,
              'truck_number', ss.truck_number
            )
          ) FILTER (WHERE ss.id IS NOT NULL),
          '[]'::json
        ) as sample_shifts
      FROM counts c
      LEFT JOIN sample_shifts ss ON true
      GROUP BY c.total_shifts, c.total_drivers, c.total_trucks
    `;
    
    const debugResult = await query(debugQuery);

    const debugData = debugResult.rows[0];
    
    res.json({
      success: true,
      debug_info: {
        total_shifts: parseInt(debugData.total_shifts),
        total_drivers: parseInt(debugData.total_drivers),
        total_trucks: parseInt(debugData.total_trucks),
        sample_shifts: debugData.sample_shifts,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    logError('DEBUG_ENDPOINT_ERROR', error, {
      admin_user_id: req.user?.id,
      timestamp: new Date().toISOString()
    });
    
    res.status(500).json({
      success: false,
      error: 'DEBUG_ERROR',
      message: error.message
    });
  }
});

/**
 * @route   GET /api/driver-admin/health
 * @desc    Health check endpoint for driver admin system
 * @access  Private (Admin/Supervisor)
 */
router.get('/health', async (req, res) => {
  try {
    // Perform basic database connectivity check
    const dbCheck = await query('SELECT 1 as health_check');
    const isDbHealthy = dbCheck.rows.length > 0;

    const healthStatus = {
      success: true,
      service: 'Driver Admin API',
      status: isDbHealthy ? 'healthy' : 'degraded',
      timestamp: new Date().toISOString(),
      checks: {
        database: isDbHealthy ? 'healthy' : 'unhealthy',
        authentication: req.user ? 'authenticated' : 'unauthenticated'
      },
      user: {
        id: req.user.id,
        username: req.user.username || req.user.email
      }
    };

    const statusCode = isDbHealthy ? 200 : 503;
    res.status(statusCode).json(healthStatus);

  } catch (error) {
    logError('HEALTH_CHECK_ERROR', error, {
      admin_user_id: req.user?.id
    });

    res.status(503).json({
      success: false,
      service: 'Driver Admin API',
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      checks: {
        database: 'unhealthy',
        authentication: req.user ? 'authenticated' : 'unauthenticated'
      },
      error: 'Health check failed'
    });
  }
});

module.exports = router;