/**
 * Task Management Service
 * 
 * Provides functionality for creating, managing, and executing maintenance tasks
 * for the System Health Monitoring feature.
 */

const { Pool } = require('pg');
const { getServerConfig } = require('../config/unified-config');

class TaskManagementService {
  constructor() {
    const config = getServerConfig();
    this.pool = new Pool(config.database);
  }

  /**
   * Create a new maintenance task
   * 
   * @param {Object} taskData - Task data
   * @param {string} taskData.type - Task type (maintenance, cleanup, monitoring, optimization)
   * @param {string} taskData.priority - Priority level (low, medium, high, critical)
   * @param {string} taskData.title - Task title
   * @param {string} taskData.description - Task description
   * @param {Date} [taskData.scheduledFor] - When the task is scheduled to run
   * @param {boolean} [taskData.autoExecutable=false] - Whether the task can be executed automatically
   * @param {Object} [taskData.metadata] - Additional task-specific data
   * @param {number} [taskData.createdBy] - User <PERSON> who created the task
   * @returns {Promise<Object>} Created task
   */
  async createTask(taskData) {
    try {
      const query = `
        INSERT INTO system_tasks (
          type, priority, title, description, scheduled_for, 
          auto_executable, metadata, created_by
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        RETURNING *
      `;
      
      const values = [
        taskData.type,
        taskData.priority || 'medium',
        taskData.title,
        taskData.description,
        taskData.scheduledFor || null,
        taskData.autoExecutable || false,
        taskData.metadata || null,
        taskData.createdBy || null
      ];
      
      const result = await this.pool.query(query, values);
      return result.rows[0];
    } catch (error) {
      console.error('Error creating task:', error);
      throw new Error(`Failed to create task: ${error.message}`);
    }
  }

  /**
   * Get all tasks with optional filtering
   * 
   * @param {Object} [filters] - Optional filters
   * @param {string} [filters.type] - Filter by task type
   * @param {string} [filters.priority] - Filter by priority
   * @param {string} [filters.status] - Filter by status
   * @param {boolean} [filters.autoExecutable] - Filter by auto-executable flag
   * @param {number} [limit=50] - Maximum number of tasks to return
   * @param {number} [offset=0] - Offset for pagination
   * @returns {Promise<Array>} List of tasks
   */
  async getTasks(filters = {}, limit = 50, offset = 0) {
    try {
      let query = 'SELECT * FROM system_tasks WHERE 1=1';
      const values = [];
      let paramIndex = 1;
      
      if (filters.type) {
        query += ` AND type = $${paramIndex++}`;
        values.push(filters.type);
      }
      
      if (filters.priority) {
        query += ` AND priority = $${paramIndex++}`;
        values.push(filters.priority);
      }
      
      if (filters.status) {
        query += ` AND status = $${paramIndex++}`;
        values.push(filters.status);
      }
      
      if (filters.autoExecutable !== undefined) {
        query += ` AND auto_executable = $${paramIndex++}`;
        values.push(filters.autoExecutable);
      }
      
      query += ` ORDER BY created_at DESC LIMIT $${paramIndex++} OFFSET $${paramIndex}`;
      values.push(limit, offset);
      
      const result = await this.pool.query(query, values);
      return result.rows;
    } catch (error) {
      console.error('Error getting tasks:', error);
      throw new Error(`Failed to get tasks: ${error.message}`);
    }
  }

  /**
   * Get a task by ID
   * 
   * @param {number} taskId - Task ID
   * @returns {Promise<Object>} Task data
   */
  async getTaskById(taskId) {
    try {
      const query = 'SELECT * FROM system_tasks WHERE id = $1';
      const result = await this.pool.query(query, [taskId]);
      
      if (result.rows.length === 0) {
        throw new Error(`Task with ID ${taskId} not found`);
      }
      
      return result.rows[0];
    } catch (error) {
      console.error(`Error getting task ${taskId}:`, error);
      throw new Error(`Failed to get task: ${error.message}`);
    }
  }

  /**
   * Update a task's status
   * 
   * @param {number} taskId - Task ID
   * @param {string} status - New status (pending, in_progress, completed, failed, cancelled)
   * @param {Object} [updateData] - Additional data to update
   * @returns {Promise<Object>} Updated task
   */
  async updateTaskStatus(taskId, status, updateData = {}) {
    try {
      let query = 'UPDATE system_tasks SET status = $1';
      const values = [status];
      let paramIndex = 2;
      
      if (status === 'completed') {
        query += ', completed_at = NOW()';
      }
      
      // Add any additional update fields
      Object.keys(updateData).forEach(key => {
        if (['type', 'priority', 'title', 'description', 'scheduled_for', 'estimated_duration', 'auto_executable', 'metadata'].includes(key)) {
          query += `, ${key} = $${paramIndex++}`;
          values.push(updateData[key]);
        }
      });
      
      query += ` WHERE id = $${paramIndex} RETURNING *`;
      values.push(taskId);
      
      const result = await this.pool.query(query, values);
      
      if (result.rows.length === 0) {
        throw new Error(`Task with ID ${taskId} not found`);
      }
      
      return result.rows[0];
    } catch (error) {
      console.error(`Error updating task ${taskId}:`, error);
      throw new Error(`Failed to update task: ${error.message}`);
    }
  }

  /**
   * Delete a task
   * 
   * @param {number} taskId - Task ID
   * @returns {Promise<boolean>} True if deleted successfully
   */
  async deleteTask(taskId) {
    try {
      const query = 'DELETE FROM system_tasks WHERE id = $1 RETURNING id';
      const result = await this.pool.query(query, [taskId]);
      
      if (result.rows.length === 0) {
        throw new Error(`Task with ID ${taskId} not found`);
      }
      
      return true;
    } catch (error) {
      console.error(`Error deleting task ${taskId}:`, error);
      throw new Error(`Failed to delete task: ${error.message}`);
    }
  }

  /**
   * Get scheduled tasks that are due for execution
   * 
   * @returns {Promise<Array>} List of scheduled tasks
   */
  async getScheduledTasks() {
    try {
      const query = `
        SELECT * FROM system_tasks 
        WHERE status = 'pending' 
        AND scheduled_for IS NOT NULL 
        AND scheduled_for <= NOW() 
        ORDER BY priority DESC, scheduled_for ASC
      `;
      
      const result = await this.pool.query(query);
      return result.rows;
    } catch (error) {
      console.error('Error getting scheduled tasks:', error);
      throw new Error(`Failed to get scheduled tasks: ${error.message}`);
    }
  }

  /**
   * Execute a task automatically if it's auto-executable
   * 
   * @param {Object} task - Task to execute
   * @returns {Promise<Object>} Execution result
   */
  async executeTask(task) {
    if (!task.auto_executable) {
      throw new Error(`Task ${task.id} is not auto-executable`);
    }
    
    try {
      // Update task status to in_progress
      await this.updateTaskStatus(task.id, 'in_progress');
      
      let result = {
        success: false,
        message: '',
        details: {}
      };
      
      // Execute task based on type
      switch (task.type) {
        case 'maintenance':
          result = await this.executeMaintenanceTask(task);
          break;
        case 'cleanup':
          result = await this.executeCleanupTask(task);
          break;
        case 'monitoring':
          result = await this.executeMonitoringTask(task);
          break;
        case 'optimization':
          result = await this.executeOptimizationTask(task);
          break;
        default:
          throw new Error(`Unknown task type: ${task.type}`);
      }
      
      // Update task status based on result
      const status = result.success ? 'completed' : 'failed';
      const updateData = {
        metadata: {
          ...task.metadata,
          execution_result: result
        }
      };
      
      return await this.updateTaskStatus(task.id, status, updateData);
    } catch (error) {
      console.error(`Error executing task ${task.id}:`, error);
      
      // Update task status to failed
      await this.updateTaskStatus(task.id, 'failed', {
        metadata: {
          ...task.metadata,
          execution_error: error.message
        }
      });
      
      throw new Error(`Failed to execute task: ${error.message}`);
    }
  }

  /**
   * Execute scheduled tasks that are due
   * 
   * @returns {Promise<Array>} Results of executed tasks
   */
  async executeScheduledTasks() {
    try {
      const tasks = await this.getScheduledTasks();
      const results = [];
      
      for (const task of tasks) {
        if (task.auto_executable) {
          try {
            const result = await this.executeTask(task);
            results.push({
              taskId: task.id,
              success: true,
              result
            });
          } catch (error) {
            results.push({
              taskId: task.id,
              success: false,
              error: error.message
            });
          }
        }
      }
      
      return results;
    } catch (error) {
      console.error('Error executing scheduled tasks:', error);
      throw new Error(`Failed to execute scheduled tasks: ${error.message}`);
    }
  }

  /**
   * Generate maintenance recommendations based on system health
   * 
   * @param {Object} systemHealth - System health data
   * @returns {Promise<Array>} List of recommendations
   */
  async generateRecommendations(systemHealth) {
    try {
      const recommendations = [];
      
      // Check shift management health
      if (systemHealth.shifts && systemHealth.shifts.status !== 'operational') {
        recommendations.push({
          type: 'maintenance',
          priority: systemHealth.shifts.status === 'critical' ? 'high' : 'medium',
          title: 'Fix shift management issues',
          description: `${systemHealth.shifts.issues.length} issues detected in shift management module`,
          autoExecutable: true,
          metadata: {
            module: 'shifts',
            issues: systemHealth.shifts.issues
          }
        });
      }
      
      // Check assignment management health
      if (systemHealth.assignments && systemHealth.assignments.status !== 'operational') {
        recommendations.push({
          type: 'maintenance',
          priority: systemHealth.assignments.status === 'critical' ? 'high' : 'medium',
          title: 'Fix assignment management issues',
          description: `${systemHealth.assignments.issues.length} issues detected in assignment management module`,
          autoExecutable: true,
          metadata: {
            module: 'assignments',
            issues: systemHealth.assignments.issues
          }
        });
      }
      
      // Check trip monitoring health
      if (systemHealth.trips && systemHealth.trips.status !== 'operational') {
        recommendations.push({
          type: 'maintenance',
          priority: systemHealth.trips.status === 'critical' ? 'high' : 'medium',
          title: 'Fix trip monitoring issues',
          description: `${systemHealth.trips.issues.length} issues detected in trip monitoring module`,
          autoExecutable: true,
          metadata: {
            module: 'trips',
            issues: systemHealth.trips.issues
          }
        });
      }
      
      // Add periodic maintenance recommendation if not already scheduled
      const existingMaintenanceTasks = await this.getTasks({
        type: 'maintenance',
        status: 'pending'
      }, 10);
      
      if (existingMaintenanceTasks.length === 0) {
        recommendations.push({
          type: 'maintenance',
          priority: 'low',
          title: 'Schedule regular system maintenance',
          description: 'Perform routine system maintenance to ensure optimal performance',
          autoExecutable: false
        });
      }
      
      return recommendations;
    } catch (error) {
      console.error('Error generating recommendations:', error);
      throw new Error(`Failed to generate recommendations: ${error.message}`);
    }
  }

  /**
   * Log system health status
   * 
   * @param {string} module - Module name
   * @param {string} status - Health status (operational, warning, critical)
   * @param {Array} issues - List of detected issues
   * @param {Object} metrics - Performance metrics
   * @returns {Promise<Object>} Created log entry
   */
  async logSystemHealth(module, status, issues = [], metrics = {}) {
    try {
      const query = `
        INSERT INTO system_health_logs (module, status, issues, metrics)
        VALUES ($1, $2, $3, $4)
        RETURNING *
      `;
      
      const values = [module, status, JSON.stringify(issues), JSON.stringify(metrics)];
      const result = await this.pool.query(query, values);
      
      return result.rows[0];
    } catch (error) {
      console.error(`Error logging system health for ${module}:`, error);
      throw new Error(`Failed to log system health: ${error.message}`);
    }
  }

  /**
   * Get recent system health logs
   * 
   * @param {string} [module] - Filter by module name
   * @param {number} [limit=20] - Maximum number of logs to return
   * @returns {Promise<Array>} List of health logs
   */
  async getSystemHealthLogs(module, limit = 20) {
    try {
      let query = 'SELECT * FROM system_health_logs';
      const values = [];
      
      if (module) {
        query += ' WHERE module = $1';
        values.push(module);
      }
      
      query += ` ORDER BY checked_at DESC LIMIT $${values.length + 1}`;
      values.push(limit);
      
      const result = await this.pool.query(query, values);
      return result.rows;
    } catch (error) {
      console.error('Error getting system health logs:', error);
      throw new Error(`Failed to get system health logs: ${error.message}`);
    }
  }

  /**
   * Execute a maintenance task
   * @private
   */
  async executeMaintenanceTask(task) {
    // This would integrate with SystemMonitoringService and AutomatedFixService
    // For now, return a placeholder result
    return {
      success: true,
      message: `Executed maintenance task: ${task.title}`,
      details: {
        taskId: task.id,
        executedAt: new Date().toISOString()
      }
    };
  }

  /**
   * Execute a cleanup task
   * @private
   */
  async executeCleanupTask(task) {
    // This would integrate with CleanupService
    // For now, return a placeholder result
    return {
      success: true,
      message: `Executed cleanup task: ${task.title}`,
      details: {
        taskId: task.id,
        executedAt: new Date().toISOString()
      }
    };
  }

  /**
   * Execute a monitoring task
   * @private
   */
  async executeMonitoringTask(task) {
    // This would integrate with SystemMonitoringService
    // For now, return a placeholder result
    return {
      success: true,
      message: `Executed monitoring task: ${task.title}`,
      details: {
        taskId: task.id,
        executedAt: new Date().toISOString()
      }
    };
  }

  /**
   * Execute an optimization task
   * @private
   */
  async executeOptimizationTask(task) {
    // This would integrate with optimization services
    // For now, return a placeholder result
    return {
      success: true,
      message: `Executed optimization task: ${task.title}`,
      details: {
        taskId: task.id,
        executedAt: new Date().toISOString()
      }
    };
  }
}

module.exports = TaskManagementService;