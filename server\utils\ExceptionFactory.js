/**
 * Unified Exception Factory
 * 
 * This module provides a simplified, unified approach to exception handling
 * in the Hauling QR Trip System. It replaces the complex, scattered exception
 * logic with a clean, consistent interface.
 * 
 * Key Features:
 * - Unified exception creation for all types
 * - Consistent transaction management
 * - Simplified API with clear error handling
 * - Performance optimized (target: <300ms)
 * - Maintains data integrity and audit trails
 */

const { getClient } = require('../config/database');
const { notifyExceptionCreated } = require('../websocket');
const { logger } = require('./logger');

/**
 * Exception Types
 */
const EXCEPTION_TYPES = {
  ROUTE_DEVIATION: 'route_deviation',
  UNASSIGNED_TRIP: 'unassigned_trip',
  LOCATION_MISMATCH: 'location_mismatch',
  ASSIGNMENT_CONFLICT: 'assignment_conflict',
  MANUAL_OVERRIDE: 'manual_override'
};

/**
 * Exception Severity Levels
 */
const SEVERITY_LEVELS = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical'
};

/**
 * Trip States
 */
const TRIP_STATES = {
  ASSIGNED: 'assigned',
  LOADING_START: 'loading_start',
  LOADING_END: 'loading_end',
  UNLOADING_START: 'unloading_start',
  UNLOADING_END: 'unloading_end',
  TRIP_COMPLETED: 'trip_completed',
  EXCEPTION_PENDING: 'exception_pending',
  CANCELLED: 'cancelled'
};

/**
 * Unified Exception Factory Class
 */
class ExceptionFactory {
  constructor() {
    this.logger = logger;
  }

  /**
   * Create a route deviation exception
   * @param {Object} params - Exception parameters
   * @param {Object} params.truck - Truck information
   * @param {Object} params.expectedLocation - Expected location
   * @param {Object} params.actualLocation - Actual scanned location
   * @param {Object} params.assignment - Current assignment
   * @param {number} params.userId - User ID who triggered the exception
   * @param {Object} params.client - Database client (optional, will create if not provided)
   * @returns {Promise<Object>} Exception creation result
   */
  async createRouteDeviationException(params) {
    const {
      truck,
      expectedLocation,
      actualLocation,
      assignment,
      userId,
      client: providedClient
    } = params;

    const client = providedClient || await getClient();
    const shouldCommit = !providedClient;

    try {
      if (shouldCommit) await client.query('BEGIN');

      // Create trip with exception status
      const tripNumber = await this._getNextTripNumber(client, assignment.id);
      
      const tripResult = await client.query(`
        INSERT INTO trip_logs (
          assignment_id, trip_number, status, loading_start_time,
          actual_loading_location_id, is_exception, exception_reason,
          notes, created_at, updated_at
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
        RETURNING *
      `, [
        assignment.id,
        tripNumber,
        TRIP_STATES.EXCEPTION_TRIGGERED, // Changed from EXCEPTION_PENDING
        new Date(),
        actualLocation.id,
        true,
        `Route deviation: Loading at ${actualLocation.name} instead of assigned ${expectedLocation.name}`,
        JSON.stringify({
          exception_type: EXCEPTION_TYPES.ROUTE_DEVIATION,
          expected_location: {
            id: expectedLocation.id,
            name: expectedLocation.name,
            code: expectedLocation.location_code
          },
          actual_location: {
            id: actualLocation.id,
            name: actualLocation.name,
            code: actualLocation.location_code
          },
          original_assignment_id: assignment.id,
          created_by: userId,
          created_at: new Date().toISOString()
        }),
        new Date(),
        new Date()
      ]);

      const trip = tripResult.rows[0];

      // Create approval request
      const approvalResult = await client.query(`
        INSERT INTO approvals (
          trip_log_id, exception_type, exception_description,
          severity, reported_by, status, requested_at,
          created_at, updated_at
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
        RETURNING *
      `, [
        trip.id,
        EXCEPTION_TYPES.ROUTE_DEVIATION,
        `Truck ${truck.truck_number} loading at ${actualLocation.name} instead of assigned ${expectedLocation.name}`,
        SEVERITY_LEVELS.MEDIUM,
        userId,
        'pending',
        new Date(),
        new Date(),
        new Date()
      ]);

      const approval = approvalResult.rows[0];

      if (shouldCommit) await client.query('COMMIT');

      // Send real-time notification
      this._sendExceptionNotification({
        trip,
        approval,
        truck,
        actualLocation,
        expectedLocation,
        type: EXCEPTION_TYPES.ROUTE_DEVIATION
      });

      this.logger.info('Route deviation exception created', {
        trip_id: trip.id,
        approval_id: approval.id,
        truck_number: truck.truck_number,
        expected_location: expectedLocation.name,
        actual_location: actualLocation.name
      });

      return {
        success: true,
        trip,
        approval,
        message: `Route deviation detected! Loading at ${actualLocation.name} instead of assigned ${expectedLocation.name}. Approval required.`,
        next_step: 'await_approval'
      };

    } catch (error) {
      if (shouldCommit) await client.query('ROLLBACK');
      this.logger.error('Failed to create route deviation exception', {
        error: error.message,
        truck_number: truck?.truck_number,
        expected_location: expectedLocation?.name,
        actual_location: actualLocation?.name
      });
      throw error;
    } finally {
      if (shouldCommit && client) {
        client.release();
      }
    }
  }

  /**
   * Create an unassigned trip exception
   * @param {Object} params - Exception parameters
   * @param {Object} params.truck - Truck information
   * @param {Object} params.location - Scanned location
   * @param {number} params.userId - User ID who triggered the exception
   * @param {Object} params.client - Database client (optional)
   * @returns {Promise<Object>} Exception creation result
   */
  async createUnassignedTripException(params) {
    const {
      truck,
      location,
      userId,
      client: providedClient
    } = params;

    const client = providedClient || await getClient();
    const shouldCommit = !providedClient;

    try {
      if (shouldCommit) await client.query('BEGIN');

      // Check for historical assignments to use as template
      const historyResult = await client.query(`
        SELECT DISTINCT a.loading_location_id, a.unloading_location_id,
               ll.name as loading_location, ul.name as unloading_location,
               a.updated_at
        FROM assignments a
        JOIN locations ll ON a.loading_location_id = ll.id
        JOIN locations ul ON a.unloading_location_id = ul.id
        WHERE a.truck_id = $1 AND a.status = 'completed'
        ORDER BY a.updated_at DESC
        LIMIT 1
      `, [truck.id]);

      let proposedAssignment;
      let severity = SEVERITY_LEVELS.HIGH;

      if (historyResult.rows.length > 0) {
        // Use historical assignment as template
        const history = historyResult.rows[0];
        proposedAssignment = {
          loading_location_id: location.id,
          unloading_location_id: history.unloading_location_id,
          loading_location: location.name,
          unloading_location: history.unloading_location
        };
        severity = SEVERITY_LEVELS.MEDIUM;
      } else {
        // No history - requires manual intervention
        proposedAssignment = {
          loading_location_id: location.id,
          unloading_location_id: null,
          loading_location: location.name,
          unloading_location: 'To be determined'
        };
        severity = SEVERITY_LEVELS.HIGH;
      }

      // Create temporary assignment for the exception
      const tempAssignmentResult = await client.query(`
        INSERT INTO assignments (
          assignment_code, truck_id, driver_id, loading_location_id, 
          unloading_location_id, assigned_date, status, notes
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        RETURNING *
      `, [
        `TEMP-${Date.now()}-${truck.truck_number}`,
        truck.id,
        1, // Default driver - will be updated during approval
        proposedAssignment.loading_location_id,
        proposedAssignment.unloading_location_id,
        new Date().toISOString().split('T')[0],
        'pending_approval',
        JSON.stringify({
          is_temporary: true,
          created_for_exception: true,
          requires_admin_approval: true
        })
      ]);

      const tempAssignment = tempAssignmentResult.rows[0];

      // Create trip with exception status
      const tripNumber = await this._getNextTripNumber(client, tempAssignment.id);
      
      const tripResult = await client.query(`
        INSERT INTO trip_logs (
          assignment_id, trip_number, status, loading_start_time,
          actual_loading_location_id, is_exception, exception_reason,
          notes, created_at, updated_at
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
        RETURNING *
      `, [
        tempAssignment.id,
        tripNumber,
        TRIP_STATES.EXCEPTION_PENDING,
        new Date(),
        location.id,
        true,
        `Unassigned trip: Truck ${truck.truck_number} at ${location.name} without current assignment`,
        JSON.stringify({
          exception_type: EXCEPTION_TYPES.UNASSIGNED_TRIP,
          scanned_location: {
            id: location.id,
            name: location.name,
            code: location.location_code
          },
          proposed_assignment: proposedAssignment,
          has_history: historyResult.rows.length > 0,
          created_by: userId,
          created_at: new Date().toISOString()
        }),
        new Date(),
        new Date()
      ]);

      const trip = tripResult.rows[0];

      // Create approval request
      const approvalResult = await client.query(`
        INSERT INTO approvals (
          trip_log_id, exception_type, exception_description,
          severity, reported_by, status, requested_at,
          created_at, updated_at
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
        RETURNING *
      `, [
        trip.id,
        EXCEPTION_TYPES.UNASSIGNED_TRIP,
        `Truck ${truck.truck_number} scanned at ${location.name} without current assignment. ${historyResult.rows.length > 0 ? 'Proposed route based on history.' : 'Manual assignment required.'}`,
        severity,
        userId,
        'pending',
        new Date(),
        new Date(),
        new Date()
      ]);

      const approval = approvalResult.rows[0];

      if (shouldCommit) await client.query('COMMIT');

      // Send real-time notification
      this._sendExceptionNotification({
        trip,
        approval,
        truck,
        actualLocation: location,
        type: EXCEPTION_TYPES.UNASSIGNED_TRIP
      });

      this.logger.info('Unassigned trip exception created', {
        trip_id: trip.id,
        approval_id: approval.id,
        truck_number: truck.truck_number,
        location: location.name,
        has_history: historyResult.rows.length > 0
      });

      return {
        success: true,
        trip,
        approval,
        tempAssignment,
        message: `Unassigned trip detected! Truck ${truck.truck_number} at ${location.name} without current assignment. ${historyResult.rows.length > 0 ? 'Proposed route based on history.' : 'Manual assignment required.'} Approval needed.`,
        next_step: 'await_approval'
      };

    } catch (error) {
      if (shouldCommit) await client.query('ROLLBACK');
      this.logger.error('Failed to create unassigned trip exception', {
        error: error.message,
        truck_number: truck?.truck_number,
        location: location?.name
      });
      throw error;
    } finally {
      if (shouldCommit && client) {
        client.release();
      }
    }
  }

  /**
   * Get next globally unique trip number
   * @private
   */
  async _getNextTripNumber(client, assignmentId) {
    // Simple approach: get next available trip number globally
    const result = await client.query(`
      SELECT COALESCE(MAX(trip_number), 0) + 1 as next_number
      FROM trip_logs
    `);
    return result.rows[0].next_number;
  }

  /**
   * Send exception notification
   * @private
   */
  _sendExceptionNotification(data) {
    try {
      notifyExceptionCreated({
        id: data.trip.id,
        exception_type: data.type,
        exception_description: data.approval.exception_description,
        severity: data.approval.severity,
        message: data.approval.exception_description,
        truck_id: data.truck.id,
        location_id: data.actualLocation.id,
        trip_log_id: data.trip.id,
        data: {
          truck: {
            id: data.truck.id,
            number: data.truck.truck_number,
            license_plate: data.truck.license_plate
          },
          location: {
            id: data.actualLocation.id,
            code: data.actualLocation.location_code,
            name: data.actualLocation.name,
            type: data.actualLocation.type
          },
          expected_location: data.expectedLocation ? {
            id: data.expectedLocation.id,
            code: data.expectedLocation.location_code,
            name: data.expectedLocation.name,
            type: data.expectedLocation.type
          } : null
        }
      });
    } catch (error) {
      this.logger.error('Failed to send exception notification', {
        error: error.message,
        trip_id: data.trip.id
      });
    }
  }
}

// Export singleton instance
const exceptionFactory = new ExceptionFactory();

module.exports = {
  ExceptionFactory,
  exceptionFactory,
  EXCEPTION_TYPES,
  SEVERITY_LEVELS,
  TRIP_STATES
};
