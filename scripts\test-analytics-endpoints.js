/**
 * Test Analytics Endpoints
 * 
 * This script tests the analytics endpoints to see what data they return
 */

const { getApiBaseUrl } = require('../client/src/utils/network-utils');

async function testAnalyticsEndpoints() {
  console.log('🧪 Testing Analytics Endpoints...\n');

  const apiUrl = 'http://localhost:5000/api'; // Direct API URL
  const token = 'test-token'; // We'll need to get a real token

  const endpoints = [
    '/analytics/trip-performance',
    '/analytics/stopped-analytics', 
    '/analytics/truck-rankings'
  ];

  for (const endpoint of endpoints) {
    console.log(`Testing ${endpoint}...`);
    
    try {
      const response = await fetch(`${apiUrl}${endpoint}?start_date=2025-08-01&end_date=2025-08-31`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log(`  Status: ${response.status}`);
      
      if (response.ok) {
        const data = await response.json();
        console.log(`  Success: ${data.success}`);
        console.log(`  Data keys: ${Object.keys(data.data || {}).join(', ')}`);
        
        // Show specific data for each endpoint
        if (endpoint.includes('trip-performance')) {
          const routePatterns = data.data?.routePatterns || [];
          const locationPerformance = data.data?.locationPerformance || [];
          console.log(`  Route patterns: ${routePatterns.length}`);
          console.log(`  Location performance: ${locationPerformance.length}`);
        } else if (endpoint.includes('truck-rankings')) {
          const rankings = data.data?.rankings || [];
          console.log(`  Rankings: ${rankings.length}`);
        } else if (endpoint.includes('stopped-analytics')) {
          const frequency = data.data?.frequency || [];
          console.log(`  Stopped frequency: ${frequency.length}`);
        }
      } else {
        const errorText = await response.text();
        console.log(`  Error: ${errorText}`);
      }
    } catch (error) {
      console.log(`  Network Error: ${error.message}`);
    }
    
    console.log('');
  }
}

// For Node.js environment, we need to use node-fetch
const fetch = require('node-fetch');

if (require.main === module) {
  testAnalyticsEndpoints().then(() => {
    console.log('✅ Endpoint testing complete!');
  }).catch(error => {
    console.error('❌ Error:', error);
  });
}