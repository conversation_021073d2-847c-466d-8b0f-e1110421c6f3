import React from 'react';

const DashboardStats = ({ data, loading }) => {
  if (loading) {
    return (
      <div className="animate-pulse space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(8)].map((_, i) => (
            <div key={i} className="bg-secondary-200 h-24 rounded-lg"></div>
          ))}
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="bg-secondary-200 h-64 rounded-lg"></div>
          ))}
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="text-center py-12">
        <svg className="w-12 h-12 text-secondary-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
        <h3 className="text-lg font-medium text-secondary-900 mb-2">No Data Available</h3>
        <p className="text-secondary-500">Analytics data will appear here once there are completed trips.</p>
      </div>
    );
  }

  // Use REAL data from API instead of mock data
  const stats = {
    fleet: {
      totalTrucks: data.fleet?.totalTrucks || 0,
      activeTrucks: data.fleet?.activeTrucks || 0,
      totalDrivers: data.fleet?.totalDrivers || 0,
      activeDrivers: data.fleet?.activeDrivers || 0,
      totalLocations: data.fleet?.totalLocations || 0,
      activeLocations: data.fleet?.activeLocations || 0,
      totalAssignments: data.fleet?.totalAssignments || 0,
      activeAssignments: data.fleet?.activeAssignments || 0
    },
    trips: {
      todayTrips: data.trips?.todayTrips || 0,
      todayCompleted: data.trips?.todayCompleted || 0,
      weeklyTrips: data.trips?.weeklyTrips || 0,
      weeklyCompleted: data.trips?.weeklyCompleted || 0,
      monthlyTrips: data.trips?.monthlyTrips || 0,
      monthlyCompleted: data.trips?.monthlyCompleted || 0,
      totalTrips: data.trips?.totalTrips || 0,
      totalCompleted: data.trips?.totalCompleted || 0
    },
    performance: {
      avgTripTime: data.performance?.avgTripTime || 0,
      avgLoadingTime: data.performance?.avgLoadingTime || 0,
      avgTravelTime: data.performance?.avgTravelTime || 0,
      avgUnloadingTime: data.performance?.avgUnloadingTime || 0,
      completionRate: parseFloat(data.performance?.completionRate || 0),
      onTimeRate: parseFloat(data.performance?.onTimeRate || 0),
      exceptionRate: parseFloat(data.performance?.exceptionRate || 0)
    }
  };

  const StatCard = ({ title, value, change, changeType, icon, subtitle }) => (
    <div className="bg-white border border-secondary-200 rounded-lg p-6">
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p className="text-sm font-medium text-secondary-600">{title}</p>
          <p className="text-2xl font-bold text-secondary-900">{value}</p>
          {subtitle && (
            <p className="text-sm text-secondary-500 mt-1">{subtitle}</p>
          )}
          {change && (
            <div className={`flex items-center mt-2 text-sm ${
              changeType === 'positive' ? 'text-green-600' : 
              changeType === 'negative' ? 'text-red-600' : 'text-secondary-600'
            }`}>
              {changeType === 'positive' && (
                <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z" clipRule="evenodd" />
                </svg>
              )}
              {changeType === 'negative' && (
                <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M14.707 10.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L9 12.586V5a1 1 0 012 0v7.586l2.293-2.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              )}
              <span>{change}</span>
            </div>
          )}
        </div>
        <div className="text-3xl">{icon}</div>
      </div>
    </div>
  );

  // Calculate percentage changes (basic calculation for demo)
  const getPercentageChange = (current, total) => {
    if (!total || total === 0) return null;
    const percentage = ((current / total) * 100).toFixed(1);
    return `${percentage}% of total`;
  };

  return (
    <div className="space-y-6">
      {/* Fleet Overview - REAL DATA */}
      <div>
        <h3 className="text-lg font-medium text-secondary-900 mb-4">Fleet Overview</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <StatCard
            title="Total Trucks"
            value={stats.fleet.totalTrucks}
            subtitle={`${stats.fleet.activeTrucks} active`}
            icon="🚛"
            change={getPercentageChange(stats.fleet.activeTrucks, stats.fleet.totalTrucks)}
          />
          <StatCard
            title="Total Drivers"
            value={stats.fleet.totalDrivers}
            subtitle={`${stats.fleet.activeDrivers} active`}
            icon="👨‍💼"
            change={getPercentageChange(stats.fleet.activeDrivers, stats.fleet.totalDrivers)}
          />
          <StatCard
            title="Locations"
            value={stats.fleet.totalLocations}
            subtitle={`${stats.fleet.activeLocations} active`}
            icon="📍"
            change={getPercentageChange(stats.fleet.activeLocations, stats.fleet.totalLocations)}
          />
          <StatCard
            title="Active Assignments"
            value={stats.fleet.activeAssignments}
            subtitle={`${stats.fleet.totalAssignments} total`}
            icon="📋"
            change={getPercentageChange(stats.fleet.activeAssignments, stats.fleet.totalAssignments)}
          />
        </div>
      </div>

      {/* Trip Statistics - REAL DATA */}
      <div>
        <h3 className="text-lg font-medium text-secondary-900 mb-4">Trip Statistics</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <StatCard
            title="Today's Trips"
            value={stats.trips.todayTrips}
            subtitle={`${stats.trips.todayCompleted} completed`}
            icon="📊"
            change={getPercentageChange(stats.trips.todayCompleted, stats.trips.todayTrips)}
          />
          <StatCard
            title="This Week"
            value={stats.trips.weeklyTrips}
            subtitle={`${stats.trips.weeklyCompleted} completed`}
            icon="📈"
            change={getPercentageChange(stats.trips.weeklyCompleted, stats.trips.weeklyTrips)}
          />
          <StatCard
            title="This Month"
            value={stats.trips.monthlyTrips}
            subtitle={`${stats.trips.monthlyCompleted} completed`}
            icon="📅"
            change={getPercentageChange(stats.trips.monthlyCompleted, stats.trips.monthlyTrips)}
          />
          <StatCard
            title="Total Trips"
            value={stats.trips.totalTrips.toLocaleString()}
            subtitle={`${stats.trips.totalCompleted.toLocaleString()} completed`}
            icon="🏁"
            change={getPercentageChange(stats.trips.totalCompleted, stats.trips.totalTrips)}
          />
        </div>
      </div>

      {/* Performance Metrics - REAL DATA */}
      <div>
        <h3 className="text-lg font-medium text-secondary-900 mb-4">Performance Metrics</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <StatCard
            title="Avg Trip Time"
            value={stats.performance.avgTripTime > 0 ? 
              `${Math.floor(stats.performance.avgTripTime / 60)}h ${stats.performance.avgTripTime % 60}m` : 
              'No data'}
            subtitle="Total cycle time"
            icon="⏱️"
          />
          <StatCard
            title="Completion Rate"
            value={`${stats.performance.completionRate}%`}
            subtitle="Trips completed successfully"
            icon="✅"
            changeType={stats.performance.completionRate >= 90 ? 'positive' : 'negative'}
          />
          <StatCard
            title="On-Time Rate"
            value={`${stats.performance.onTimeRate}%`}
            subtitle="Trips completed on schedule"
            icon="🎯"
            changeType={stats.performance.onTimeRate >= 85 ? 'positive' : 'negative'}
          />
          <StatCard
            title="Exception Rate"
            value={`${stats.performance.exceptionRate}%`}
            subtitle="Trips requiring attention"
            icon="⚠️"
            changeType={stats.performance.exceptionRate <= 10 ? 'positive' : 'negative'}
          />
        </div>
      </div>

      {/* Operational Efficiency - REAL DATA */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Time Analysis */}
        <div className="bg-white border border-secondary-200 rounded-lg p-6">
          <h4 className="text-lg font-medium text-secondary-900 mb-4">Average Time Analysis</h4>
          {stats.performance.avgTripTime > 0 ? (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-secondary-700">Loading Time</span>
                <div className="flex items-center">
                  <div className="w-32 bg-secondary-200 rounded-full h-2 mr-3">
                    <div 
                      className="bg-green-500 h-2 rounded-full" 
                      style={{ width: `${(stats.performance.avgLoadingTime / stats.performance.avgTripTime) * 100}%` }}
                    ></div>
                  </div>
                  <span className="text-sm text-secondary-900 font-medium">
                    {stats.performance.avgLoadingTime}m
                  </span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-secondary-700">Travel Time</span>
                <div className="flex items-center">
                  <div className="w-32 bg-secondary-200 rounded-full h-2 mr-3">
                    <div 
                      className="bg-blue-500 h-2 rounded-full" 
                      style={{ width: `${(stats.performance.avgTravelTime / stats.performance.avgTripTime) * 100}%` }}
                    ></div>
                  </div>
                  <span className="text-sm text-secondary-900 font-medium">
                    {stats.performance.avgTravelTime}m
                  </span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-secondary-700">Unloading Time</span>
                <div className="flex items-center">
                  <div className="w-32 bg-secondary-200 rounded-full h-2 mr-3">
                    <div 
                      className="bg-orange-500 h-2 rounded-full" 
                      style={{ width: `${(stats.performance.avgUnloadingTime / stats.performance.avgTripTime) * 100}%` }}
                    ></div>
                  </div>
                  <span className="text-sm text-secondary-900 font-medium">
                    {stats.performance.avgUnloadingTime}m
                  </span>
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center py-8 text-secondary-500">
              <span className="text-4xl block mb-2">⏱️</span>
              No time analysis data available
            </div>
          )}
        </div>

        {/* System Summary */}
        <div className="bg-white border border-secondary-200 rounded-lg p-6">
          <h4 className="text-lg font-medium text-secondary-900 mb-4">System Summary</h4>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-secondary-700">Fleet Utilization</span>
              <div className="flex items-center">
                <div className="w-32 bg-secondary-200 rounded-full h-2 mr-3">
                  <div 
                    className="bg-primary-500 h-2 rounded-full" 
                    style={{ width: `${(stats.fleet.activeTrucks / Math.max(stats.fleet.totalTrucks, 1)) * 100}%` }}
                  ></div>
                </div>
                <span className="text-sm text-secondary-900 font-medium">
                  {Math.round((stats.fleet.activeTrucks / Math.max(stats.fleet.totalTrucks, 1)) * 100)}%
                </span>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-secondary-700">Driver Utilization</span>
              <div className="flex items-center">
                <div className="w-32 bg-secondary-200 rounded-full h-2 mr-3">
                  <div 
                    className="bg-success-500 h-2 rounded-full" 
                    style={{ width: `${(stats.fleet.activeDrivers / Math.max(stats.fleet.totalDrivers, 1)) * 100}%` }}
                  ></div>
                </div>
                <span className="text-sm text-secondary-900 font-medium">
                  {Math.round((stats.fleet.activeDrivers / Math.max(stats.fleet.totalDrivers, 1)) * 100)}%
                </span>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-secondary-700">Trip Completion</span>
              <div className="flex items-center">
                <div className="w-32 bg-secondary-200 rounded-full h-2 mr-3">
                  <div 
                    className="bg-green-500 h-2 rounded-full" 
                    style={{ width: `${stats.performance.completionRate}%` }}
                  ></div>
                </div>
                <span className="text-sm text-secondary-900 font-medium">
                  {stats.performance.completionRate}%
                </span>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-secondary-700">On-Time Performance</span>
              <div className="flex items-center">
                <div className="w-32 bg-secondary-200 rounded-full h-2 mr-3">
                  <div 
                    className="bg-blue-500 h-2 rounded-full" 
                    style={{ width: `${stats.performance.onTimeRate}%` }}
                  ></div>
                </div>
                <span className="text-sm text-secondary-900 font-medium">
                  {stats.performance.onTimeRate}%
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Data Summary */}
      <div className="bg-info-50 border border-info-200 rounded-lg p-6">
        <h4 className="text-lg font-medium text-info-900 mb-4">📊 Live Data Summary</h4>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
          <div>
            <div className="text-2xl font-bold text-info-900">{stats.fleet.totalTrucks}</div>
            <div className="text-sm text-info-700">Total Trucks</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-info-900">{stats.fleet.activeDrivers}</div>
            <div className="text-sm text-info-700">Active Drivers</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-info-900">{stats.fleet.totalLocations}</div>
            <div className="text-sm text-info-700">Locations</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-info-900">{stats.trips.todayTrips}</div>
            <div className="text-sm text-info-700">Today's Trips</div>
          </div>
        </div>
        <p className="text-sm text-info-700 mt-4 text-center">
          ✅ All data is fetched live from the database • Last updated: {new Date().toLocaleTimeString()}
        </p>
      </div>
    </div>
  );
};

export default DashboardStats;