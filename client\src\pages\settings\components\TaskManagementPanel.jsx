import React, { useState, useEffect, useCallback } from 'react';
import { getApiBaseUrl } from '../../../utils/network-utils';

const TaskManagementPanel = () => {
  const [tasks, setTasks] = useState([]);
  const [recommendations, setRecommendations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState('tasks'); // 'tasks' or 'recommendations'
  const [newTask, setNewTask] = useState({
    type: 'maintenance',
    priority: 'medium',
    title: '',
    description: '',
    scheduledFor: '',
    autoExecutable: false
  });
  const [filters, setFilters] = useState({
    type: '',
    priority: '',
    status: ''
  });

  // Fetch tasks
  const fetchTasks = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const apiUrl = getApiBaseUrl();
      const queryParams = new URLSearchParams();

      if (filters.type) queryParams.append('type', filters.type);
      if (filters.priority) queryParams.append('priority', filters.priority);
      if (filters.status) queryParams.append('status', filters.status);

      const response = await fetch(`${apiUrl}/tasks?${queryParams.toString()}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('hauling_token')}`
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch tasks: ${response.statusText}`);
      }

      const result = await response.json();
      setTasks(result.data || []);
    } catch (err) {
      console.error('Error fetching tasks:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, [filters]);

  // Fetch recommendations
  const fetchRecommendations = async () => {
    try {
      setLoading(true);
      setError(null);

      const apiUrl = getApiBaseUrl();
      const response = await fetch(`${apiUrl}/tasks/recommendations`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('hauling_token')}`
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch recommendations: ${response.statusText}`);
      }

      const result = await response.json();
      setRecommendations(result.data || []);
    } catch (err) {
      console.error('Error fetching recommendations:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Create task
  const createTask = async (e) => {
    e.preventDefault();

    try {
      setLoading(true);
      setError(null);

      const apiUrl = getApiBaseUrl();
      const response = await fetch(`${apiUrl}/tasks`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('hauling_token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(newTask)
      });

      if (!response.ok) {
        throw new Error(`Failed to create task: ${response.statusText}`);
      }

      // Reset form
      setNewTask({
        type: 'maintenance',
        priority: 'medium',
        title: '',
        description: '',
        scheduledFor: '',
        autoExecutable: false
      });

      // Refresh tasks
      fetchTasks();

      // Show success message
      alert('✅ Task created successfully!');
    } catch (err) {
      console.error('Error creating task:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Update task status
  const updateTaskStatus = async (taskId, newStatus) => {
    try {
      setLoading(true);
      setError(null);

      const apiUrl = getApiBaseUrl();
      const response = await fetch(`${apiUrl}/tasks/${taskId}/status`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('hauling_token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ status: newStatus })
      });

      if (!response.ok) {
        throw new Error(`Failed to update task status: ${response.statusText}`);
      }

      // Refresh tasks
      fetchTasks();
    } catch (err) {
      console.error('Error updating task status:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Execute task
  const executeTask = async (taskId) => {
    try {
      setLoading(true);
      setError(null);

      const apiUrl = getApiBaseUrl();
      const response = await fetch(`${apiUrl}/tasks/${taskId}/execute`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('hauling_token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to execute task: ${response.statusText}`);
      }

      const result = await response.json();

      // Show success message
      alert(`✅ Task executed successfully!\n\n${result.data?.message || 'Task completed'}`);

      // Refresh tasks
      fetchTasks();
    } catch (err) {
      console.error('Error executing task:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Delete task
  const deleteTask = async (taskId) => {
    if (!window.confirm('Are you sure you want to delete this task?')) {
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const apiUrl = getApiBaseUrl();
      const response = await fetch(`${apiUrl}/tasks/${taskId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('hauling_token')}`
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to delete task: ${response.statusText}`);
      }

      // Refresh tasks
      fetchTasks();

      // Show success message
      alert('✅ Task deleted successfully!');
    } catch (err) {
      console.error('Error deleting task:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Create task from recommendation
  const createTaskFromRecommendation = async (recommendation) => {
    try {
      setLoading(true);
      setError(null);

      const apiUrl = getApiBaseUrl();
      const response = await fetch(`${apiUrl}/tasks`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('hauling_token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(recommendation)
      });

      if (!response.ok) {
        throw new Error(`Failed to create task: ${response.statusText}`);
      }

      // Refresh tasks and recommendations
      fetchTasks();
      fetchRecommendations();

      // Show success message
      alert('✅ Task created from recommendation successfully!');
    } catch (err) {
      console.error('Error creating task from recommendation:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setNewTask(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  // Handle filter changes
  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Apply filters
  const applyFilters = (e) => {
    e.preventDefault();
    fetchTasks();
  };

  // Reset filters
  const resetFilters = () => {
    setFilters({
      type: '',
      priority: '',
      status: ''
    });
    fetchTasks();
  };

  // Get priority color
  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'critical': return 'text-red-600 bg-red-100';
      case 'high': return 'text-orange-600 bg-orange-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-blue-600 bg-blue-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  // Get status color
  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-100';
      case 'in_progress': return 'text-blue-600 bg-blue-100';
      case 'failed': return 'text-red-600 bg-red-100';
      case 'cancelled': return 'text-gray-600 bg-gray-100';
      default: return 'text-yellow-600 bg-yellow-100'; // pending
    }
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString();
  };

  // Load data on component mount
  useEffect(() => {
    fetchTasks();
    fetchRecommendations();
  }, [fetchTasks]);

  return (
    <div className="bg-white rounded-lg shadow border border-secondary-200 mt-6">
      <div className="px-6 py-4 border-b border-secondary-200">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium text-secondary-900">
              📋 Task Management System
            </h3>
            <p className="text-sm text-secondary-500 mt-1">
              Create, manage, and execute maintenance tasks for system health
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => {
                setActiveTab('tasks');
                fetchTasks();
              }}
              className={`px-3 py-1 text-sm font-medium rounded-md ${activeTab === 'tasks'
                  ? 'bg-blue-600 text-white'
                  : 'bg-secondary-100 text-secondary-700 hover:bg-secondary-200'
                }`}
            >
              Tasks
            </button>
            <button
              onClick={() => {
                setActiveTab('recommendations');
                fetchRecommendations();
              }}
              className={`px-3 py-1 text-sm font-medium rounded-md ${activeTab === 'recommendations'
                  ? 'bg-blue-600 text-white'
                  : 'bg-secondary-100 text-secondary-700 hover:bg-secondary-200'
                }`}
            >
              Recommendations
            </button>
          </div>
        </div>
      </div>

      <div className="p-6">
        {error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-600">❌ {error}</p>
          </div>
        )}

        {/* Tasks Tab */}
        {activeTab === 'tasks' && (
          <div>
            {/* Task Filters */}
            <div className="mb-6 bg-secondary-50 p-4 rounded-lg border border-secondary-200">
              <h4 className="text-md font-medium text-secondary-900 mb-3">🔍 Filter Tasks</h4>
              <form onSubmit={applyFilters} className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-1">Type</label>
                  <select
                    name="type"
                    value={filters.type}
                    onChange={handleFilterChange}
                    className="w-full rounded-md border border-secondary-300 shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  >
                    <option value="">All Types</option>
                    <option value="maintenance">Maintenance</option>
                    <option value="cleanup">Cleanup</option>
                    <option value="monitoring">Monitoring</option>
                    <option value="optimization">Optimization</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-1">Priority</label>
                  <select
                    name="priority"
                    value={filters.priority}
                    onChange={handleFilterChange}
                    className="w-full rounded-md border border-secondary-300 shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  >
                    <option value="">All Priorities</option>
                    <option value="critical">Critical</option>
                    <option value="high">High</option>
                    <option value="medium">Medium</option>
                    <option value="low">Low</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-1">Status</label>
                  <select
                    name="status"
                    value={filters.status}
                    onChange={handleFilterChange}
                    className="w-full rounded-md border border-secondary-300 shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  >
                    <option value="">All Statuses</option>
                    <option value="pending">Pending</option>
                    <option value="in_progress">In Progress</option>
                    <option value="completed">Completed</option>
                    <option value="failed">Failed</option>
                    <option value="cancelled">Cancelled</option>
                  </select>
                </div>
                <div className="flex items-end space-x-2">
                  <button
                    type="submit"
                    className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    Apply Filters
                  </button>
                  <button
                    type="button"
                    onClick={resetFilters}
                    className="inline-flex items-center px-3 py-2 border border-secondary-300 text-sm font-medium rounded-md text-secondary-700 bg-white hover:bg-secondary-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    Reset
                  </button>
                </div>
              </form>
            </div>

            {/* Create Task Form */}
            <div className="mb-6 bg-blue-50 p-4 rounded-lg border border-blue-200">
              <h4 className="text-md font-medium text-blue-900 mb-3">➕ Create New Task</h4>
              <form onSubmit={createTask} className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-blue-700 mb-1">Title*</label>
                  <input
                    type="text"
                    name="title"
                    value={newTask.title}
                    onChange={handleInputChange}
                    required
                    className="w-full rounded-md border border-blue-300 shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    placeholder="Task title"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-blue-700 mb-1">Type*</label>
                  <select
                    name="type"
                    value={newTask.type}
                    onChange={handleInputChange}
                    required
                    className="w-full rounded-md border border-blue-300 shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  >
                    <option value="maintenance">Maintenance</option>
                    <option value="cleanup">Cleanup</option>
                    <option value="monitoring">Monitoring</option>
                    <option value="optimization">Optimization</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-blue-700 mb-1">Priority*</label>
                  <select
                    name="priority"
                    value={newTask.priority}
                    onChange={handleInputChange}
                    required
                    className="w-full rounded-md border border-blue-300 shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  >
                    <option value="critical">Critical</option>
                    <option value="high">High</option>
                    <option value="medium">Medium</option>
                    <option value="low">Low</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-blue-700 mb-1">Scheduled For</label>
                  <input
                    type="datetime-local"
                    name="scheduledFor"
                    value={newTask.scheduledFor}
                    onChange={handleInputChange}
                    className="w-full rounded-md border border-blue-300 shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-blue-700 mb-1">Description*</label>
                  <textarea
                    name="description"
                    value={newTask.description}
                    onChange={handleInputChange}
                    required
                    rows={3}
                    className="w-full rounded-md border border-blue-300 shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    placeholder="Task description"
                  />
                </div>
                <div className="md:col-span-2 flex items-center">
                  <input
                    type="checkbox"
                    id="autoExecutable"
                    name="autoExecutable"
                    checked={newTask.autoExecutable}
                    onChange={handleInputChange}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-blue-300 rounded"
                  />
                  <label htmlFor="autoExecutable" className="ml-2 block text-sm text-blue-700">
                    Auto-executable (can be executed automatically)
                  </label>
                </div>
                <div className="md:col-span-2">
                  <button
                    type="submit"
                    disabled={loading}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                  >
                    {loading ? 'Creating...' : 'Create Task'}
                  </button>
                </div>
              </form>
            </div>

            {/* Tasks List */}
            <div>
              <h4 className="text-md font-medium text-secondary-900 mb-3">📋 Tasks</h4>

              {loading && <p className="text-sm text-secondary-500">Loading tasks...</p>}

              {!loading && tasks.length === 0 && (
                <div className="text-center py-6 bg-secondary-50 rounded-lg border border-secondary-200">
                  <p className="text-secondary-500">No tasks found</p>
                </div>
              )}

              {!loading && tasks.length > 0 && (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-secondary-200">
                    <thead className="bg-secondary-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                          Title
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                          Type
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                          Priority
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                          Status
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                          Created
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-secondary-200">
                      {tasks.map((task) => (
                        <tr key={task.id}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-secondary-900">{task.title}</div>
                            <div className="text-sm text-secondary-500">{task.description}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-secondary-100 text-secondary-800">
                              {task.type}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(task.priority)}`}>
                              {task.priority}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(task.status)}`}>
                              {task.status || 'pending'}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-secondary-500">
                            {formatDate(task.created_at)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div className="flex space-x-2">
                              {task.status === 'pending' && (
                                <>
                                  <button
                                    onClick={() => updateTaskStatus(task.id, 'in_progress')}
                                    className="text-blue-600 hover:text-blue-900"
                                  >
                                    Start
                                  </button>
                                  {task.auto_executable && (
                                    <button
                                      onClick={() => executeTask(task.id)}
                                      className="text-green-600 hover:text-green-900"
                                    >
                                      Execute
                                    </button>
                                  )}
                                </>
                              )}
                              {task.status === 'in_progress' && (
                                <>
                                  <button
                                    onClick={() => updateTaskStatus(task.id, 'completed')}
                                    className="text-green-600 hover:text-green-900"
                                  >
                                    Complete
                                  </button>
                                  <button
                                    onClick={() => updateTaskStatus(task.id, 'failed')}
                                    className="text-red-600 hover:text-red-900"
                                  >
                                    Mark Failed
                                  </button>
                                </>
                              )}
                              <button
                                onClick={() => deleteTask(task.id)}
                                className="text-red-600 hover:text-red-900"
                              >
                                Delete
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Recommendations Tab */}
        {activeTab === 'recommendations' && (
          <div>
            <h4 className="text-md font-medium text-secondary-900 mb-3">💡 System Recommendations</h4>

            {loading && <p className="text-sm text-secondary-500">Loading recommendations...</p>}

            {!loading && recommendations.length === 0 && (
              <div className="text-center py-6 bg-secondary-50 rounded-lg border border-secondary-200">
                <p className="text-secondary-500">No recommendations available</p>
              </div>
            )}

            {!loading && recommendations.length > 0 && (
              <div className="space-y-4">
                {recommendations.map((recommendation, index) => (
                  <div key={index} className="bg-white rounded-lg shadow border border-secondary-200 p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(recommendation.priority)}`}>
                            {recommendation.priority}
                          </span>
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-secondary-100 text-secondary-800">
                            {recommendation.type}
                          </span>
                        </div>
                        <h5 className="text-sm font-medium text-secondary-900 mb-1">{recommendation.title}</h5>
                        <p className="text-sm text-secondary-700 mb-3">{recommendation.description}</p>

                        {recommendation.metadata?.module && (
                          <div className="text-xs text-secondary-500 mb-2">
                            Module: {recommendation.metadata.module}
                          </div>
                        )}

                        {recommendation.metadata?.issues?.length > 0 && (
                          <div className="text-xs text-secondary-500">
                            Issues: {recommendation.metadata.issues.length}
                          </div>
                        )}
                      </div>

                      <button
                        onClick={() => createTaskFromRecommendation(recommendation)}
                        className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      >
                        Create Task
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}

            <div className="mt-4 flex justify-end">
              <button
                onClick={fetchRecommendations}
                disabled={loading}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
              >
                {loading ? 'Refreshing...' : '🔄 Refresh Recommendations'}
              </button>
            </div>
          </div>
        )}

        {/* Help Text */}
        <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-md">
          <h5 className="text-sm font-medium text-blue-900 mb-2">ℹ️ About Task Management System:</h5>
          <ul className="text-sm text-blue-700 space-y-1">
            <li>• Create and manage maintenance tasks for system health</li>
            <li>• View system-generated recommendations based on health status</li>
            <li>• Track task status and execution results</li>
            <li>• Schedule tasks for future execution</li>
            <li>• Automatically execute compatible tasks</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default TaskManagementPanel;