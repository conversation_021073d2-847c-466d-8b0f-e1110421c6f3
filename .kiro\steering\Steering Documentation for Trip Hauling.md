# Trip Hauling System Development Guidelines

## Core Development Principles

### QR Code Integration Standards
- Always implement QR code scanning with fallback mechanisms for poor lighting/camera conditions
- Use consistent QR code format: `{location_id}:{timestamp}:{verification_hash}`
- Implement client-side validation before server submission
- Support both camera scanning and manual code entry

### Trip Workflow Management
- Maintain strict state transitions: PENDING → IN_PROGRESS → COMPLETED → VERIFIED
- Implement exception handling for workflow deviations (missed scans, wrong locations)
- Support multi-location workflows (A→B→C extensions, C→B→C cycles)
- Always log state changes with timestamps and user context

### Real-time Communication
- Use WebSocket connections for live dashboard updates
- Implement connection recovery and offline mode handling
- Batch non-critical updates to reduce server load
- Provide immediate user feedback for all actions

### Mobile-First Development
- Design all interfaces for mobile devices first
- Implement touch-friendly controls (minimum 44px touch targets)
- Optimize for one-handed operation
- Test on actual mobile devices, not just browser dev tools

### Data Integrity & Security
- Validate all trip data server-side regardless of client validation
- Implement JWT authentication with proper expiration handling
- Use parameterized queries to prevent SQL injection
- Log all critical operations for audit trails

### Performance Standards
- Database queries must complete within 500ms for dashboard operations
- Mobile pages must load within 3 seconds on 3G connections
- Implement proper caching strategies for frequently accessed data
- Use connection pooling for database operations

### Error Handling & User Experience
- Provide clear, actionable error messages to users
- Implement graceful degradation when features are unavailable
- Show loading states for operations taking >200ms
- Maintain user context during error recovery

### Code Organization
- Follow the established project structure (client/server/database separation)
- Use consistent naming conventions across frontend and backend
- Implement proper separation of concerns (routes/services/middleware)
- Write self-documenting code with meaningful variable names

### Testing Requirements
- Write unit tests for all business logic functions
- Implement integration tests for API endpoints
- Test QR code scanning in various lighting conditions
- Validate mobile responsiveness across different screen sizes

### Documentation Standards
- Update API documentation when adding/modifying endpoints
- Document all environment variables and their purposes
- Maintain migration documentation for database changes
- Include setup instructions for new developers
