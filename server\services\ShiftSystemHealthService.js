/**
 * Shift System Health Service
 * Purpose: Permanent monitoring and cleanup for shift management system
 * Features: Automated health checks, regression prevention, self-healing
 */

const { getClient } = require('../config/database');

class ShiftSystemHealthService {
  constructor() {
    this.isRunning = false;
    this.intervalId = null;
    this.healthCheckInterval = 300000; // 5 minutes
    this.logger = console;
    this.lastHealthCheck = null;
    this.healthHistory = [];
  }

  /**
   * Start the health monitoring service
   */
  async start() {
    if (this.isRunning) {
      this.logger.warn('Shift System Health Service is already running');
      return;
    }

    this.logger.info('🏥 Starting Shift System Health Service');
    this.isRunning = true;
    
    // Run initial health check
    await this.runHealthCheck();
    
    // Schedule regular health checks
    this.intervalId = setInterval(async () => {
      await this.runHealthCheck();
    }, this.healthCheckInterval);
  }

  /**
   * Stop the health monitoring service
   */
  stop() {
    if (!this.isRunning) {
      return;
    }

    this.logger.info('🛑 Stopping Shift System Health Service');
    this.isRunning = false;
    
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }

  /**
   * Run comprehensive health check
   */
  async runHealthCheck() {
    if (!this.isRunning) {
      return;
    }

    const startTime = Date.now();
    let client;

    try {
      client = await getClient();
      
      // Run database health check
      const healthResult = await client.query('SELECT * FROM shift_system_health_check()');
      const healthChecks = healthResult.rows;
      
      const failedChecks = healthChecks.filter(check => !check.passed);
      const warningChecks = healthChecks.filter(check => check.status === 'WARN');
      
      const healthStatus = {
        timestamp: new Date(),
        duration: Date.now() - startTime,
        totalChecks: healthChecks.length,
        passedChecks: healthChecks.filter(check => check.passed).length,
        failedChecks: failedChecks.length,
        warningChecks: warningChecks.length,
        overallHealth: failedChecks.length === 0 ? 'HEALTHY' : 'UNHEALTHY',
        checks: healthChecks
      };

      // Log results
      if (failedChecks.length > 0) {
        this.logger.error('🚨 Shift System Health Issues Detected:', {
          failed: failedChecks.length,
          warnings: warningChecks.length,
          details: failedChecks.map(check => `${check.check_name}: ${check.details}`)
        });
        
        // Auto-cleanup for critical issues
        await this.performAutoCleanup();
      } else if (warningChecks.length > 0) {
        this.logger.warn('⚠️ Shift System Health Warnings:', {
          warnings: warningChecks.length,
          details: warningChecks.map(check => `${check.check_name}: ${check.details}`)
        });
      } else {
        this.logger.info('✅ Shift System Health Check Passed', {
          checks: healthStatus.totalChecks,
          duration: healthStatus.duration + 'ms'
        });
      }

      // Store health status
      this.lastHealthCheck = healthStatus;
      this.healthHistory.push(healthStatus);
      
      // Keep only last 24 health checks
      if (this.healthHistory.length > 24) {
        this.healthHistory = this.healthHistory.slice(-24);
      }

    } catch (error) {
      this.logger.error('❌ Health check error:', error);
    } finally {
      if (client) {
        client.release();
      }
    }
  }

  /**
   * Perform automatic cleanup
   */
  async performAutoCleanup() {
    let client;
    
    try {
      client = await getClient();
      
      this.logger.info('🧹 Performing automatic system cleanup...');
      
      const cleanupResult = await client.query('SELECT cleanup_shift_system()');
      const cleanupMessage = cleanupResult.rows[0].cleanup_shift_system;
      
      this.logger.info('✅ Automatic cleanup completed:', cleanupMessage);
      
      // Run health check again after cleanup
      setTimeout(() => this.runHealthCheck(), 5000);
      
    } catch (error) {
      this.logger.error('❌ Auto-cleanup error:', error);
    } finally {
      if (client) {
        client.release();
      }
    }
  }

  /**
   * Force health check and cleanup
   */
  async forceHealthCheck() {
    this.logger.info('🔄 Forcing health check...');
    await this.runHealthCheck();
    return this.lastHealthCheck;
  }

  /**
   * Get current health status
   */
  getHealthStatus() {
    return {
      isRunning: this.isRunning,
      lastCheck: this.lastHealthCheck,
      healthHistory: this.healthHistory.slice(-5), // Last 5 checks
      nextCheckIn: this.isRunning ? Math.max(0, this.healthCheckInterval - (Date.now() - (this.lastHealthCheck?.timestamp?.getTime() || 0))) : null
    };
  }

  /**
   * Get detailed health report
   */
  getHealthReport() {
    if (!this.lastHealthCheck) {
      return { status: 'NO_DATA', message: 'No health check data available' };
    }

    const { lastHealthCheck } = this;
    const recentIssues = this.healthHistory.slice(-5).filter(check => check.failedChecks > 0);
    
    return {
      status: lastHealthCheck.overallHealth,
      lastCheck: lastHealthCheck.timestamp,
      summary: {
        totalChecks: lastHealthCheck.totalChecks,
        passed: lastHealthCheck.passedChecks,
        failed: lastHealthCheck.failedChecks,
        warnings: lastHealthCheck.warningChecks
      },
      recentIssues: recentIssues.length,
      checks: lastHealthCheck.checks,
      trend: this.getHealthTrend()
    };
  }

  /**
   * Get health trend analysis
   */
  getHealthTrend() {
    if (this.healthHistory.length < 3) {
      return 'INSUFFICIENT_DATA';
    }

    const recent = this.healthHistory.slice(-3);
    const failureCounts = recent.map(check => check.failedChecks);
    
    if (failureCounts.every(count => count === 0)) {
      return 'STABLE_HEALTHY';
    } else if (failureCounts[2] < failureCounts[0]) {
      return 'IMPROVING';
    } else if (failureCounts[2] > failureCounts[0]) {
      return 'DEGRADING';
    } else {
      return 'STABLE_ISSUES';
    }
  }
}

// Create singleton instance
const shiftSystemHealthService = new ShiftSystemHealthService();

module.exports = shiftSystemHealthService;