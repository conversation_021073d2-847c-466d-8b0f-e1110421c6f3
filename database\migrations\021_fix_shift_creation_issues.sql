-- Migration 021: Fix Shift Creation Issues
-- Fixes two issues:
-- 1. end_time should be NULL on check-in, not '23:59:59'
-- 2. display_type classification should not be affected by placeholder end_time

-- First, remove the NOT NULL constraint from end_time column
ALTER TABLE driver_shifts ALTER COLUMN end_time DROP NOT NULL;

-- Update the handover_driver_shift function to set end_time as NULL
CREATE OR REPLACE FUNCTION handover_driver_shift(
    p_truck_id INTEGER,
    p_new_driver_id INTEGER,
    p_handover_notes TEXT DEFAULT NULL
)
RETURNS INTEGER AS $$
DECLARE
    v_old_shift_id INTEGER;
    v_new_shift_id INTEGER;
    v_current_timestamp TIMESTAMP := CURRENT_TIMESTAMP;
    v_current_date DATE := CURRENT_DATE;
    v_current_time TIME := CURRENT_TIME;
BEGIN
    -- End any existing active shift for this truck
    UPDATE driver_shifts 
    SET 
        status = 'completed',
        end_date = v_current_date,
        end_time = v_current_time,
        handover_notes = COALESCE(p_handover_notes, 'Automatic handover via QR system'),
        handover_completed_at = v_current_timestamp,
        updated_at = v_current_timestamp
    WHERE truck_id = p_truck_id 
      AND status = 'active'
    RETURNING id INTO v_old_shift_id;
    
    -- Create new active shift for the new driver with NULL end_time
    INSERT INTO driver_shifts (
        truck_id,
        driver_id,
        shift_type,
        start_date,
        end_date,
        start_time,
        end_time,
        status,
        previous_shift_id,
        auto_created,
        created_at,
        updated_at
    ) VALUES (
        p_truck_id,
        p_new_driver_id,
        'custom',
        v_current_date,
        v_current_date,
        v_current_time,
        NULL, -- Set to NULL instead of '23:59:59'
        'active',
        v_old_shift_id,
        true,
        v_current_timestamp,
        v_current_timestamp
    ) RETURNING id INTO v_new_shift_id;
    
    -- Log the handover event
    PERFORM log_system_event(
        'DRIVER_HANDOVER',
        'Driver shift handover completed via QR system',
        jsonb_build_object(
            'truck_id', p_truck_id,
            'old_shift_id', v_old_shift_id,
            'new_shift_id', v_new_shift_id,
            'new_driver_id', p_new_driver_id,
            'handover_time', v_current_timestamp
        )
    );
    
    RETURN v_new_shift_id;
END;
$$ LANGUAGE plpgsql;

-- Update the classify_shift_by_time function to handle NULL end_time
CREATE OR REPLACE FUNCTION classify_shift_by_time(
    p_start_time TIME,
    p_end_time TIME
) RETURNS shift_type AS $$
DECLARE
    v_start_minutes INTEGER;
    v_end_minutes INTEGER;
    v_tolerance INTEGER := 30; -- 30 minutes tolerance
    v_current_hour INTEGER;
BEGIN
    -- If end_time is NULL (active shift), classify based on start_time and current time
    IF p_end_time IS NULL THEN
        v_current_hour := EXTRACT(HOUR FROM CURRENT_TIME);
        
        -- If starting during typical day hours (6 AM - 6 PM), classify as day
        IF EXTRACT(HOUR FROM p_start_time) BETWEEN 6 AND 18 THEN
            RETURN 'day';
        -- If starting during typical night hours (6 PM - 6 AM), classify as night
        ELSIF EXTRACT(HOUR FROM p_start_time) >= 18 OR EXTRACT(HOUR FROM p_start_time) <= 6 THEN
            RETURN 'night';
        ELSE
            RETURN 'custom';
        END IF;
    END IF;
    
    -- Convert times to minutes since midnight for completed shifts
    v_start_minutes := EXTRACT(HOUR FROM p_start_time) * 60 + EXTRACT(MINUTE FROM p_start_time);
    v_end_minutes := EXTRACT(HOUR FROM p_end_time) * 60 + EXTRACT(MINUTE FROM p_end_time);
    
    -- Check for standard day shift patterns (6AM-6PM, 7AM-7PM, 8AM-5PM, etc.)
    IF (
        (ABS(v_start_minutes - 360) <= v_tolerance AND ABS(v_end_minutes - 1080) <= v_tolerance) OR -- 6AM-6PM
        (ABS(v_start_minutes - 420) <= v_tolerance AND ABS(v_end_minutes - 1140) <= v_tolerance) OR -- 7AM-7PM
        (ABS(v_start_minutes - 480) <= v_tolerance AND ABS(v_end_minutes - 1020) <= v_tolerance) OR -- 8AM-5PM
        (ABS(v_start_minutes - 360) <= v_tolerance AND ABS(v_end_minutes - 1020) <= v_tolerance) OR -- 6AM-5PM
        (ABS(v_start_minutes - 420) <= v_tolerance AND ABS(v_end_minutes - 1080) <= v_tolerance) OR -- 7AM-6PM
        (ABS(v_start_minutes - 480) <= v_tolerance AND ABS(v_end_minutes - 960) <= v_tolerance) OR  -- 8AM-4PM
        (ABS(v_start_minutes - 540) <= v_tolerance AND ABS(v_end_minutes - 1020) <= v_tolerance)    -- 9AM-5PM
    ) THEN
        RETURN 'day';
    END IF;
    
    -- Check for standard night shift patterns (6PM-6AM, 7PM-7AM, etc.)
    -- Handle midnight crossing
    IF (
        (ABS(v_start_minutes - 1080) <= v_tolerance AND (v_end_minutes < v_start_minutes AND ABS(v_end_minutes - 360) <= v_tolerance)) OR -- 6PM-6AM
        (ABS(v_start_minutes - 1140) <= v_tolerance AND (v_end_minutes < v_start_minutes AND ABS(v_end_minutes - 420) <= v_tolerance)) OR -- 7PM-7AM
        (ABS(v_start_minutes - 1320) <= v_tolerance AND (v_end_minutes < v_start_minutes AND ABS(v_end_minutes - 360) <= v_tolerance)) OR -- 10PM-6AM
        (ABS(v_start_minutes - 1380) <= v_tolerance AND (v_end_minutes < v_start_minutes AND ABS(v_end_minutes - 420) <= v_tolerance)) OR -- 11PM-7AM
        (ABS(v_start_minutes - 1200) <= v_tolerance AND (v_end_minutes < v_start_minutes AND ABS(v_end_minutes - 480) <= v_tolerance))    -- 8PM-8AM
    ) THEN
        RETURN 'night';
    END IF;
    
    -- If no standard pattern matches, return custom
    RETURN 'custom';
END;
$$ LANGUAGE plpgsql;

-- Update existing active shifts to have NULL end_time instead of '23:59:59'
UPDATE driver_shifts 
SET 
    end_time = NULL,
    updated_at = CURRENT_TIMESTAMP
WHERE status = 'active' 
  AND end_time = '23:59:59'::TIME;

-- Add a comment explaining the fix
COMMENT ON FUNCTION classify_shift_by_time(TIME, TIME) IS 'Intelligently classify shift type based on time patterns. Handles NULL end_time for active shifts.';

-- Log completion
DO $$
BEGIN
    RAISE NOTICE 'Migration 021 completed: Fixed shift creation issues - end_time now NULL on check-in, improved display_type classification';
END $$;