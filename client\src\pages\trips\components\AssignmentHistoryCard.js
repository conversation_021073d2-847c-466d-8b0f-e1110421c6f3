import React from 'react';

/**
 * Displays assignment history for a trip, including auto-creation details
 */
const AssignmentHistoryCard = ({ trip }) => {
  if (!trip) return null;

  // Check if this trip was created with an auto-assignment
  const wasAutoAssigned = trip.assignment_notes &&
    typeof trip.assignment_notes === 'object' &&
    trip.assignment_notes.type === 'auto_assignment';
  
  // Parse trip notes if they exist
  let tripNotes = {};
  try {
    if (trip.notes && typeof trip.notes === 'string' && trip.notes.trim() !== '') {
      tripNotes = JSON.parse(trip.notes);
    } else if (trip.notes && typeof trip.notes === 'object') {
      tripNotes = trip.notes;
    }
  } catch (error) {
    console.error('Failed to parse trip notes:', error);
  }

  // If no assignment history, don't render anything
  if (!wasAutoAssigned && !tripNotes.auto_created_assignment_id) {
    return null;
  }

  return (
    <div className="bg-white border border-secondary-200 rounded-lg p-4 mt-4">
      <h3 className="text-lg font-medium text-secondary-900 mb-2 flex items-center">
        <span className="mr-2">🤖</span>
        Assignment History
      </h3>

      <div className="mt-3 space-y-3">
        {/* Auto-Assignment Status */}
        {wasAutoAssigned && (
          <div className="p-3 rounded-lg bg-purple-50 border border-purple-200">
            <div className="flex items-center">
              <div className="flex-shrink-0 w-8 h-8 bg-purple-100 text-purple-600 rounded-full flex items-center justify-center">
                <span>🤖</span>
              </div>
              <div className="ml-3">
                <h4 className="text-sm font-medium">Auto-Assignment Created</h4>
                <p className="text-sm">This trip was created using automatic assignment logic</p>
              </div>
            </div>
          </div>
        )}

        {/* Assignment Creation Details */}
        {tripNotes.auto_created_assignment_id && (
          <div className="p-3 rounded-lg bg-blue-50 border border-blue-200">
            <div className="flex items-center">
              <div className="flex-shrink-0 w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center">
                <span>📋</span>
              </div>
              <div className="ml-3">
                <h4 className="text-sm font-medium">Assignment Created</h4>
                <p className="text-xs text-secondary-500">
                  Assignment ID: #{tripNotes.auto_created_assignment_id}
                </p>
              </div>
            </div>

            {tripNotes.historical_assignment_id && (
              <div className="mt-2 text-sm">
                <div className="font-medium">Based on Historical Pattern:</div>
                <div className="text-secondary-700">Assignment #{tripNotes.historical_assignment_id}</div>
              </div>
            )}
            
            {tripNotes.current_location && (
              <div className="mt-2 text-xs text-secondary-500">
                Created at: {tripNotes.current_location}
              </div>
            )}
          </div>
        )}

        {/* Assignment Pattern Info */}
        {tripNotes.assignment_pattern && (
          <div className="mt-2 text-sm border-t border-secondary-200 pt-2">
            <div className="font-medium text-secondary-700">Assignment Pattern:</div>
            <div className="text-secondary-600">{tripNotes.assignment_pattern}</div>
            
            {tripNotes.confidence_score && (
              <div className="mt-1 text-xs text-green-600">
                Confidence: {Math.round(tripNotes.confidence_score * 100)}%
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default AssignmentHistoryCard;
