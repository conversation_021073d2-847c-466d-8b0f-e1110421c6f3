import React from 'react';

const ResourceSummaryCards = ({ resourceData }) => {
  if (!resourceData) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[1, 2, 3, 4].map((i) => (
          <div key={i} className="bg-white overflow-hidden shadow rounded-lg animate-pulse">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-gray-300 rounded-full"></div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <div className="h-4 bg-gray-300 rounded mb-2"></div>
                  <div className="h-6 bg-gray-300 rounded mb-1"></div>
                  <div className="h-3 bg-gray-300 rounded"></div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  const getStatusColor = (alerts) => {
    if (alerts.driver_shortage || alerts.truck_shortage) {
      return 'bg-red-500';
    }
    return 'bg-green-500';
  };

  const getStatusIcon = (alerts) => {
    if (alerts.driver_shortage || alerts.truck_shortage) {
      return '⚠️';
    }
    return '✅';
  };

  const getStatusText = (alerts) => {
    if (alerts.driver_shortage || alerts.truck_shortage) {
      return 'Shortage';
    }
    return 'Normal';
  };

  const getUtilizationColor = (unassigned, total) => {
    if (total === 0) return 'text-gray-500';
    const utilizationRate = (total - unassigned) / total;
    if (utilizationRate >= 0.8) return 'text-green-600';
    if (utilizationRate >= 0.6) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {/* Driver Summary Card */}
      <div className="bg-white overflow-hidden shadow rounded-lg hover:shadow-lg transition-shadow duration-200">
        <div className="p-5">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                <span className="text-white text-sm">👨‍💼</span>
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">
                  Drivers
                </dt>
                <dd className={`text-lg font-medium ${getUtilizationColor(
                  resourceData.drivers.unassigned, 
                  resourceData.drivers.total_available
                )}`}>
                  {resourceData.drivers.assigned_to_active_shifts} / {resourceData.drivers.total_available}
                </dd>
                <dd className="text-sm text-gray-500">
                  {resourceData.drivers.unassigned} unassigned
                </dd>
              </dl>
            </div>
          </div>
          {/* Progress bar */}
          <div className="mt-3">
            <div className="bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                style={{
                  width: resourceData.drivers.total_available > 0 
                    ? `${(resourceData.drivers.assigned_to_active_shifts / resourceData.drivers.total_available) * 100}%`
                    : '0%'
                }}
              ></div>
            </div>
          </div>
        </div>
      </div>

      {/* Truck Summary Card */}
      <div className="bg-white overflow-hidden shadow rounded-lg hover:shadow-lg transition-shadow duration-200">
        <div className="p-5">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                <span className="text-white text-sm">🚛</span>
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">
                  Dump Trucks
                </dt>
                <dd className={`text-lg font-medium ${getUtilizationColor(
                  resourceData.trucks.unassigned, 
                  resourceData.trucks.total_available
                )}`}>
                  {resourceData.trucks.assigned_to_active_shifts} / {resourceData.trucks.total_available}
                </dd>
                <dd className="text-sm text-gray-500">
                  {resourceData.trucks.unassigned} unassigned
                </dd>
              </dl>
            </div>
          </div>
          {/* Progress bar */}
          <div className="mt-3">
            <div className="bg-gray-200 rounded-full h-2">
              <div 
                className="bg-green-500 h-2 rounded-full transition-all duration-300"
                style={{
                  width: resourceData.trucks.total_available > 0 
                    ? `${(resourceData.trucks.assigned_to_active_shifts / resourceData.trucks.total_available) * 100}%`
                    : '0%'
                }}
              ></div>
            </div>
          </div>
        </div>
      </div>

      {/* On Route Card */}
      <div className="bg-white overflow-hidden shadow rounded-lg hover:shadow-lg transition-shadow duration-200">
        <div className="p-5">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                <span className="text-white text-sm">🛣️</span>
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">
                  On Route
                </dt>
                <dd className="text-lg font-medium text-gray-900">
                  {resourceData.trucks.on_route}
                </dd>
                <dd className="text-sm text-gray-500">
                  trucks in transit
                </dd>
              </dl>
            </div>
          </div>
          {/* Activity indicator */}
          <div className="mt-3 flex items-center">
            <div className={`w-2 h-2 rounded-full mr-2 ${
              resourceData.trucks.on_route > 0 ? 'bg-yellow-500 animate-pulse' : 'bg-gray-300'
            }`}></div>
            <span className="text-xs text-gray-500">
              {resourceData.trucks.on_route > 0 ? 'Active hauling' : 'No active trips'}
            </span>
          </div>
        </div>
      </div>

      {/* Alerts Card */}
      <div className="bg-white overflow-hidden shadow rounded-lg hover:shadow-lg transition-shadow duration-200">
        <div className="p-5">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${getStatusColor(resourceData.alerts)}`}>
                <span className="text-white text-sm">
                  {getStatusIcon(resourceData.alerts)}
                </span>
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">
                  Status
                </dt>
                <dd className="text-lg font-medium text-gray-900">
                  {getStatusText(resourceData.alerts)}
                </dd>
                <dd className="text-sm text-gray-500">
                  resource levels
                </dd>
              </dl>
            </div>
          </div>
          {/* Alert details */}
          <div className="mt-3">
            {resourceData.alerts.driver_shortage && (
              <div className="text-xs text-red-600 mb-1">⚠️ Driver shortage detected</div>
            )}
            {resourceData.alerts.truck_shortage && (
              <div className="text-xs text-red-600 mb-1">⚠️ Truck shortage detected</div>
            )}
            {!resourceData.alerts.driver_shortage && !resourceData.alerts.truck_shortage && (
              <div className="text-xs text-green-600">✅ All resources adequate</div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResourceSummaryCards;