import React, { useState, useEffect } from 'react';
import axios from 'axios';

const TripLogsDataQuality = () => {
  const [validationData, setValidationData] = useState(null);
  const [healthStatus, setHealthStatus] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [timeRange, setTimeRange] = useState(24);
  const [alerts, setAlerts] = useState(null);
  const [customThresholds, setCustomThresholds] = useState({
    driver_info_success_rate: 95,
    notes_success_rate: 90,
    sequence_success_rate: 95,
    completeness_rate: 98
  });

  // Fetch health status on component mount
  useEffect(() => {
    fetchHealthStatus();
  }, []);

  const fetchHealthStatus = async () => {
    try {
      const response = await axios.get('/api/validation/trip-logs/health-check');
      setHealthStatus(response.data.data);
    } catch (error) {
      console.error('Error fetching health status:', error);
    }
  };

  const runComprehensiveValidation = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await axios.get(`/api/validation/trip-logs/comprehensive?hours=${timeRange}`);
      setValidationData(response.data.data);
    } catch (error) {
      setError(error.response?.data?.message || 'Failed to run validation');
      console.error('Validation error:', error);
    } finally {
      setLoading(false);
    }
  };

  const generateAlerts = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await axios.post('/api/validation/trip-logs/alerts', {
        thresholds: {
          ...customThresholds,
          time_range_hours: timeRange
        }
      });
      setAlerts(response.data.data);
    } catch (error) {
      setError(error.response?.data?.message || 'Failed to generate alerts');
      console.error('Alerts error:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'HEALTHY': return 'text-green-600 bg-green-100';
      case 'WARNING': return 'text-yellow-600 bg-yellow-100';
      case 'CRITICAL': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'CRITICAL': return 'text-red-600 bg-red-100 border-red-200';
      case 'HIGH': return 'text-orange-600 bg-orange-100 border-orange-200';
      case 'MEDIUM': return 'text-yellow-600 bg-yellow-100 border-yellow-200';
      default: return 'text-gray-600 bg-gray-100 border-gray-200';
    }
  };

  const formatPercentage = (rate) => {
    const percentage = parseFloat(rate.replace('%', ''));
    if (percentage >= 95) return 'text-green-600';
    if (percentage >= 90) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="border-b border-gray-200 pb-4">
        <h2 className="text-2xl font-bold text-gray-900 flex items-center">
          📊 Trip Logs Data Quality Validation
        </h2>
        <p className="mt-2 text-sm text-gray-600">
          Monitor and validate the completeness and quality of trip_logs field population to ensure data integrity.
        </p>
      </div>

      {/* Quick Health Status */}
      {healthStatus && (
        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Current Health Status</h3>
            <button
              onClick={fetchHealthStatus}
              className="text-sm text-blue-600 hover:text-blue-500"
            >
              🔄 Refresh
            </button>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="text-center">
              <div className={`inline-flex px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(healthStatus.status)}`}>
                {healthStatus.status}
              </div>
              <p className="text-xs text-gray-500 mt-1">Overall Status</p>
            </div>
            
            <div className="text-center">
              <div className={`text-lg font-bold ${formatPercentage(healthStatus.metrics.driver_info_success_rate)}`}>
                {healthStatus.metrics.driver_info_success_rate}
              </div>
              <p className="text-xs text-gray-500">Driver Info</p>
            </div>
            
            <div className="text-center">
              <div className={`text-lg font-bold ${formatPercentage(healthStatus.metrics.notes_quality_success_rate)}`}>
                {healthStatus.metrics.notes_quality_success_rate}
              </div>
              <p className="text-xs text-gray-500">Notes Quality</p>
            </div>
            
            <div className="text-center">
              <div className={`text-lg font-bold ${formatPercentage(healthStatus.metrics.overall_completeness_rate)}`}>
                {healthStatus.metrics.overall_completeness_rate}
              </div>
              <p className="text-xs text-gray-500">Completeness</p>
            </div>
          </div>

          {healthStatus.issues && healthStatus.issues.length > 0 && (
            <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
              <h4 className="text-sm font-medium text-red-800 mb-2">Current Issues:</h4>
              <ul className="text-sm text-red-700 space-y-1">
                {healthStatus.issues.map((issue, index) => (
                  <li key={index}>• {issue}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}

      {/* Controls */}
      <div className="bg-white border border-gray-200 rounded-lg p-4">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Validation Controls</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Time Range (hours)
            </label>
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(parseInt(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value={1}>Last 1 hour</option>
              <option value={6}>Last 6 hours</option>
              <option value={24}>Last 24 hours</option>
              <option value={72}>Last 3 days</option>
              <option value={168}>Last 7 days</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Alert Thresholds
            </label>
            <div className="grid grid-cols-2 gap-2">
              <input
                type="number"
                placeholder="Driver Info %"
                value={customThresholds.driver_info_success_rate}
                onChange={(e) => setCustomThresholds({
                  ...customThresholds,
                  driver_info_success_rate: parseInt(e.target.value)
                })}
                className="px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
              />
              <input
                type="number"
                placeholder="Completeness %"
                value={customThresholds.completeness_rate}
                onChange={(e) => setCustomThresholds({
                  ...customThresholds,
                  completeness_rate: parseInt(e.target.value)
                })}
                className="px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
              />
            </div>
          </div>
        </div>

        <div className="flex space-x-4">
          <button
            onClick={runComprehensiveValidation}
            disabled={loading}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? '⏳ Running...' : '🔍 Run Comprehensive Validation'}
          </button>
          
          <button
            onClick={generateAlerts}
            disabled={loading}
            className="px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? '⏳ Generating...' : '🚨 Generate Quality Alerts'}
          </button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <span className="text-red-600 text-lg mr-2">❌</span>
            <div>
              <h3 className="text-sm font-medium text-red-800">Validation Error</h3>
              <p className="text-sm text-red-700 mt-1">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Comprehensive Validation Results */}
      {validationData && (
        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Comprehensive Validation Report</h3>
          
          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-blue-600">
                {validationData.overall_summary.total_trips_analyzed}
              </div>
              <p className="text-sm text-blue-700">Total Trips Analyzed</p>
            </div>
            
            <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
              <div className={`text-2xl font-bold ${formatPercentage(validationData.overall_summary.driver_info_success_rate)}`}>
                {validationData.overall_summary.driver_info_success_rate}
              </div>
              <p className="text-sm text-green-700">Driver Info Success</p>
            </div>
            
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-center">
              <div className={`text-2xl font-bold ${formatPercentage(validationData.overall_summary.notes_quality_success_rate)}`}>
                {validationData.overall_summary.notes_quality_success_rate}
              </div>
              <p className="text-sm text-yellow-700">Notes Quality</p>
            </div>
            
            <div className="bg-purple-50 border border-purple-200 rounded-lg p-4 text-center">
              <div className={`text-2xl font-bold ${formatPercentage(validationData.overall_summary.overall_completeness_rate)}`}>
                {validationData.overall_summary.overall_completeness_rate}
              </div>
              <p className="text-sm text-purple-700">Overall Completeness</p>
            </div>
          </div>

          {/* Detailed Results */}
          <div className="space-y-4">
            {/* Driver Validation Details */}
            <div className="border border-gray-200 rounded-lg p-4">
              <h4 className="font-semibold text-gray-900 mb-2">Driver Information Validation</h4>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">Complete:</span>
                  <span className="ml-2 font-medium">{validationData.detailed_results.driver_validation.complete_driver_info}</span>
                </div>
                <div>
                  <span className="text-gray-600">Missing:</span>
                  <span className="ml-2 font-medium text-red-600">{validationData.detailed_results.driver_validation.missing_driver_info}</span>
                </div>
                <div>
                  <span className="text-gray-600">Success Rate:</span>
                  <span className={`ml-2 font-medium ${formatPercentage(validationData.detailed_results.driver_validation.success_rate)}`}>
                    {validationData.detailed_results.driver_validation.success_rate}
                  </span>
                </div>
              </div>
              
              {Object.keys(validationData.detailed_results.driver_validation.missing_breakdown).length > 0 && (
                <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded">
                  <h5 className="text-sm font-medium text-red-800 mb-2">Missing Field Breakdown:</h5>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2 text-sm">
                    {Object.entries(validationData.detailed_results.driver_validation.missing_breakdown).map(([field, count]) => (
                      <div key={field} className="text-red-700">
                        {field.replace('missing_', '')}: {count}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Notes Validation Details */}
            <div className="border border-gray-200 rounded-lg p-4">
              <h4 className="font-semibold text-gray-900 mb-2">Notes Quality Validation</h4>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">Valid:</span>
                  <span className="ml-2 font-medium">{validationData.detailed_results.notes_validation.valid_notes}</span>
                </div>
                <div>
                  <span className="text-gray-600">Invalid:</span>
                  <span className="ml-2 font-medium text-red-600">{validationData.detailed_results.notes_validation.invalid_notes}</span>
                </div>
                <div>
                  <span className="text-gray-600">Avg Length:</span>
                  <span className="ml-2 font-medium">{validationData.detailed_results.notes_validation.average_notes_length} chars</span>
                </div>
                <div>
                  <span className="text-gray-600">Success Rate:</span>
                  <span className={`ml-2 font-medium ${formatPercentage(validationData.detailed_results.notes_validation.success_rate)}`}>
                    {validationData.detailed_results.notes_validation.success_rate}
                  </span>
                </div>
              </div>
            </div>

            {/* Location Sequence Details */}
            <div className="border border-gray-200 rounded-lg p-4">
              <h4 className="font-semibold text-gray-900 mb-2">Location Sequence Validation</h4>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">Valid:</span>
                  <span className="ml-2 font-medium">{validationData.detailed_results.sequence_validation.valid_sequences}</span>
                </div>
                <div>
                  <span className="text-gray-600">Invalid:</span>
                  <span className="ml-2 font-medium text-red-600">{validationData.detailed_results.sequence_validation.invalid_sequences}</span>
                </div>
                <div>
                  <span className="text-gray-600">Success Rate:</span>
                  <span className={`ml-2 font-medium ${formatPercentage(validationData.detailed_results.sequence_validation.success_rate)}`}>
                    {validationData.detailed_results.sequence_validation.success_rate}
                  </span>
                </div>
              </div>
              
              {Object.keys(validationData.detailed_results.sequence_validation.workflow_breakdown).length > 0 && (
                <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded">
                  <h5 className="text-sm font-medium text-blue-800 mb-2">Workflow Type Breakdown:</h5>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm">
                    {Object.entries(validationData.detailed_results.sequence_validation.workflow_breakdown).map(([workflow, count]) => (
                      <div key={workflow} className="text-blue-700">
                        {workflow}: {count}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Execution Info */}
          <div className="mt-4 text-xs text-gray-500 text-center">
            Report generated in {validationData.execution_time_ms}ms at {new Date(validationData.validation_timestamp).toLocaleString()}
          </div>
        </div>
      )}

      {/* Alerts Results */}
      {alerts && (
        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Data Quality Alerts</h3>
          
          {alerts.alerts_generated === 0 ? (
            <div className="text-center py-8">
              <span className="text-6xl">✅</span>
              <h4 className="text-lg font-medium text-green-800 mt-2">No Quality Issues Found</h4>
              <p className="text-sm text-green-600 mt-1">All metrics are within the specified thresholds</p>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="flex items-center justify-between mb-4">
                <span className="text-sm text-gray-600">
                  {alerts.alerts_generated} alert{alerts.alerts_generated !== 1 ? 's' : ''} generated
                </span>
              </div>
              
              {alerts.alerts.map((alert, index) => (
                <div key={index} className={`border rounded-lg p-4 ${getSeverityColor(alert.severity)}`}>
                  <div className="flex items-start">
                    <div className="flex-shrink-0">
                      <span className="text-lg">
                        {alert.severity === 'CRITICAL' ? '🚨' : 
                         alert.severity === 'HIGH' ? '⚠️' : '⚡'}
                      </span>
                    </div>
                    <div className="ml-3 flex-1">
                      <div className="flex items-center">
                        <h4 className="text-sm font-medium">{alert.type.replace(/_/g, ' ')}</h4>
                        <span className={`ml-2 px-2 py-1 text-xs rounded-full ${getSeverityColor(alert.severity)}`}>
                          {alert.severity}
                        </span>
                      </div>
                      <p className="text-sm mt-1">{alert.message}</p>
                      
                      {alert.details && (
                        <div className="mt-2 text-xs">
                          <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                            <div>Current: {alert.details.current_rate}</div>
                            <div>Threshold: {alert.details.threshold}</div>
                            <div>Total Trips: {alert.details.total_trips}</div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Help Section */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h3 className="text-lg font-semibold text-blue-900 mb-2">📚 About Trip Logs Data Quality</h3>
        <div className="text-sm text-blue-800 space-y-2">
          <p><strong>Driver Information:</strong> Validates that all driver-related fields (ID, name, employee ID, shift info) are populated when trips are created.</p>
          <p><strong>Notes Quality:</strong> Ensures notes contain meaningful contextual information (at least 10 characters, valid JSON format).</p>
          <p><strong>Location Sequence:</strong> Verifies sequence numbers are correct for different workflow types (standard: 1-2, extended: 1-3, cycle: 1+).</p>
          <p><strong>Overall Completeness:</strong> Checks that all required fields are populated for complete trip records.</p>
        </div>
      </div>
    </div>
  );
};

export default TripLogsDataQuality;