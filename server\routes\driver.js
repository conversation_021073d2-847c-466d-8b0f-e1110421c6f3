const express = require('express');
const rateLimit = require('express-rate-limit');
const Joi = require('joi');
const { query, getClient } = require('../config/database');
const DriverQRCodeGenerator = require('../utils/DriverQRCodeGenerator');
const SecurityMonitor = require('../utils/SecurityMonitor');
const { logError, logInfo } = require('../utils/logger');
const { notifyDriverConnected, notifyDriverDisconnected, notifyDriverHandover } = require('../websocket');
const { requireActiveDriverStatus, getDriverStatusForCache, getTruckStatusForCache } = require('../middleware/driverStatusValidation');

const router = express.Router();

// Enhanced rate limiting with abuse pattern monitoring
const driverConnectLimiter = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 100, // 100 requests per minute per IP
  message: {
    success: false,
    error: 'RATE_LIMIT_EXCEEDED',
    message: 'Too many driver connect requests from this IP. Please try again later.',
    retryAfter: 60
  },
  standardHeaders: true,
  legacyHeaders: false,
  skip: (req) => process.env.NODE_ENV === 'development',
  // Note: onLimitReached is deprecated in express-rate-limit v7
  // Rate limit logging is handled in the handler function instead
});

// Validation schemas
const driverConnectSchema = Joi.object({
  driver_qr_data: Joi.alternatives().try(
    Joi.string().required(),
    Joi.object().required()
  ).required(),
  truck_qr_data: Joi.alternatives().try(
    Joi.string().required(),
    Joi.object().required()
  ).required(),
  action: Joi.string().valid('check_in', 'check_out').optional()
});

const driverStatusSchema = Joi.object({
  employee_id: Joi.string().required()
});

const truckStatusSchema = Joi.object({
  truck_number: Joi.string().required()
});

/**
 * @route   POST /api/driver/connect
 * @desc    Process driver QR scan and truck QR scan for check-in/check-out
 * @access  Public (no authentication required)
 */
router.post('/connect', driverConnectLimiter, requireActiveDriverStatus('employeeId', 'driver_connect'), async (req, res) => {
  const client = await getClient();
  
  // Enhanced security context for audit logging - defined outside try block
  const securityContext = {
    ip_address: req.ip,
    user_agent: req.get('User-Agent'),
    timestamp: new Date().toISOString(),
    request_id: req.headers['x-request-id'] || 'unknown',
    forwarded_for: req.get('X-Forwarded-For')
  };
  
  try {
    // Security validation - check request origin
    const originValidation = SecurityMonitor.validateRequestOrigin(req);
    if (originValidation.suspicious && originValidation.risk_level === 'CRITICAL') {
      logError('SUSPICIOUS_REQUEST_BLOCKED', 'Request blocked due to suspicious headers', {
        ip_address: req.ip,
        user_agent: req.get('User-Agent'),
        indicators: originValidation.indicators,
        risk_level: originValidation.risk_level
      });
      
      return res.status(403).json({
        success: false,
        error: 'REQUEST_BLOCKED',
        message: 'Request blocked for security reasons'
      });
    }
    // Validate request body
    const { error, value } = driverConnectSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        error: 'VALIDATION_ERROR',
        message: 'Invalid request data',
        details: error.details[0].message
      });
    }

    const { driver_qr_data, truck_qr_data, action } = value;

    // Start database transaction
    await client.query('BEGIN');

    // Step 1: Validate driver QR code with security context
    const driverValidation = await DriverQRCodeGenerator.validateDriverQR(driver_qr_data);
    if (!driverValidation.success || !driverValidation.valid) {
      await client.query('ROLLBACK');
      
      // Enhanced security audit for failed validation
      logError('DRIVER_CONNECT_VALIDATION_FAILED', driverValidation.error || 'Driver QR validation failed', {
        ...securityContext,
        validation_error: driverValidation.error,
        qr_data_hash: driverValidation.qr_data ? 
          require('crypto').createHash('sha256').update(JSON.stringify(driverValidation.qr_data)).digest('hex').substring(0, 16) : 
          'NO_DATA'
      });

      // Monitor failed authentication attempt
      await SecurityMonitor.monitorFailedAuth(
        req.ip, 
        driverValidation.qr_data?.employee_id || 'UNKNOWN',
        driverValidation.error || 'QR_VALIDATION_FAILED'
      );
      
      return res.status(400).json({
        success: false,
        error: 'INVALID_DRIVER_QR',
        message: driverValidation.error || 'Invalid driver QR code',
        details: { driver_validation: driverValidation }
      });
    }

    const driver = driverValidation.driver;

    // Step 2: Parse and validate truck QR code
    let truckQrData;
    try {
      truckQrData = typeof truck_qr_data === 'string' 
        ? JSON.parse(truck_qr_data) 
        : truck_qr_data;
    } catch (parseError) {
      await client.query('ROLLBACK');
      return res.status(400).json({
        success: false,
        error: 'INVALID_TRUCK_QR',
        message: 'Truck QR code is not valid JSON format'
      });
    }

    // Validate truck QR structure
    if (!truckQrData || !truckQrData.id || !truckQrData.type || truckQrData.type !== 'truck') {
      await client.query('ROLLBACK');
      return res.status(400).json({
        success: false,
        error: 'INVALID_TRUCK_QR',
        message: 'Invalid truck QR code structure'
      });
    }

    // Step 3: Validate truck exists and is active
    const truckResult = await client.query(
      `SELECT id, truck_number, license_plate, qr_code_data, status
       FROM dump_trucks 
       WHERE truck_number = $1`,
      [truckQrData.id]
    );

    if (truckResult.rows.length === 0) {
      await client.query('ROLLBACK');
      return res.status(400).json({
        success: false,
        error: 'TRUCK_NOT_FOUND',
        message: 'Truck not found'
      });
    }

    const truck = truckResult.rows[0];

    if (truck.status !== 'active') {
      await client.query('ROLLBACK');
      return res.status(400).json({
        success: false,
        error: 'TRUCK_INACTIVE',
        message: 'Truck is not available for assignment. Please contact maintenance or supervisor.'
      });
    }

    // Verify truck QR code data matches database
    const storedTruckQrData = truck.qr_code_data;
    if (!storedTruckQrData || storedTruckQrData.id !== truckQrData.id) {
      await client.query('ROLLBACK');
      return res.status(400).json({
        success: false,
        error: 'TRUCK_QR_MISMATCH',
        message: 'Truck QR code data mismatch with database'
      });
    }

    // Step 4: Check driver's current shift status
    const currentShiftResult = await client.query(
      `SELECT ds.id, ds.truck_id, ds.start_date, ds.start_time, dt.truck_number
       FROM driver_shifts ds
       JOIN dump_trucks dt ON ds.truck_id = dt.id
       WHERE ds.driver_id = $1 AND ds.status = 'active'
         AND ds.start_date IS NOT NULL AND ds.start_time IS NOT NULL
       ORDER BY ds.created_at DESC
       LIMIT 1`,
      [driver.id]
    );

    const hasActiveShift = currentShiftResult.rows.length > 0;
    const currentShift = hasActiveShift ? currentShiftResult.rows[0] : null;

    // Step 5: Determine action based on current status and truck
    let finalAction = action;
    let isHandover = false;

    if (!finalAction) {
      if (!hasActiveShift) {
        finalAction = 'check_in';
      } else if (currentShift.truck_id === truck.id) {
        finalAction = 'check_out';
      } else {
        finalAction = 'check_in';
        isHandover = true;
      }
    }

    let result = {};

    // Step 6: Execute the action
    if (finalAction === 'check_in') {
      if (isHandover) {
        // Handle handover: end current shift and start new one
        const handoverResult = await client.query(
          'SELECT handover_driver_shift($1, $2, $3)',
          [truck.id, driver.id, `Handover from ${currentShift.truck_number} to ${truck.truck_number}`]
        );

        const newShiftId = handoverResult.rows[0].handover_driver_shift;

        result = {
          action: 'handover',
          message: `Taking over from previous assignment on ${currentShift.truck_number}`,
          previous_truck: currentShift.truck_number,
          new_truck: truck.truck_number,
          shift_id: newShiftId,
          previous_shift_id: currentShift.id,
          check_in_time: new Date().toISOString(),
          driver: {
            employee_id: driver.employee_id,
            full_name: driver.full_name
          }
        };

      } else {
        // Regular check-in: create new shift with constraint violation prevention
        const currentTimestamp = new Date();
        const currentDate = new Intl.DateTimeFormat('en-CA', { timeZone: 'Asia/Manila' }).format(currentTimestamp);
        const currentTime = currentTimestamp.toTimeString().split(' ')[0];

        // ENHANCED: Ensure no active shifts exist before creating new one
        // This prevents unique_active_driver_shift constraint violations
        await client.query(
          `UPDATE driver_shifts
           SET status = 'completed',
               end_date = $1,
               end_time = $2,
               updated_at = $3
           WHERE driver_id = $4 AND status = 'active'`,
          [currentDate, currentTime, currentTimestamp, driver.id]
        );

        // Determine shift type based on current time using centralized utility
        const ShiftTypeDetector = require('../utils/ShiftTypeDetector');
        const shiftType = ShiftTypeDetector.detectShiftType(currentTimestamp);

        const newShiftResult = await client.query(
          `INSERT INTO driver_shifts (
            truck_id, driver_id, shift_type, start_date, end_date,
            start_time, end_time, status, auto_created, created_at, updated_at
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
          RETURNING id`,
          [
            truck.id, driver.id, shiftType, currentDate, null,
            currentTime, null, 'active', true, currentTimestamp, currentTimestamp
          ]
        );

        const newShiftId = newShiftResult.rows[0].id;

        result = {
          action: 'check_in',
          message: `Checked in to ${truck.truck_number}`,
          truck: truck.truck_number,
          shift_id: newShiftId,
          check_in_time: currentTimestamp.toISOString(),
          driver: {
            employee_id: driver.employee_id,
            full_name: driver.full_name
          }
        };
      }

    } else if (finalAction === 'check_out') {
      if (!hasActiveShift) {
        await client.query('ROLLBACK');
        return res.status(400).json({
          success: false,
          error: 'NO_ACTIVE_SHIFT',
          message: 'No active shift found to check out from'
        });
      }

      if (currentShift.truck_id !== truck.id) {
        await client.query('ROLLBACK');
        return res.status(400).json({
          success: false,
          error: 'TRUCK_MISMATCH',
          message: `You are currently assigned to ${currentShift.truck_number}. Please scan that truck's QR code to check out.`
        });
      }

      // Check out: end current shift
      const currentTimestamp = new Date();
      const currentDate = new Intl.DateTimeFormat('en-CA', { timeZone: 'Asia/Manila' }).format(currentTimestamp);
      const currentTime = currentTimestamp.toTimeString().split(' ')[0];

      await client.query(
        `UPDATE driver_shifts 
         SET status = 'completed', end_date = $1, end_time = $2, updated_at = $3
         WHERE id = $4`,
        [currentDate, currentTime, currentTimestamp, currentShift.id]
      );

      // Calculate duration with proper validation
      let startDateTime;
      let durationHours = 0;
      let durationMinutes = 0;
      
      try {
        // Validate that we have valid date and time values
        if (!currentShift.start_date || !currentShift.start_time) {
          throw new Error('Missing start_date or start_time');
        }
        
        // Ensure start_date is in proper format (handle Date objects or strings)
        let formattedStartDate;
        if (currentShift.start_date instanceof Date) {
          // Check if the Date object is valid before calling toISOString()
          if (isNaN(currentShift.start_date.getTime())) {
            throw new Error('Invalid start_date Date object');
          }
          formattedStartDate = new Intl.DateTimeFormat('en-CA', { timeZone: 'Asia/Manila' }).format(currentShift.start_date);
        } else {
          formattedStartDate = currentShift.start_date;
        }
        
        startDateTime = new Date(`${formattedStartDate}T${currentShift.start_time}`);
        
        // Check if the created date is valid
        if (isNaN(startDateTime.getTime())) {
          throw new Error('Invalid date/time combination');
        }
        
        const endDateTime = currentTimestamp;
        const durationMs = endDateTime - startDateTime;
        durationHours = Math.floor(durationMs / (1000 * 60 * 60));
        durationMinutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60));
        
        // Ensure non-negative duration
        if (durationMs < 0) {
          durationHours = 0;
          durationMinutes = 0;
        }
      } catch (dateError) {
        // Log the date parsing error but don't fail the checkout
        logError('DATE_PARSING_ERROR', dateError, {
          shift_id: currentShift.id,
          start_date: currentShift.start_date,
          start_time: currentShift.start_time,
          driver_id: driver.id
        });
        
        // Use current timestamp as fallback for start time
        startDateTime = null; // Set to null to indicate fallback should be used
        durationHours = 0;
        durationMinutes = 0;
      }

      // Safely format check_in_time
      let checkInTime;
      try {
        checkInTime = startDateTime && !isNaN(startDateTime.getTime()) 
          ? startDateTime.toISOString() 
          : currentTimestamp.toISOString();
      } catch (error) {
        checkInTime = currentTimestamp.toISOString();
      }

      result = {
        action: 'check_out',
        message: `Checked out from ${truck.truck_number}`,
        truck: truck.truck_number,
        shift_id: currentShift.id,
        check_in_time: checkInTime,
        check_out_time: currentTimestamp.toISOString(),
        duration: `${durationHours}h ${durationMinutes}m`,
        driver: {
          employee_id: driver.employee_id,
          full_name: driver.full_name
        }
      };
    }

    // Commit transaction
    await client.query('COMMIT');

    // Send WebSocket notifications
    try {
      if (finalAction === 'check_in') {
        if (isHandover) {
          // Get previous driver info for handover notification
          const previousDriverResult = await client.query(
            'SELECT d.id, d.employee_id, d.full_name FROM drivers d JOIN driver_shifts ds ON d.id = ds.driver_id WHERE ds.id = $1',
            [result.previous_shift_id]
          );
          
          if (previousDriverResult.rows.length > 0) {
            const previousDriver = previousDriverResult.rows[0];
            notifyDriverHandover(previousDriver, driver, truck, result);
          }
        } else {
          notifyDriverConnected(driver, truck, result);
        }
      } else if (finalAction === 'check_out') {
        notifyDriverDisconnected(driver, truck, result);
      }
    } catch (wsError) {
      // Don't fail the request if WebSocket notification fails
      logError('WEBSOCKET_NOTIFICATION_ERROR', wsError, {
        driver_id: driver.id,
        action: finalAction
      });
    }

    // Enhanced audit logging for successful operation
    logInfo('DRIVER_CONNECT_SUCCESS', `Driver ${driver.employee_id} ${finalAction} ${isHandover ? '(handover)' : ''}`, {
      ...securityContext,
      driver_id: driver.id,
      employee_id: driver.employee_id,
      truck_id: truck.id,
      truck_number: truck.truck_number,
      action: finalAction,
      is_handover: isHandover,
      shift_id: result.shift_id,
      previous_shift_id: result.previous_shift_id,
      processing_time_ms: Date.now() - new Date(securityContext.timestamp).getTime(),
      security_level: 'enhanced'
    });

    res.json({
      success: true,
      ...result
    });

  } catch (error) {
    await client.query('ROLLBACK');
    
    // Enhanced error logging with security context
    logError('DRIVER_CONNECT_ERROR', error, {
      ...securityContext,
      error_type: error.name,
      error_message: error.message,
      stack_trace: process.env.NODE_ENV === 'development' ? error.stack : 'REDACTED',
      request_body_hash: require('crypto').createHash('sha256').update(JSON.stringify(req.body)).digest('hex').substring(0, 16)
    });

    res.status(500).json({
      success: false,
      error: 'INTERNAL_ERROR',
      message: 'An error occurred while processing your request. Please try again.'
    });
  } finally {
    client.release();
  }
});

/**
 * @route   GET /api/driver/status/:employeeId
 * @desc    Get current driver shift status
 * @access  Public (no authentication required)
 */
router.get('/status/:employeeId', driverConnectLimiter, async (req, res) => {
  try {
    // Validate employee ID
    const { error } = driverStatusSchema.validate({ employee_id: req.params.employeeId });
    if (error) {
      return res.status(400).json({
        success: false,
        error: 'VALIDATION_ERROR',
        message: 'Invalid employee ID format'
      });
    }

    const { employeeId } = req.params;

    // Use comprehensive driver status validation with caching support
    const statusValidation = await getDriverStatusForCache(employeeId);

    if (!statusValidation.success) {
      // Return appropriate status code based on error type
      const statusCode = statusValidation.error === 'DRIVER_NOT_FOUND' ? 404 : 403;

      return res.status(statusCode).json({
        success: false,
        error: statusValidation.error,
        message: statusValidation.message,
        driver_status: statusValidation.status,
        status_display_name: statusValidation.statusDisplayName,
        cached_at: statusValidation.cachedAt
      });
    }

    const driver = statusValidation.driver;

    // Get current active shift
    const shiftResult = await query(
      `SELECT ds.id, ds.truck_id, ds.start_date, ds.start_time, 
              dt.truck_number, dt.license_plate
       FROM driver_shifts ds
       JOIN dump_trucks dt ON ds.truck_id = dt.id
       WHERE ds.driver_id = $1 AND ds.status = 'active'
       ORDER BY ds.created_at DESC
       LIMIT 1`,
      [driver.id]
    );

    const hasActiveShift = shiftResult.rows.length > 0;
    const currentShift = hasActiveShift ? shiftResult.rows[0] : null;

    let status = 'checked_out';
    let statusMessage = 'Ready to check in';
    let currentTruck = null;

    if (hasActiveShift) {
      status = 'checked_in';
      statusMessage = `Currently assigned to ${currentShift.truck_number}`;
      currentTruck = {
        id: currentShift.truck_id,
        truck_number: currentShift.truck_number,
        license_plate: currentShift.license_plate
      };
    }

    res.json({
      success: true,
      driver: {
        employee_id: driver.employee_id,
        full_name: driver.full_name,
        status: driver.status
      },
      status,
      status_message: statusMessage,
      driver_status: statusValidation.status,
      status_display_name: statusValidation.statusDisplayName,
      current_truck: currentTruck,
      shift_info: currentShift ? {
        shift_id: currentShift.id,
        start_date: currentShift.start_date,
        start_time: currentShift.start_time
      } : null,
      cached_at: statusValidation.cachedAt,
      cache_expiry: statusValidation.cacheExpiry
    });

  } catch (error) {
    // Enhanced error logging for status endpoint
    logError('DRIVER_STATUS_ERROR', error, {
      employee_id: req.params.employeeId,
      ip_address: req.ip,
      user_agent: req.get('User-Agent'),
      forwarded_for: req.get('X-Forwarded-For'),
      timestamp: new Date().toISOString(),
      request_id: req.headers['x-request-id'] || 'unknown',
      error_type: error.name,
      error_message: error.message
    });

    res.status(500).json({
      success: false,
      error: 'INTERNAL_ERROR',
      message: 'An error occurred while retrieving driver status. Please try again.'
    });
  }
});

/**
 * @route   GET /api/driver/truck-status/:truckNumber
 * @desc    Get truck status for PWA Driver Connect validation
 * @access  Public (no authentication required)
 */
router.get('/truck-status/:truckNumber', driverConnectLimiter, async (req, res) => {
  try {
    // Validate truck number
    const { error } = truckStatusSchema.validate({ truck_number: req.params.truckNumber });
    if (error) {
      return res.status(400).json({
        success: false,
        error: 'VALIDATION_ERROR',
        message: 'Invalid truck number format'
      });
    }

    const { truckNumber } = req.params;

    // Use comprehensive truck status validation with caching support
    const statusValidation = await getTruckStatusForCache(truckNumber);

    if (!statusValidation.success) {
      // Return appropriate status code based on error type
      const statusCode = statusValidation.error === 'TRUCK_NOT_FOUND' ? 404 : 403;

      return res.status(statusCode).json({
        success: false,
        error: statusValidation.error,
        message: statusValidation.message,
        truck_status: statusValidation.status,
        status_display_name: statusValidation.statusDisplayName,
        truck: statusValidation.truck,
        statusBlocked: true, // Flag for client-side handling
        cached_at: statusValidation.cachedAt
      });
    }

    // Return successful truck status validation
    res.json({
      success: true,
      truck: statusValidation.truck,
      truck_status: statusValidation.status,
      status_display_name: statusValidation.statusDisplayName,
      message: statusValidation.message,
      cached_at: statusValidation.cachedAt,
      cache_expiry: statusValidation.cacheExpiry
    });

  } catch (error) {
    // Enhanced error logging for truck status endpoint
    logError('TRUCK_STATUS_ERROR', error, {
      truck_number: req.params.truckNumber,
      ip_address: req.ip,
      user_agent: req.get('User-Agent'),
      forwarded_for: req.get('X-Forwarded-For'),
      timestamp: new Date().toISOString(),
      request_id: req.headers['x-request-id'] || 'unknown',
      error_type: error.name,
      error_message: error.message
    });

    res.status(500).json({
      success: false,
      error: 'INTERNAL_ERROR',
      message: 'An error occurred while retrieving truck status. Please try again.'
    });
  }
});

/**
 * @route   GET /api/driver/active-shifts
 * @desc    Get all currently active driver shifts for offline sync
 * @access  Public (for PWA offline sync)
 */
router.get('/active-shifts', async (req, res) => {
  try {
    const result = await query(`
      SELECT
        ds.id,
        ds.driver_id,
        ds.truck_id,
        ds.created_at,
        d.employee_id,
        d.full_name,
        dt.truck_number
      FROM driver_shifts ds
      JOIN drivers d ON ds.driver_id = d.id
      JOIN dump_trucks dt ON ds.truck_id = dt.id
      WHERE ds.status = 'active'
      ORDER BY ds.created_at DESC
    `);

    console.log(`[Driver API] Retrieved ${result.rows.length} active shifts for sync`);

    res.json(result.rows);
  } catch (error) {
    console.error('[Driver API] Error fetching active shifts:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to fetch active shifts'
    });
  }
});

/**
 * @route   GET /api/driver/health
 * @desc    Health check endpoint for driver connect system
 * @access  Public
 */
router.get('/health', (req, res) => {
  res.json({
    success: true,
    service: 'Driver Connect API',
    status: 'healthy',
    timestamp: new Date().toISOString()
  });
});

module.exports = router;