#!/usr/bin/env node
/**
 * Development Mode Startup Script (HTTP)
 * Automatically configures and starts the system in development mode with HTTP
 */

const { spawn } = require('child_process');
const { loadConfig, writeClientEnv, displayConfig } = require('../config-loader');
const fs = require('fs');
const path = require('path');

console.log('🚀 Starting Hauling QR Trip System in Development Mode (HTTP)...\n');

// Configure environment for development HTTP
const envPath = path.join(__dirname, '..', '.env');
let envContent = fs.readFileSync(envPath, 'utf8');

// Update configuration
envContent = envContent.replace(/^NODE_ENV=.*/m, 'NODE_ENV=development');
envContent = envContent.replace(/^ENABLE_HTTPS=.*/m, 'ENABLE_HTTPS=false');

// Write updated .env file
fs.writeFileSync(envPath, envContent);

// Load configuration and generate client env
const config = loadConfig();
writeClientEnv(config);
displayConfig(config);

console.log('\n🔄 Starting servers...\n');

// Start both server and client using concurrently
const concurrently = spawn('npx', [
  'concurrently',
  '--names', 'SERVER,CLIENT',
  '--prefix-colors', 'blue,green',
  '--kill-others-on-fail',
  '"cd server && npm run dev"',
  '"cd client && npm start"'
], {
  stdio: 'inherit',
  shell: true,
  cwd: path.join(__dirname, '..')
});

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down development servers...');
  concurrently.kill('SIGINT');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Shutting down development servers...');
  concurrently.kill('SIGTERM');
  process.exit(0);
});

concurrently.on('exit', (code) => {
  console.log(`\n✅ Development servers stopped with code ${code}`);
  process.exit(code);
});
