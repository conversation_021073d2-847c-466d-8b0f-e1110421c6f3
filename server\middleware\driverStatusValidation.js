const { query } = require('../config/database');
const { logError } = require('../utils/logger');
const { validateTruckStatus, TRUCK_STATUS_MESSAGES, TRUCK_STATUS_DISPLAY_NAMES } = require('./truckStatusValidation');

/**
 * Driver Status Validation Middleware
 * Validates driver status for PWA Driver Connect operations
 * Prevents inactive, suspended, on_leave, or terminated drivers from performing shift operations
 */

// Status-specific error messages with actionable guidance
const STATUS_MESSAGES = {
  inactive: "Your account is inactive. Contact your supervisor to reactivate your account.",
  suspended: "Your account is suspended. Contact HR for assistance.",
  on_leave: "You are currently on leave. Contact your supervisor if this is incorrect.",
  terminated: "Your employment status has changed. Contact HR immediately."
};

// Status display names for logging
const STATUS_DISPLAY_NAMES = {
  inactive: "Inactive",
  suspended: "Suspended",
  on_leave: "On Leave",
  terminated: "Terminated"
};

/**
 * Validate driver status and return structured response
 * @param {string} employeeId - Driver employee ID
 * @param {string} operation - Operation being attempted (e.g., 'check_in', 'check_out', 'qr_scan')
 * @returns {Promise<Object>} Validation result with status and error details
 */
async function validateDriverStatus(employeeId, operation = 'shift_operation') {
  try {
    // Get driver info including current status
    const driverResult = await query(
      `SELECT id, employee_id, full_name, status, hire_date, updated_at
       FROM drivers 
       WHERE employee_id = $1`,
      [employeeId]
    );

    if (driverResult.rows.length === 0) {
      return {
        valid: false,
        error: 'DRIVER_NOT_FOUND',
        message: 'Driver not found in system.',
        status: null,
        driver: null
      };
    }

    const driver = driverResult.rows[0];

    // Check if driver status allows operations
    if (driver.status !== 'active') {
      // Log blocked operation attempt for security monitoring
      await logBlockedOperation(driver, operation);

      return {
        valid: false,
        error: 'DRIVER_STATUS_BLOCKED',
        message: STATUS_MESSAGES[driver.status] || 'Driver account status prevents operations.',
        status: driver.status,
        statusDisplayName: STATUS_DISPLAY_NAMES[driver.status] || driver.status,
        driver: {
          id: driver.id,
          employee_id: driver.employee_id,
          full_name: driver.full_name,
          status: driver.status,
          hire_date: driver.hire_date,
          updated_at: driver.updated_at
        }
      };
    }

    // Driver is active - allow operation
    return {
      valid: true,
      error: null,
      message: 'Driver status validated successfully.',
      status: driver.status,
      statusDisplayName: 'Active',
      driver: {
        id: driver.id,
        employee_id: driver.employee_id,
        full_name: driver.full_name,
        status: driver.status,
        hire_date: driver.hire_date,
        updated_at: driver.updated_at
      }
    };

  } catch (error) {
    logError('DRIVER_STATUS_VALIDATION_ERROR', error, {
      employee_id: employeeId,
      operation
    });

    return {
      valid: false,
      error: 'VALIDATION_ERROR',
      message: 'Unable to validate driver status. Please try again.',
      status: null,
      driver: null
    };
  }
}

/**
 * Log blocked operation attempt for security monitoring
 * @param {Object} driver - Driver information
 * @param {string} operation - Operation that was blocked
 */
async function logBlockedOperation(driver, operation) {
  try {
    // Insert audit record for blocked operation
    await query(
      `INSERT INTO driver_status_audit (
        driver_id, employee_id, driver_status, operation_attempted, 
        blocked_at, ip_address, user_agent
      ) VALUES ($1, $2, $3, $4, NOW(), $5, $6)`,
      [
        driver.id,
        driver.employee_id,
        driver.status,
        operation,
        'PWA_SYSTEM', // IP will be enhanced in middleware
        'PWA_DRIVER_CONNECT' // User agent will be enhanced in middleware
      ]
    );

    logError('DRIVER_OPERATION_BLOCKED', `${STATUS_DISPLAY_NAMES[driver.status] || driver.status} driver attempted ${operation}`, {
      driver_id: driver.id,
      employee_id: driver.employee_id,
      status: driver.status,
      operation,
      full_name: driver.full_name
    });

  } catch (auditError) {
    logError('AUDIT_LOG_ERROR', auditError, {
      driver_id: driver.id,
      employee_id: driver.employee_id,
      operation
    });
  }
}

/**
 * Express middleware for driver status validation
 * Validates driver status and blocks operations for non-active drivers
 * @param {string} employeeIdParam - Request parameter name containing employee ID (default: 'employeeId')
 * @param {string} operation - Operation being attempted
 * @returns {Function} Express middleware function
 */
function requireActiveDriverStatus(employeeIdParam = 'employeeId', operation = 'shift_operation') {
  return async (req, res, next) => {
    try {
      // Extract employee ID from request parameters or body
      let employeeId = req.params[employeeIdParam] || req.body.employee_id;
      
      // For driver connect operations, extract from QR data
      if (!employeeId && req.body.driver_qr_data) {
        try {
          const driverQrData = typeof req.body.driver_qr_data === 'string' 
            ? JSON.parse(req.body.driver_qr_data) 
            : req.body.driver_qr_data;
          employeeId = driverQrData.employee_id;
        } catch (parseError) {
          return res.status(400).json({
            success: false,
            error: 'INVALID_QR_DATA',
            message: 'Invalid driver QR code data format.'
          });
        }
      }

      if (!employeeId) {
        return res.status(400).json({
          success: false,
          error: 'MISSING_EMPLOYEE_ID',
          message: 'Employee ID is required for driver operations.'
        });
      }

      // Validate driver status
      const validation = await validateDriverStatus(employeeId, operation);

      if (!validation.valid) {
        // Enhanced audit logging with request context
        if (validation.driver) {
          await enhanceAuditLog(validation.driver, operation, req);
        }

        return res.status(403).json({
          success: false,
          error: validation.error,
          message: validation.message,
          status: validation.status,
          statusDisplayName: validation.statusDisplayName,
          driver: validation.driver ? {
            employee_id: validation.driver.employee_id,
            full_name: validation.driver.full_name,
            status: validation.driver.status
          } : null
        });
      }

      // Add validated driver info to request for downstream use
      req.validatedDriver = validation.driver;
      req.driverStatus = validation.status;

      next();

    } catch (error) {
      logError('DRIVER_STATUS_MIDDLEWARE_ERROR', error, {
        employee_id_param: employeeIdParam,
        operation,
        url: req.url,
        method: req.method
      });

      return res.status(500).json({
        success: false,
        error: 'VALIDATION_ERROR',
        message: 'Unable to validate driver status. Please try again.'
      });
    }
  };
}

/**
 * Enhance audit log with request context information
 * @param {Object} driver - Driver information
 * @param {string} operation - Operation attempted
 * @param {Object} req - Express request object
 */
async function enhanceAuditLog(driver, operation, req) {
  try {
    const ipAddress = req.ip || req.connection.remoteAddress || 'unknown';
    const userAgent = req.get('User-Agent') || 'unknown';

    await query(
      `UPDATE driver_status_audit 
       SET ip_address = $1, user_agent = $2
       WHERE driver_id = $3 AND operation_attempted = $4 
         AND blocked_at > NOW() - INTERVAL '1 minute'`,
      [ipAddress, userAgent, driver.id, operation]
    );
  } catch (error) {
    logError('AUDIT_ENHANCEMENT_ERROR', error, {
      driver_id: driver.id,
      operation
    });
  }
}

/**
 * Validate both driver and truck status for PWA Driver Connect operations
 * @param {string} employeeId - Driver employee ID
 * @param {string} truckNumber - Truck number (e.g., "DT-100")
 * @param {string} operation - Operation being attempted
 * @returns {Promise<Object>} Combined validation result
 */
async function validateDriverAndTruckStatus(employeeId, truckNumber, operation = 'shift_operation') {
  try {
    // Validate both driver and truck status in parallel
    const [driverValidation, truckValidation] = await Promise.all([
      validateDriverStatus(employeeId, operation),
      validateTruckStatus(truckNumber, operation)
    ]);

    // Check if both validations passed
    if (driverValidation.valid && truckValidation.valid) {
      return {
        valid: true,
        driver: driverValidation.driver,
        truck: truckValidation.truck,
        message: 'Both driver and truck status validated successfully.'
      };
    }

    // Determine which validation failed and create appropriate error response
    let errorResponse = {
      valid: false,
      driver: driverValidation.driver,
      truck: truckValidation.truck
    };

    // Priority: Driver errors first, then truck errors
    if (!driverValidation.valid) {
      errorResponse = {
        ...errorResponse,
        error: driverValidation.error,
        message: driverValidation.message,
        status: driverValidation.status,
        statusDisplayName: driverValidation.statusDisplayName,
        entityType: 'driver',
        statusBlocked: true
      };
    } else if (!truckValidation.valid) {
      errorResponse = {
        ...errorResponse,
        error: truckValidation.error,
        message: truckValidation.message,
        status: truckValidation.status,
        statusDisplayName: truckValidation.statusDisplayName,
        entityType: 'truck',
        statusBlocked: true
      };
    }

    return errorResponse;

  } catch (error) {
    logError('COMBINED_STATUS_VALIDATION_ERROR', error, {
      employee_id: employeeId,
      truck_number: truckNumber,
      operation
    });

    return {
      valid: false,
      error: 'VALIDATION_ERROR',
      message: 'Unable to validate driver and truck status. Please try again.',
      driver: null,
      truck: null
    };
  }
}

/**
 * Get driver status with caching information for PWA offline mode
 * @param {string} employeeId - Driver employee ID
 * @returns {Promise<Object>} Driver status with cache metadata
 */
async function getDriverStatusForCache(employeeId) {
  try {
    const validation = await validateDriverStatus(employeeId, 'status_check');

    return {
      success: validation.valid,
      driver: validation.driver,
      status: validation.status,
      statusDisplayName: validation.statusDisplayName,
      message: validation.message,
      error: validation.error,
      cachedAt: new Date().toISOString(),
      cacheExpiry: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 hour TTL
    };
  } catch (error) {
    logError('DRIVER_STATUS_CACHE_ERROR', error, { employee_id: employeeId });

    return {
      success: false,
      error: 'CACHE_ERROR',
      message: 'Unable to retrieve driver status for caching.',
      cachedAt: new Date().toISOString()
    };
  }
}

/**
 * Get truck status with caching information for PWA offline mode
 * @param {string} truckNumber - Truck number
 * @returns {Promise<Object>} Truck status with cache metadata
 */
async function getTruckStatusForCache(truckNumber) {
  try {
    const validation = await validateTruckStatus(truckNumber, 'status_check');

    return {
      success: validation.valid,
      truck: validation.truck,
      status: validation.status,
      statusDisplayName: validation.statusDisplayName,
      message: validation.message,
      error: validation.error,
      cachedAt: new Date().toISOString(),
      cacheExpiry: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 hour TTL
    };
  } catch (error) {
    logError('TRUCK_STATUS_CACHE_ERROR', error, { truck_number: truckNumber });

    return {
      success: false,
      error: 'CACHE_ERROR',
      message: 'Unable to retrieve truck status for caching.',
      cachedAt: new Date().toISOString()
    };
  }
}

module.exports = {
  validateDriverStatus,
  validateDriverAndTruckStatus,
  requireActiveDriverStatus,
  getDriverStatusForCache,
  getTruckStatusForCache,
  STATUS_MESSAGES,
  STATUS_DISPLAY_NAMES,
  TRUCK_STATUS_MESSAGES,
  TRUCK_STATUS_DISPLAY_NAMES
};
