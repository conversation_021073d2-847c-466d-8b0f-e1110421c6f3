#!/usr/bin/env node
/**
 * Development Mode Startup Script (HTTPS)
 * Automatically configures and starts the system in development mode with HTTPS
 */

const { spawn } = require('child_process');
const { loadConfig, writeClientEnv, displayConfig } = require('../config-loader');
const fs = require('fs');
const path = require('path');

console.log('🚀 Starting Hauling QR Trip System in Development Mode (HTTPS)...\n');

// Check if SSL certificates exist
const sslDevPath = path.join(__dirname, '..', 'server', 'ssl', 'dev');
const certPath = path.join(sslDevPath, 'server.crt');
const keyPath = path.join(sslDevPath, 'server.key');

if (!fs.existsSync(certPath) || !fs.existsSync(keyPath)) {
  console.log('🔐 SSL certificates not found. Generating development certificates...');
  
  // Generate development certificates
  const generateCerts = spawn('node', ['ssl/generate-dev-certs.js'], {
    cwd: path.join(__dirname, '..', 'server'),
    stdio: 'inherit'
  });
  
  generateCerts.on('exit', (code) => {
    if (code !== 0) {
      console.error('❌ Failed to generate SSL certificates');
      process.exit(1);
    }
    startServers();
  });
} else {
  console.log('🔐 SSL certificates found');
  startServers();
}

function startServers() {
  // Configure environment for development HTTPS
  const envPath = path.join(__dirname, '..', '.env');
  let envContent = fs.readFileSync(envPath, 'utf8');

  // Update configuration
  envContent = envContent.replace(/^NODE_ENV=.*/m, 'NODE_ENV=development');
  envContent = envContent.replace(/^ENABLE_HTTPS=.*/m, 'ENABLE_HTTPS=true');

  // Write updated .env file
  fs.writeFileSync(envPath, envContent);

  // Load configuration and generate client env
  const config = loadConfig();
  writeClientEnv(config);
  displayConfig(config);

  console.log('\n🔄 Starting servers with HTTPS...\n');

  console.log('📋 IMPORTANT - ACCESS URLS:');
  console.log('='.repeat(50));
  console.log(`🌐 Frontend (React App): https://${config.IP_ADDRESS}:3000`);
  console.log(`📡 Backend API: https://${config.IP_ADDRESS}:5444/api`);
  console.log(`🔍 Health Check: https://${config.IP_ADDRESS}:5444/health`);
  console.log('='.repeat(50));
  console.log('⚠️  DO NOT access the backend URL directly for the frontend!');
  console.log('✅ Use the Frontend URL for login and dashboard access.\n');

  // Start both server and client using concurrently with proper environment
  const concurrently = spawn('npx', [
    'concurrently',
    '--names', 'SERVER,CLIENT',
    '--prefix-colors', 'blue,green',
    '--kill-others-on-fail',
    '"cd server && node server.js"',
    '"cd client && npm start"'
  ], {
    stdio: 'inherit',
    shell: true,
    cwd: path.join(__dirname, '..'),
    env: { ...process.env, FORCE_COLOR: true }
  });

  // Handle process termination
  process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down development servers...');
    concurrently.kill('SIGINT');
    process.exit(0);
  });

  process.on('SIGTERM', () => {
    console.log('\n🛑 Shutting down development servers...');
    concurrently.kill('SIGTERM');
    process.exit(0);
  });

  concurrently.on('exit', (code) => {
    console.log(`\n✅ Development servers stopped with code ${code}`);
    process.exit(code);
  });
}
