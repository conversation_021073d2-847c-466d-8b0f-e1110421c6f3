<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PWA Driver Connect Offline Fix Tool</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            margin: -20px -20px 20px -20px;
            padding: 20px;
            border-radius: 8px 8px 0 0;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        .status.online { background: #10b981; color: white; }
        .status.offline { background: #ef4444; color: white; }
        .status.unknown { background: #6b7280; color: white; }
        .button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .button:hover { background: #2563eb; }
        .button.danger { background: #ef4444; }
        .button.danger:hover { background: #dc2626; }
        .button.success { background: #10b981; }
        .button.success:hover { background: #059669; }
        .button.warning { background: #f59e0b; }
        .button.warning:hover { background: #d97706; }
        .log {
            background: #1f2937;
            color: #f3f4f6;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
            margin-top: 10px;
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
        }
        .section h3 {
            margin-top: 0;
            color: #374151;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 10px 0;
        }
        .diagnostic-item {
            padding: 10px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            background: #f9fafb;
        }
        .diagnostic-item.pass { border-color: #10b981; background: #ecfdf5; }
        .diagnostic-item.fail { border-color: #ef4444; background: #fef2f2; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 PWA Driver Connect Offline Fix Tool</h1>
            <p>Diagnose and fix offline functionality issues</p>
        </div>

        <div class="section">
            <h3>📊 Current Status</h3>
            <div>
                Network Status: <span id="network-status" class="status unknown">Checking...</span><br>
                Service Worker: <span id="sw-status" class="status unknown">Checking...</span><br>
                PWA Mode: <span id="pwa-status" class="status unknown">Checking...</span><br>
                Manual UI: <span id="manual-ui-status" class="status unknown">Checking...</span>
            </div>
        </div>

        <div class="section">
            <h3>🔍 Quick Diagnostics</h3>
            <div class="grid">
                <button class="button" onclick="runQuickDiagnostics()">Run Quick Diagnostics</button>
                <button class="button" onclick="runFullDiagnostics()">Run Full Diagnostics</button>
                <button class="button warning" onclick="clearLog()">Clear Log</button>
            </div>
        </div>

        <div class="section">
            <h3>🛠️ Manual Fixes</h3>
            <div class="grid">
                <button class="button danger" onclick="forceOfflineMode()">Force Offline Mode</button>
                <button class="button success" onclick="forceOnlineMode()">Force Online Mode</button>
                <button class="button warning" onclick="showManualUI()">Show Manual UI</button>
                <button class="button" onclick="hideManualUI()">Hide Manual UI</button>
                <button class="button" onclick="forceCachePages()">Force Cache Pages</button>
                <button class="button" onclick="applyAllFixes()">Apply All Fixes</button>
            </div>
        </div>

        <div class="section">
            <h3>🧪 Test Actions</h3>
            <div class="grid">
                <button class="button" onclick="testDriverConnect()">Test Driver Connect</button>
                <button class="button" onclick="testOfflineFlow()">Test Offline Flow</button>
                <button class="button" onclick="testCacheStatus()">Test Cache Status</button>
                <button class="button" onclick="testServiceWorker()">Test Service Worker</button>
            </div>
        </div>

        <div class="section">
            <h3>📋 Diagnostic Results</h3>
            <div id="diagnostic-results"></div>
        </div>

        <div class="section">
            <h3>📝 Activity Log</h3>
            <div id="log" class="log">Offline Fix Tool loaded. Click "Run Quick Diagnostics" to start.\n</div>
        </div>
    </div>

    <script>
        // Global state
        let diagnosticResults = {};
        
        // Logging function
        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').textContent = '';
            log('Log cleared');
        }

        // Status update functions
        function updateStatus() {
            // Network status
            const networkStatus = navigator.onLine ? 'online' : 'offline';
            const networkElement = document.getElementById('network-status');
            networkElement.textContent = networkStatus.toUpperCase();
            networkElement.className = `status ${networkStatus}`;

            // Service worker status
            const swStatus = 'serviceWorker' in navigator && navigator.serviceWorker.controller ? 'active' : 'inactive';
            const swElement = document.getElementById('sw-status');
            swElement.textContent = swStatus.toUpperCase();
            swElement.className = `status ${swStatus === 'active' ? 'online' : 'offline'}`;

            // PWA mode status
            const isPWA = window.matchMedia('(display-mode: standalone)').matches || window.navigator.standalone === true;
            const pwaElement = document.getElementById('pwa-status');
            pwaElement.textContent = isPWA ? 'PWA' : 'BROWSER';
            pwaElement.className = `status ${isPWA ? 'online' : 'unknown'}`;

            // Manual UI status
            const manualUI = document.querySelector('[data-testid="manual-action-selection"]');
            const manualUIElement = document.getElementById('manual-ui-status');
            manualUIElement.textContent = manualUI ? 'VISIBLE' : 'HIDDEN';
            manualUIElement.className = `status ${manualUI ? 'online' : 'offline'}`;
        }

        // Diagnostic functions
        async function runQuickDiagnostics() {
            log('Running quick diagnostics...');
            
            try {
                // Check network state
                const networkState = navigator.onLine;
                log(`Network state: ${networkState ? 'ONLINE' : 'OFFLINE'}`);
                
                // Check service worker
                const swRegistered = 'serviceWorker' in navigator;
                const swActive = swRegistered && navigator.serviceWorker.controller;
                log(`Service Worker: ${swRegistered ? 'SUPPORTED' : 'NOT SUPPORTED'}, ${swActive ? 'ACTIVE' : 'INACTIVE'}`);
                
                // Check PWA mode
                const isPWA = window.matchMedia('(display-mode: standalone)').matches || window.navigator.standalone === true;
                log(`PWA Mode: ${isPWA ? 'DETECTED' : 'NOT DETECTED'}`);
                
                // Check manual UI
                const manualUI = document.querySelector('[data-testid="manual-action-selection"]');
                log(`Manual UI: ${manualUI ? 'VISIBLE' : 'HIDDEN'}`);
                
                // Check driver data
                const driverData = document.querySelector('h4:contains("Driver Authenticated")') || 
                                 document.querySelector('[class*="green"]:has(span:contains("👤"))');
                log(`Driver Data: ${driverData ? 'AUTHENTICATED' : 'NOT AUTHENTICATED'}`);
                
                updateStatus();
                log('Quick diagnostics completed');
                
            } catch (error) {
                log(`Quick diagnostics failed: ${error.message}`);
            }
        }

        async function runFullDiagnostics() {
            log('Running full diagnostics...');
            
            try {
                // Load and run the comprehensive diagnostic test
                if (window.runOfflineUIDebugTest) {
                    const results = await window.runOfflineUIDebugTest();
                    log('Full diagnostics completed');
                    log(`Results: ${results.success ? 'PASSED' : 'FAILED'}`);
                    log(`Issues found: ${results.rootCauses?.length || 0}`);
                    
                    if (results.rootCauses?.length > 0) {
                        results.rootCauses.forEach(cause => log(`- ${cause}`));
                    }
                    
                    diagnosticResults = results;
                    displayDiagnosticResults(results);
                } else {
                    log('Full diagnostic test not available - loading from driver-connect page...');
                    log('Please run this tool from the Driver Connect page for full diagnostics');
                }
                
            } catch (error) {
                log(`Full diagnostics failed: ${error.message}`);
            }
        }

        function displayDiagnosticResults(results) {
            const container = document.getElementById('diagnostic-results');
            container.innerHTML = '';
            
            if (!results || !results.results) {
                container.innerHTML = '<p>No diagnostic results available</p>';
                return;
            }
            
            results.results.forEach(result => {
                const item = document.createElement('div');
                item.className = `diagnostic-item ${result.success ? 'pass' : 'fail'}`;
                item.innerHTML = `
                    <strong>${result.test}</strong><br>
                    Status: ${result.success ? '✅ PASS' : '❌ FAIL'}<br>
                    <small>${result.timestamp}</small>
                `;
                container.appendChild(item);
            });
        }

        // Fix functions
        function forceOfflineMode() {
            log('Forcing offline mode...');
            if (window.networkMonitor) {
                window.networkMonitor.forceOfflineMode();
                log('Offline mode activated');
            } else {
                log('Network monitor not available - applying fixes first...');
                applyAllFixes().then(() => {
                    if (window.networkMonitor) {
                        window.networkMonitor.forceOfflineMode();
                        log('Offline mode activated after applying fixes');
                    }
                });
            }
            setTimeout(updateStatus, 500);
        }

        function forceOnlineMode() {
            log('Forcing online mode...');
            if (window.networkMonitor) {
                window.networkMonitor.forceOnlineMode();
                log('Online mode activated');
            } else {
                log('Network monitor not available - applying fixes first...');
                applyAllFixes().then(() => {
                    if (window.networkMonitor) {
                        window.networkMonitor.forceOnlineMode();
                        log('Online mode activated after applying fixes');
                    }
                });
            }
            setTimeout(updateStatus, 500);
        }

        function showManualUI() {
            log('Showing manual selection UI...');
            if (window.manualSelectionForcer) {
                window.manualSelectionForcer.forceShowManualSelection();
                log('Manual selection UI shown');
            } else {
                log('Manual selection forcer not available - applying fixes first...');
                applyAllFixes().then(() => {
                    if (window.manualSelectionForcer) {
                        window.manualSelectionForcer.forceShowManualSelection();
                        log('Manual selection UI shown after applying fixes');
                    }
                });
            }
            setTimeout(updateStatus, 500);
        }

        function hideManualUI() {
            log('Hiding manual selection UI...');
            if (window.manualSelectionForcer) {
                window.manualSelectionForcer.hideManualSelection();
                log('Manual selection UI hidden');
            } else {
                log('Manual selection forcer not available');
            }
            setTimeout(updateStatus, 500);
        }

        async function forceCachePages() {
            log('Force caching PWA pages...');
            try {
                if (window.cacheManager) {
                    const result = await window.cacheManager.forceCacheDriverConnect();
                    log(`Cache operation: ${result.success ? 'SUCCESS' : 'FAILED'}`);
                    if (result.error) log(`Error: ${result.error}`);
                } else {
                    log('Cache manager not available - applying fixes first...');
                    await applyAllFixes();
                    if (window.cacheManager) {
                        const result = await window.cacheManager.forceCacheDriverConnect();
                        log(`Cache operation: ${result.success ? 'SUCCESS' : 'FAILED'}`);
                    }
                }
            } catch (error) {
                log(`Force cache failed: ${error.message}`);
            }
        }

        async function applyAllFixes() {
            log('Applying all offline fixes...');
            try {
                if (window.applyOfflineFixes) {
                    const result = await window.applyOfflineFixes();
                    log(`Fix application: ${result.success ? 'SUCCESS' : 'FAILED'}`);
                    log(`Applied fixes: ${result.appliedFixes?.length || 0}`);
                    updateStatus();
                } else {
                    // Load the fix implementation
                    log('Loading fix implementation...');
                    const script = document.createElement('script');
                    script.src = '/src/tests/offlineFixImplementation.js';
                    script.type = 'module';
                    document.head.appendChild(script);
                    
                    setTimeout(async () => {
                        if (window.applyOfflineFixes) {
                            const result = await window.applyOfflineFixes();
                            log(`Fix application: ${result.success ? 'SUCCESS' : 'FAILED'}`);
                            updateStatus();
                        } else {
                            log('Fix implementation failed to load');
                        }
                    }, 1000);
                }
            } catch (error) {
                log(`Apply fixes failed: ${error.message}`);
            }
        }

        // Test functions
        function testDriverConnect() {
            log('Testing Driver Connect page...');
            const currentUrl = window.location.href;
            if (currentUrl.includes('/driver-connect')) {
                log('Already on Driver Connect page');
                runQuickDiagnostics();
            } else {
                log('Navigating to Driver Connect page...');
                window.open('/driver-connect', '_blank');
            }
        }

        function testOfflineFlow() {
            log('Testing offline flow...');
            log('1. Force offline mode');
            forceOfflineMode();
            
            setTimeout(() => {
                log('2. Show manual selection UI');
                showManualUI();
                
                setTimeout(() => {
                    log('3. Check if manual UI is visible');
                    const manualUI = document.querySelector('[data-testid="manual-action-selection"]');
                    log(`Manual UI visible: ${manualUI ? 'YES' : 'NO'}`);
                    
                    if (manualUI) {
                        log('✅ Offline flow test PASSED');
                    } else {
                        log('❌ Offline flow test FAILED');
                    }
                }, 1000);
            }, 1000);
        }

        async function testCacheStatus() {
            log('Testing cache status...');
            try {
                if ('caches' in window) {
                    const cacheNames = await caches.keys();
                    log(`Available caches: ${cacheNames.length}`);
                    cacheNames.forEach(name => log(`- ${name}`));
                    
                    // Check specific pages
                    const driverConnectCached = await caches.match('/driver-connect');
                    const indexCached = await caches.match('/') || await caches.match('/index.html');
                    
                    log(`Driver Connect cached: ${driverConnectCached ? 'YES' : 'NO'}`);
                    log(`Index page cached: ${indexCached ? 'YES' : 'NO'}`);
                } else {
                    log('Cache API not supported');
                }
            } catch (error) {
                log(`Cache test failed: ${error.message}`);
            }
        }

        function testServiceWorker() {
            log('Testing service worker...');
            if ('serviceWorker' in navigator) {
                log('Service Worker API supported');
                
                navigator.serviceWorker.getRegistration().then(registration => {
                    if (registration) {
                        log(`Service Worker registered: ${registration.scope}`);
                        log(`Service Worker state: ${registration.active?.state || 'unknown'}`);
                        
                        // Test communication
                        if (navigator.serviceWorker.controller) {
                            navigator.serviceWorker.controller.postMessage({
                                type: 'DIAGNOSTIC_TEST',
                                timestamp: new Date().toISOString()
                            });
                            log('Diagnostic message sent to service worker');
                        } else {
                            log('No service worker controller available');
                        }
                    } else {
                        log('No service worker registration found');
                    }
                }).catch(error => {
                    log(`Service worker test failed: ${error.message}`);
                });
            } else {
                log('Service Worker API not supported');
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            log('Offline Fix Tool initialized');
            updateStatus();
            
            // Update status every 2 seconds
            setInterval(updateStatus, 2000);
            
            // Listen for network changes
            window.addEventListener('online', () => {
                log('Network status changed: ONLINE');
                updateStatus();
            });
            
            window.addEventListener('offline', () => {
                log('Network status changed: OFFLINE');
                updateStatus();
            });
        });
    </script>
</body>
</html>
