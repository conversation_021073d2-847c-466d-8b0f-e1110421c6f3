import React from 'react';

const StoppedAnalytics = ({ data, loading }) => {
  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {[...Array(2)].map((_, i) => (
          <div key={i} className="animate-pulse bg-secondary-200 h-64 rounded-lg"></div>
        ))}
      </div>
    );
  }

  if (!data) {
    return (
      <div className="text-center py-8 text-secondary-500">
        <span className="text-4xl block mb-2">⏹️</span>
        No stopped analytics data available
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {/* Stopped Frequency */}
      <div className="bg-white rounded-lg shadow border border-secondary-200 p-6">
        <h3 className="text-lg font-medium text-secondary-900 mb-4">
          ⏹️ Stopped Frequency by Truck
        </h3>
        
        {data.frequency && data.frequency.length > 0 ? (
          <div className="space-y-3">
            {data.frequency.slice(0, 5).map((truck, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-secondary-50 rounded-lg">
                <div>
                  <div className="font-medium text-secondary-900">
                    {truck.truckNumber}
                  </div>
                  <div className="text-sm text-secondary-500">
                    {truck.driverName}
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-bold text-red-600">
                    {truck.stoppedCount}
                  </div>
                  <div className="text-xs text-secondary-500">
                    {truck.avgResolutionTime}m avg
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-4 text-secondary-500">
            No stopped frequency data
          </div>
        )}
      </div>

      {/* Stopped Patterns */}
      <div className="bg-white rounded-lg shadow border border-secondary-200 p-6">
        <h3 className="text-lg font-medium text-secondary-900 mb-4">
          📍 Stopped Patterns by Location
        </h3>
        
        {data.locationPatterns && data.locationPatterns.length > 0 ? (
          <div className="space-y-3">
            {data.locationPatterns.slice(0, 5).map((location, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-secondary-50 rounded-lg">
                <div>
                  <div className="font-medium text-secondary-900">
                    {location.locationName}
                  </div>
                  <div className="text-sm text-secondary-500">
                    {location.phaseWhenStopped}
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-bold text-orange-600">
                    {location.stoppedCount}
                  </div>
                  <div className="text-xs text-secondary-500">
                    {location.avgResolutionTime}m avg
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-4 text-secondary-500">
            No location pattern data
          </div>
        )}
      </div>

      {/* Stopped Trends */}
      <div className="md:col-span-2">
        <div className="bg-white rounded-lg shadow border border-secondary-200 p-6">
          <h3 className="text-lg font-medium text-secondary-900 mb-4">
            📈 Stopped Trends
          </h3>
          
          {data.trends && data.trends.length > 0 ? (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">
                    {data.trends.reduce((sum, item) => sum + item.stoppedCount, 0)}
                  </div>
                  <div className="text-sm text-secondary-500">Total Stopped</div>
                </div>
                
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">
                    {Math.round(data.trends.reduce((sum, item) => sum + item.avgResolutionTime, 0) / data.trends.length)}m
                  </div>
                  <div className="text-sm text-secondary-500">Avg Resolution Time</div>
                </div>
                
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {data.trends[0]?.mostCommonReason || 'N/A'}
                  </div>
                  <div className="text-sm text-secondary-500">Most Common Reason</div>
                </div>
              </div>
              
              {/* Simple trend visualization */}
              <div className="h-32 flex items-end justify-between space-x-2">
                {data.trends.slice(-7).map((item, index) => {
                  const maxCount = Math.max(...data.trends.map(t => t.stoppedCount));
                  const height = maxCount > 0 ? (item.stoppedCount / maxCount) * 100 : 0;
                  
                  return (
                    <div key={index} className="flex-1 flex flex-col items-center">
                      <div 
                        className="w-full bg-red-500 rounded-t"
                        style={{ height: `${height}px` }}
                        title={`${item.stoppedCount} stopped`}
                      ></div>
                      <span className="text-xs text-secondary-600 mt-1">
                        {new Date(item.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                      </span>
                    </div>
                  );
                })}
              </div>
            </div>
          ) : (
            <div className="text-center py-4 text-secondary-500">
              No trend data available
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default StoppedAnalytics;
