<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <title>Offline - Hauling QR Trip System</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
                'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
                sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .offline-container {
            text-align: center;
            padding: 2rem;
            max-width: 500px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .offline-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.8;
        }

        .offline-title {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 1rem;
            color: #fff;
        }

        .offline-message {
            font-size: 1.1rem;
            margin-bottom: 2rem;
            line-height: 1.6;
            opacity: 0.9;
        }

        .offline-actions {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            align-items: center;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            min-width: 200px;
        }

        .btn-primary {
            background: #4CAF50;
            color: white;
        }

        .btn-primary:hover {
            background: #45a049;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-offline {
            background: #ff4444;
        }

        .status-online {
            background: #4CAF50;
        }

        .connection-status {
            margin-top: 1rem;
            padding: 0.5rem;
            border-radius: 8px;
            background: rgba(0, 0, 0, 0.2);
            font-size: 0.9rem;
        }

        @media (max-width: 480px) {
            .offline-container {
                margin: 1rem;
                padding: 1.5rem;
            }
            
            .offline-title {
                font-size: 1.5rem;
            }
            
            .offline-message {
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon">📱</div>
        <h1 class="offline-title">App Temporarily Unavailable</h1>
        <p class="offline-message">
            The Hauling QR Trip System is currently offline or some components couldn't load. 
            This might be due to a network connection issue or missing app files.
        </p>
        
        <div class="offline-actions">
            <button class="btn btn-primary" onclick="retryConnection()">
                🔄 Retry Connection
            </button>
            
            <a href="/trip-scanner" class="btn btn-secondary">
                📱 Try Trip Scanner
            </a>
            
            <a href="/driver-connect" class="btn btn-secondary">
                👤 Try Driver Connect
            </a>
        </div>
        
        <div class="connection-status">
            <span class="status-indicator" id="statusIndicator"></span>
            <span id="statusText">Checking connection...</span>
        </div>
    </div>

    <script>
        // Check connection status
        function updateConnectionStatus() {
            const indicator = document.getElementById('statusIndicator');
            const text = document.getElementById('statusText');
            
            if (navigator.onLine) {
                indicator.className = 'status-indicator status-online';
                text.textContent = 'Connected - You can retry loading the app';
            } else {
                indicator.className = 'status-indicator status-offline';
                text.textContent = 'Offline - Check your internet connection';
            }
        }

        // Retry connection
        function retryConnection() {
            if (navigator.onLine) {
                // Clear service worker cache and reload
                if ('serviceWorker' in navigator) {
                    navigator.serviceWorker.getRegistrations().then(function(registrations) {
                        for(let registration of registrations) {
                            registration.unregister();
                        }
                        window.location.reload(true);
                    });
                } else {
                    window.location.reload(true);
                }
            } else {
                alert('Please check your internet connection and try again.');
            }
        }

        // Monitor connection changes
        window.addEventListener('online', updateConnectionStatus);
        window.addEventListener('offline', updateConnectionStatus);
        
        // Initial status check
        updateConnectionStatus();
        
        // Auto-retry when connection is restored
        window.addEventListener('online', function() {
            setTimeout(() => {
                if (confirm('Connection restored! Would you like to reload the app?')) {
                    retryConnection();
                }
            }, 1000);
        });
    </script>
</body>
</html>
