/**
 * Test Route Patterns Query
 * 
 * This script tests the route patterns query directly to see if our fix worked
 */

const { query } = require('../server/config/database');

async function testRoutePatterns() {
  console.log('🧪 Testing Route Patterns Query...\n');

  try {
    // Test the route pattern query with the new threshold
    const routePatternQuery = `
      SELECT
        CONCAT(COALESCE(al.name, ll.name), ' → ', COALESCE(aul.name, ul.name)) as route,
        COUNT(*) as trip_count,
        AVG(tl.total_duration_minutes) as avg_duration,
        AVG(tl.travel_duration_minutes) as avg_travel_time,
        COUNT(CASE WHEN tl.status = 'trip_completed' THEN 1 END) as completed_count,
        COUNT(CASE WHEN tl.is_exception = true THEN 1 END) as exception_count,

        -- Workflow type detection (A→B→A vs A→B→C)
        CASE
          WHEN COALESCE(al.id, ll.id) = COALESCE(aul.id, ul.id) THEN 'Same Location'
          ELSE 'Different Locations'
        END as workflow_type

      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      LEFT JOIN locations al ON tl.actual_loading_location_id = al.id
      LEFT JOIN locations aul ON tl.actual_unloading_location_id = aul.id
      WHERE tl.created_at >= CURRENT_DATE - INTERVAL '30 days'
      GROUP BY
        CONCAT(COALESCE(al.name, ll.name), ' → ', COALESCE(aul.name, ul.name)),
        COALESCE(al.id, ll.id),
        COALESCE(aul.id, ul.id)
      HAVING COUNT(*) >= 1
      ORDER BY trip_count DESC, avg_duration ASC
      LIMIT 15
    `;

    console.log('Executing route patterns query...');
    const result = await query(routePatternQuery);
    
    console.log(`Found ${result.rows.length} route patterns:`);
    
    result.rows.forEach((row, index) => {
      console.log(`${index + 1}. ${row.route}`);
      console.log(`   Trips: ${row.trip_count}`);
      console.log(`   Avg Duration: ${Math.round(parseFloat(row.avg_duration || 0))}m`);
      console.log(`   Avg Travel: ${Math.round(parseFloat(row.avg_travel_time || 0))}m`);
      console.log(`   Completed: ${row.completed_count}`);
      console.log(`   Workflow: ${row.workflow_type}`);
      console.log('');
    });

    // Test truck rankings query
    console.log('Testing truck rankings query...');
    const rankingsQuery = `
      SELECT
        dt.truck_number,
        dt.id as truck_id,
        COUNT(*) as total_trips,
        COUNT(CASE WHEN tl.status = 'trip_completed' THEN 1 END) as completed_trips
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      JOIN dump_trucks dt ON a.truck_id = dt.id
      WHERE tl.created_at >= CURRENT_DATE - INTERVAL '30 days'
      GROUP BY dt.truck_number, dt.id
      HAVING COUNT(*) >= 1
      ORDER BY total_trips DESC
      LIMIT 10
    `;

    const rankingsResult = await query(rankingsQuery);
    
    console.log(`Found ${rankingsResult.rows.length} truck rankings:`);
    
    rankingsResult.rows.forEach((row, index) => {
      console.log(`${index + 1}. ${row.truck_number}`);
      console.log(`   Total Trips: ${row.total_trips}`);
      console.log(`   Completed: ${row.completed_trips}`);
      console.log('');
    });

    console.log('✅ Route patterns and rankings queries working!');

  } catch (error) {
    console.error('❌ Error testing queries:', error);
  }
}

// Run the test
if (require.main === module) {
  testRoutePatterns().then(() => {
    process.exit(0);
  }).catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

module.exports = { testRoutePatterns };