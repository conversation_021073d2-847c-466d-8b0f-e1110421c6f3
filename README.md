# Hauling QR Trip Management System

A comprehensive logistics management system for tracking hauling truck trips using QR code technology. The system enables real-time monitoring of truck movements between locations, driver assignments, and trip completion workflows.

## 🚛 Overview

The Hauling QR Trip Management System eliminates manual trip logging and provides real-time visibility into fleet operations. Trucks scan QR codes at locations to log arrivals/departures, enabling automated workflow management and comprehensive analytics.

### Key Features

- **QR Code-based Trip Tracking**: Trucks scan QR codes at locations to log arrivals/departures
- **Driver QR Code System**: Standalone driver check-in/check-out system with ID card QR codes for time tracking and automatic shift creation
- **Multi-Location Workflows**: Support for A→B→C extensions, C→B→C cycles, and dynamic route discovery
- **Real-time Dashboard**: Live monitoring of active trips, truck status, and performance metrics
- **Driver Management**: Assignment tracking, shift management, and performance analytics
- **Manual Shift Completion**: Administrative interface for manually completing and canceling shifts
- **Exception Handling**: Automated detection and management of workflow deviations
- **Mobile-First Design**: Responsive web application optimized for mobile devices and tablets
- **Customizable Appearance**: Logo customization, font settings, and visual theme configuration
- **System Health Monitoring**: Comprehensive monitoring and automated fixing for Shift Management, Assignment Management, and Trip Monitoring modules
- **Fleet Resource Monitor**: Real-time visibility into driver and truck availability, utilization, and resource allocation across active shifts
- **Role-Based Access Control**: Comprehensive RBAC system for managing user roles and page-level permissions (in development)

## 🏗️ Architecture

Full-stack web application with separate client and server components, unified configuration system, and PostgreSQL database.

```
hauling-qr-trip-system/
├── client/                    # React frontend application
├── server/                    # Express.js backend API
├── database/                  # Database schema and migrations
├── deploy-hauling-qr-ubuntu/  # Ubuntu deployment resources
├── scripts/                   # System startup and utility scripts
├── docs/                      # Project documentation
├── utils/                     # Shared utilities
├── tests/                     # Integration tests
├── .env                       # Unified environment configuration
└── package.json               # Root package with system-level scripts
```

## 🌐 Remote Development & Network Access

### Automatic Network Configuration
The system includes intelligent network configuration that automatically:
- **🔍 Detects your local network IP** for mobile device testing
- **⚙️ Updates environment variables** for cross-device access
- **🔧 Configures CORS** for network requests
- **📱 Enables mobile testing** without manual configuration

### VS Code Dev Tunnels Support
The system includes built-in support for VS Code dev tunnels, allowing you to:
- Access your development environment from anywhere
- Test on mobile devices remotely
- Share your development instance with others

**Quick Setup:**
1. Start the unified system: `node start-system.js`
2. In VS Code, forward port 3000 and set it to **Public**
3. Access the provided tunnel URL

**Network Access Features:**
- **HOST=0.0.0.0**: Frontend accessible from any network device
- **AUTO_DETECT_IP=true**: Automatic IP detection and configuration
- **REACT_APP_DEV_TUNNEL=true**: Dev tunnel optimization
- **Flexible CORS**: Supports localhost, network IP, and tunnel URLs

For detailed setup instructions, see [docs/DEV_TUNNEL_SETUP.md](docs/DEV_TUNNEL_SETUP.md)

## 🛠️ Tech Stack

### Frontend (Client)
- **Framework**: React 18.2.0 with React Router DOM 6.20.1
- **Styling**: Tailwind CSS 3.3.6 with PostCSS and Autoprefixer
- **QR Code**: Multiple libraries (@yudiel/react-qr-scanner 2.3.1, html5-qrcode 2.3.8, qrcode.react 3.1.0)
- **Charts**: Chart.js 4.4.0 with react-chartjs-2 5.2.0 for analytics dashboards
- **Forms**: React Hook Form 7.48.2 for form management
- **HTTP Client**: Axios 1.6.2 for API communication
- **Notifications**: React Hot Toast 2.4.1 for user feedback
- **File Handling**: JSZip 3.10.1, file-saver 2.0.5, pdf-lib 1.17.1 for document generation
- **Build Tool**: Create React App (CRA) with custom configurations
- **Dev Tunnels**: Automatic proxy configuration for remote access
- **Testing**: Jest with React Testing Library and PWA offline functionality tests

### Backend (Server)
- **Runtime**: Node.js with Express.js 4.18.2 framework
- **Database**: PostgreSQL with pg 8.11.3 driver and connection pooling
- **Authentication**: JWT 9.0.2 with bcryptjs 2.4.3 for password hashing
- **Security**: Helmet 7.1.0, CORS 2.8.5, express-rate-limit 7.1.5
- **WebSocket**: ws 8.18.3 library for real-time communication
- **QR Processing**: qrcode 1.5.3 generation, jsqr 1.4.0 for reading
- **Image Processing**: Sharp 0.32.6 for image manipulation
- **Validation**: Joi 17.11.0 for request validation
- **Logging**: Winston 3.17.0 for structured logging
- **File Upload**: Multer 1.4.5-lts.1 for handling file uploads
- **Testing**: Jest 29.7.0 with Supertest 6.3.3 for API testing

### Database
- **Primary**: PostgreSQL 17.4 with advanced features (JSONB, GIN indexes, materialized views)
- **Migration System**: Custom Node.js migration runner with 29 active migrations
- **Latest Migrations**: Role-based access control (017), Driver QR system (018), Security audit logging (019), Shift management fixes (020-029)
- **Migration Monitoring**: Built-in migration checker for status verification
- **Performance**: Connection pooling (max 25, min 5), optimized indexes, materialized views for analytics
- **Advanced Functions**: Automated shift status evaluation, overnight shift handling, and management

## 🚀 Quick Start

### Prerequisites

- Node.js (v16 or higher, v20 LTS recommended)
- PostgreSQL (v12 or higher, v17.4 recommended)
- npm package manager

### Development Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd hauling-qr-trip-system
   ```

2. **Configure environment**
   ```bash
   # The .env file is included with sensible defaults
   # Edit .env with your database credentials if needed
   nano .env
   ```

3. **Setup database**
   ```bash
   # Create PostgreSQL database
   sudo -u postgres createdb hauling_qr_system
   
   # Run database migrations
   npm run db:migrate
   
   # Or use consolidated init.sql (recommended for new installations)
   sudo -u postgres psql hauling_qr_system < database/init.sql
   ```

4. **Start the application**
   ```bash
   # 🚀 RECOMMENDED: Unified startup with automatic IP detection
   node start-system.js
   
   # Alternative: Traditional development mode
   npm run dev
   
   # Or start individually:
   # Frontend dev server (port 3000)
   cd client && npm start
   
   # Backend server (port 5000)
   cd server && npm run dev
   ```

### 🎯 Unified Startup System

The system includes a comprehensive unified startup script that automatically:

- **🔍 IP Detection**: Automatically detects your network IP address
- **⚙️ Configuration**: Updates environment variables for network access
- **📦 Dependencies**: Checks and installs missing dependencies
- **🖥️ Backend**: Starts the Express.js server on port 5000
- **💻 Frontend**: Starts the React development server on port 3000
- **🔄 Process Management**: Handles graceful shutdown with Ctrl+C

**Usage:**
```bash
# Start the complete system
node start-system.js

# The script will display:
# 📱 Frontend: http://[YOUR-IP]:3000
# 🖥️ Backend: http://[YOUR-IP]:5000
# 🔌 WebSocket: ws://[YOUR-IP]:5000
```

**Features:**
- Automatic dependency installation if missing
- Real-time process monitoring and logging
- Graceful shutdown handling
- Network IP detection for mobile testing
- Process timeout protection (30s backend, 60s frontend)

### Production Deployment (Ubuntu 24.04)

The system includes a comprehensive automated deployment script for Ubuntu 24.04 VPS servers with advanced features:

#### Key Features
- **🔍 Automatic VPS IP Detection**: Multiple fallback methods (ipinfo.io, ipify.org, OpenDNS, ip route, httpbin.org)
- **🌏 Timezone Configuration**: Automatic Asia/Manila timezone setup with NTP synchronization
- **🧹 Cleanup & Preparation**: Removes old deployments and conflicting services
- **📦 Dependency Management**: Node.js 20.x LTS, PostgreSQL, Nginx, PM2 with verification
- **🔐 GitHub Authentication**: Secure repository cloning with PAT token support
- **⚙️ Environment Configuration**: Production .env with development-style flexibility
- **🗄️ Database Automation**: PostgreSQL setup, user creation, and migration running
- **🏗️ Application Building**: Frontend build with API subdomain configuration
- **🌐 Nginx Configuration**: Cloudflare SSL termination with comprehensive security headers
- **🔄 PM2 Process Management**: Ecosystem configuration with logging and monitoring
- **🔧 Production Patches**: CORS fixes, rate limiting adjustments, and error corrections
- **🏥 Health Checks**: Comprehensive service verification and status monitoring
- **📊 Detailed Logging**: Structured logs with timestamps and comprehensive error reporting

#### 🚀 Quick Start

1. **Clone repository**: `git clone https://github.com/mightybadz18/hauling-qr-trip-management.git`
2. **Navigate to deployment**: `cd hauling-qr-trip-management/deploy-hauling-qr-ubuntu/`
3. **Configure settings**: Edit `deployment-config.conf` with your domain and settings
4. **Deploy**: `sudo -E ./auto-deploy-complete-fixed.sh` (automatic IP detection included)

#### Main Deployment Files

- `auto-deploy-complete-fixed.sh` - **Enhanced deployment script** with automatic IP detection and comprehensive automation
- `deployment-config.conf` - **Configuration file** with your deployment settings
- `Install-Ubuntu-2404.ps1` - **PowerShell installation script** for Windows environments

#### Quick Deployment

1. **SSH into your Ubuntu 24.04 server as root**

2. **Clone the repository and access deployment files**

   ```bash
   # Clone the repository
   git clone https://github.com/mightybadz18/hauling-qr-trip-management.git
   cd hauling-qr-trip-management/deploy-hauling-qr-ubuntu/
   
   # Make the deployment script executable
   chmod +x auto-deploy-complete-fixed.sh
   ```

3. **Configure deployment settings**
   ```bash
   # Edit the configuration file with your settings
   nano deployment-config.conf
   
   # Set environment variables for GitHub authentication (if needed)
   export GITHUB_PAT="your_github_token"
   export PRODUCTION_DOMAIN="yourdomain.com"
   ```

4. **Run the enhanced deployment**
   ```bash
   # Enhanced deployment with automatic IP detection
   sudo -E ./auto-deploy-complete-fixed.sh
   
   # With custom configuration file
   sudo -E ./auto-deploy-complete-fixed.sh --config deployment-config.conf
   
   # With manual IP override (if auto-detection fails)
   export MANUAL_IP="your.server.ip"
   sudo -E ./auto-deploy-complete-fixed.sh
   ```

#### Advanced Deployment Options

**Configuration File Deployment:**
```bash
# Download configuration template (supports .conf, .json, .yaml)
wget https://raw.githubusercontent.com/your-org/hauling-qr-trip-system/main/deploy-hauling-qr-ubuntu/deployment-config-template.conf

# Customize and run
cp deployment-config-template.conf my-config.conf
nano my-config.conf
./deploy-hauling-qr-ubuntu.sh --config my-config.conf
```

**Enhanced Deployment Features:**
```bash
# Automatic VPS IP detection with multiple fallback methods
sudo -E ./auto-deploy-complete-fixed.sh

# With custom domain configuration
export PRODUCTION_DOMAIN="yourdomain.com"
sudo -E ./auto-deploy-complete-fixed.sh

# With GitHub PAT for private repository access
export GITHUB_PAT="your_github_token"
sudo -E ./auto-deploy-complete-fixed.sh

# Manual IP override (only if auto-detection fails)
export MANUAL_IP="your.server.ip"
sudo -E ./auto-deploy-complete-fixed.sh

# With configuration file
sudo -E ./auto-deploy-complete-fixed.sh --config deployment-config.conf
```

#### Enhanced Deployment Features

- **🔍 Automatic VPS IP Detection**: Works on any VPS provider (DigitalOcean, AWS, Linode, Vultr, etc.)
- **🌐 VPS Provider Agnostic**: Zero hardcoded IPs, dynamic configuration everywhere
- **⚙️ Development-Style Production**: Preserves development flexibility with production security
- **🚀 Single Command Deployment**: Complete automation with `sudo -E ./auto-deploy-complete-fixed.sh`
- **🔧 Comprehensive Error Handling**: Detailed logging and automatic recovery mechanisms
- **🏥 Health Monitoring**: Automated health checks and system verification
- **🔒 Security Hardening**: UFW firewall, secure configurations, and production-ready setup
- **📊 Performance Optimization**: Nginx reverse proxy, PM2 process management, and database optimization
- **🌏 Timezone Configuration**: Automatic Philippine Standard Time (Asia/Manila) setup
- **☁️ Cloudflare Ready**: SSL termination and CDN optimization support
- **CI/CD Integration**: Complete automation support with structured output, exit codes, and quiet modes
- **✅ Idempotency Features (100% Complete)**: Component detection, skip logic, configuration backups, and comprehensive rollback functionality fully implemented
- **✅ Rollback System**: Complete rollback functionality with automatic backups, service management, and state validation

## 📚 Documentation

### Current System Documentation
- **[Developer Quick Start](docs/DEVELOPER_QUICK_START.md)** - Get running in 5 minutes with step-by-step guide
- **[Current System Status](docs/CURRENT_SYSTEM_STATUS.md)** - Complete overview of recent updates and current capabilities
- **[API Endpoints](docs/API_ENDPOINTS_CURRENT.md)** - Comprehensive API documentation with examples
- **[Deployment Script Maintenance](docs/DEPLOYMENT_SCRIPT_MAINTENANCE.md)** - Ongoing maintenance guide for deployment scripts

### Deployment Resources
- **[Ubuntu Deployment Directory](deploy-hauling-qr-ubuntu/README.md)** - Complete deployment directory overview with automatic IP detection
- **[Ubuntu VPS Deployment Guide](deploy-hauling-qr-ubuntu/UBUNTU_VPS_DEPLOYMENT_GUIDE.md)** - Comprehensive step-by-step deployment instructions
- **[Auto Deployment Guide](deploy-hauling-qr-ubuntu/AUTO_DEPLOYMENT.md)** - Enhanced production deployment with automatic IP detection
- **[Cloudflare DNS Setup Guide](deploy-hauling-qr-ubuntu/CLOUDFLARE_DNS_SETUP_GUIDE.md)** - Complete Cloudflare configuration instructions
- **[Troubleshooting Guide](deploy-hauling-qr-ubuntu/TROUBLESHOOTING_GUIDE.md)** - Comprehensive troubleshooting for all deployment issues

### Technical Documentation
- [Auto Deployment Ubuntu Guide](docs/AUTO_DEPLOYMENT_UBUNTU_GUIDE.md) - Comprehensive deployment system documentation
- [Deployment Idempotency Features](docs/DEPLOYMENT_IDEMPOTENCY_FEATURES.md) - Component detection, backup, and rollback capabilities
- [Deployment Script Updates](deploy-hauling-qr-ubuntu/DEPLOYMENT_SCRIPT_UPDATES.md) - **Required updates for current codebase alignment**

## 📋 Available Scripts

### Root Level Scripts
- `npm start` - Start production server
- `npm run dev` - Start development server with nodemon
- `npm test` - Run test suite with Jest
- `npm run test:mobile` - Run mobile-specific tests
- `npm run test:mobile:watch` - Run mobile tests in watch mode
- `npm run test:mobile:coverage` - Run mobile tests with coverage
- `npm run test:browser-compatibility` - Run browser compatibility tests
- `npm run db:migrate` - Run database migrations
- `node start-system.js` - **Unified startup script** with automatic IP detection

### Client Scripts
- `cd client && npm start` - Start React development server (with HOST=0.0.0.0 for network access)
- `cd client && npm run build` - Build for production (with sourcemap disabled)
- `cd client && npm test` - Run client tests
- `cd client && npm run test:pwa-offline` - Test PWA offline functionality
- `cd client && npm run test:service-worker` - Test service worker PWA mode
- `cd client && npm run test:pwa-all` - Run all PWA tests
- `cd client && npm run start-clean` - Clean startup script
- `cd client && npm run start-silent` - Silent startup for CI/CD

### Server Scripts
- `cd server && npm run dev` - Start server with nodemon
- `cd server && npm test` - Run server tests
- `cd server && npm run test:exceptions` - Run exception workflow tests
- `cd server && npm run test:watch` - Run tests in watch mode
- `cd server && npm run test:coverage` - Run tests with coverage

## 👤 Driver QR Code System

The Driver QR Code System provides a complementary time tracking and identification system that integrates seamlessly with the existing trip management workflow. This system enables drivers to check in and out of their shifts using unique QR codes printed on their ID cards, automatically creating driver-truck associations and tracking precise shift timestamps.

### Key Features

#### Standalone Driver Check-in Interface
- **Public Access**: No login required - accessible at `/driver-connect`
- **Two-Step Process**: Scan driver ID QR code, then scan truck QR code
- **Automatic Status Detection**: System determines check-in vs check-out based on current shift status
- **Mobile-Optimized**: Designed for mobile devices with close-range ID card scanning (4-8 inches)
- **Same Process for Both Actions**: Identical workflow for time-in and time-out operations

#### Driver QR Code Management
- **ID Card Integration**: QR codes designed for printing on driver ID cards with lamination protection
- **Simple Authentication**: QR codes contain driver_id and employee_id for basic validation
- **Admin Generation**: Administrators can generate and manage driver QR codes through dashboard
- **Active Status Validation**: Only active drivers and trucks can be used for check-in operations
- **Security Features**: Tamper-resistant QR code format with basic encryption

#### Automatic Shift Creation and Handovers
- **Real-time Shift Tracking**: Automatically creates driver shifts when connecting to trucks
- **Multiple Driver Support**: Handles multiple drivers working on the same truck throughout the day
- **Automatic Handovers**: When Driver B connects to Driver A's truck, automatically ends A's shift and starts B's shift
- **Seamless Integration**: Enhances existing assignment system with real driver connection data
- **Precise Time Tracking**: Records exact check-in/check-out timestamps for payroll and attendance
- **Emergency Handling**: Supervisors can manually end shifts for accident/emergency situations

#### Driver Attendance Reporting
- **Duration Calculation**: Automatic calculation of shift durations using simple time arithmetic (end_time - start_time)
- **Attendance Records**: Comprehensive attendance tracking with filtering by driver, date range, truck, and duration
- **Payroll Integration**: Detailed reports for payroll processing and management oversight
- **Historical Data**: Complete history of driver work patterns and truck assignments
- **Multiple Shifts Support**: Tracks multiple shifts per day for the same driver

### Accessing Driver Features

#### For Drivers (Public Access)
- Navigate to `/driver-connect` (no login required)
- **Step 1**: Scan QR code on back of driver ID card for authentication
- **Step 2**: Scan truck QR code to check in (if not currently checked in) or check out (if currently checked in to same truck)
- Receive immediate confirmation showing action taken and duration (for check-out)
- **Different Truck Handling**: Scanning different truck automatically ends current shift and starts new one

#### For Administrators
1. **Driver QR Management**: Settings → Driver Management → Generate QR codes for printing on ID cards
2. **Attendance Reports**: Navigate to Driver Attendance page for duration reports, filtering, and payroll data
3. **Emergency Management**: Manual shift completion through admin interface for accident/emergency situations
4. **System Integration**: Automatic shifts enhance existing assignment and trip management systems

## 🎨 Appearance Customization

The system includes comprehensive appearance customization options accessible through the Settings panel.

### Customizable Elements

#### Logo Settings
- **Custom Logo URL**: Upload and display your organization's logo
- **Logo Dimensions**: Adjustable width and height (20-200px)
- **Alt Text**: Customizable accessibility text
- **Live Preview**: Real-time preview of logo changes

#### Typography Settings
- **Font Families**: Choose from 8 professional font options including Inter, Arial, Georgia, and more
- **Font Sizes**: Customizable sizes for headers (18-32px), content (12-22px), and footer (10-18px)
- **Font Weights**: Light, Normal, Medium, Semi Bold, and Bold options
- **Separate Controls**: Independent settings for headers, content, and footer text

#### Visual Features
- **Preview Mode**: Test changes before applying them permanently
- **Live Updates**: See changes applied in real-time with CSS custom properties
- **Persistent Storage**: Settings saved to localStorage and applied across sessions
- **Reset Option**: One-click reset to default appearance settings

### CSS Custom Properties

The appearance system uses CSS custom properties for dynamic theming:

```css
/* Font Properties */
--font-header-family: 'Inter, system-ui, sans-serif'
--font-header-size: '24px'
--font-header-weight: '600'
--font-content-family: 'Inter, system-ui, sans-serif'
--font-content-size: '16px'
--font-content-weight: '400'
--font-footer-family: 'Inter, system-ui, sans-serif'
--font-footer-size: '14px'
--font-footer-weight: '400'

/* Logo Properties */
--logo-width: '40px'
--logo-height: '40px'
```

### Accessing Appearance Settings

1. Navigate to **Settings** from the main menu
2. Click on **🎨 Appearance Settings**
3. Customize logo, fonts, and visual elements
4. Use **Preview Mode** to test changes
5. Click **Save Settings** to apply permanently

## 🔐 Role-Based Access Control (RBAC)

The system includes a comprehensive role-based access control system that allows administrators to manage user roles and configure granular page access permissions. This feature is currently in active development with detailed implementation planning.

### Key Features

#### Role Management
- **Dynamic Role Types**: Create, edit, and delete user role types through an intuitive interface
- **User Role Enum Integration**: Seamlessly integrates with the existing user_role enum system
- **Safe Deletion**: Prevents deletion of roles that are assigned to users
- **Role Assignment**: Created roles are available for assignment in the Users page

#### Permission Management
- **Page-Level Permissions**: Configure access permissions for each application page/route
- **Checkbox Interface**: Simple checkbox matrix for granting/revoking page access
- **Comprehensive Coverage**: Includes Dashboard, Trips, Assignments, Shifts, Analytics, Settings, and all main navigation pages
- **Settings Subpages**: Separate permissions for User Management, Shift Management, and other admin tools

#### Access Control Enforcement
- **Route Protection**: Frontend and backend route protection based on user roles
- **Navigation Filtering**: Menu items only show for pages the user's role can access
- **Direct URL Protection**: Enforces permission checks for direct URL access attempts
- **JWT Integration**: Seamlessly integrates with existing JWT authentication system

### Implementation Status

The RBAC system is currently in active development with detailed implementation planning:

#### Phase 1: Database Foundation
- ✅ **Requirements Analysis**: Comprehensive requirements document completed
- ✅ **System Design**: Architecture and database schema designed
- ✅ **Implementation Planning**: Detailed task breakdown with specific file paths
- ⏳ **Database Migration**: `database/migrations/017_role_based_access_control.sql`
  - Create role_permissions table with role_name and page_key columns
  - Add PostgreSQL functions for safe enum CRUD operations
  - Insert default permissions for existing roles

#### Phase 2: Backend API Development
- ⏳ **Role Management API**: `server/routes/roles.js`
  - GET /api/roles - Get all user_role enum values
  - POST /api/roles - Add new role to enum
  - DELETE /api/roles/:name - Remove role with safety checks
- ⏳ **Permission Management API**: `server/routes/permissions.js`
  - GET /api/permissions - Get all role-page permissions
  - POST /api/permissions - Bulk update permissions
- ⏳ **Server Integration**: Update `server/server.js` with new route imports

#### Phase 3: Frontend Interface Development
- ⏳ **UserRolesManagement Component**: `client/src/pages/settings/components/UserRolesManagement.js`
  - Role CRUD operations with user count validation
  - Permission matrix with checkbox interface
- ⏳ **Settings Integration**: Update `client/src/pages/settings/Settings.js`
- ⏳ **Permission Hook**: `client/src/hooks/usePermissions.js`

#### Phase 4: Access Control Implementation
- ⏳ **Route Protection Middleware**: `server/middleware/permissions.js`
- ⏳ **Navigation Updates**: Modify `client/src/components/layout/Sidebar.js`
- ⏳ **Route Guards**: Update `client/src/components/layout/DashboardLayout.js`

#### Phase 5: Authentication Integration
- ⏳ **Auth Enhancement**: Update `server/routes/auth.js` to include permissions
- ⏳ **Context Updates**: Enhance `client/src/context/AuthContext.js`
- ⏳ **Testing**: Comprehensive testing of role CRUD and permission enforcement

### Database Schema

The RBAC system uses a simple two-table approach:

#### role_permissions table
```sql
CREATE TABLE role_permissions (
    id SERIAL PRIMARY KEY,
    role_name user_role NOT NULL,
    page_key VARCHAR(100) NOT NULL,
    has_access BOOLEAN DEFAULT false,
    UNIQUE(role_name, page_key)
);
```

#### Enhanced user_role enum
- Maintains existing enum structure for backward compatibility
- Adds PostgreSQL functions for safe CRUD operations on enum values
- Supports dynamic role creation and deletion

### Available Pages for Permission Control

- **Dashboard**: Main system dashboard and overview
- **Users**: User management and administration
- **Trips**: Trip monitoring and management
- **Assignments**: Assignment creation and tracking
- **Shifts**: Shift management and scheduling
- **Analytics**: Performance metrics and reporting
- **Settings**: System configuration and admin tools

### Future Enhancements

- **Granular Permissions**: Sub-page and feature-level permissions
- **Role Templates**: Pre-configured role templates for common use cases
- **Permission Inheritance**: Hierarchical role structures with permission inheritance
- **Audit Logging**: Comprehensive logging of role and permission changes

## 🚛 Fleet Resource Monitor

The Fleet Resource Monitor provides real-time visibility into driver and dump truck resource allocation, helping fleet managers and dispatchers quickly identify resource shortages, over-allocation, or imbalances between active shifts and available resources.

### Key Features

#### Real-time Resource Tracking
- **Driver Availability**: Total available drivers vs. assigned to active shifts vs. unassigned
- **Truck Utilization**: Total available trucks vs. assigned to active shifts vs. on routes
- **Loading Location Breakdown**: Truck assignments grouped by loading location with specific truck numbers
- **Route Monitoring**: Trucks currently in transit with loading/unloading locations and trip times
- **Resource Alerts**: Visual warnings for resource shortages and critical utilization issues

#### Comprehensive Resource Views
- **Summary Cards**: High-level overview of driver and truck counts with status indicators
- **Detailed Tables**: Unassigned drivers with names/employee IDs, unassigned trucks with numbers
- **Location Analysis**: Truck distribution across loading locations with at-location vs. on-route status
- **Historical Trends**: Resource utilization patterns over time for planning purposes
- **Export Capabilities**: Downloadable reports for stakeholder sharing and operational records

#### Mobile-Optimized Interface
- **Responsive Design**: Touch-friendly controls optimized for mobile devices and tablets
- **Real-time Updates**: WebSocket integration for live resource status changes
- **Offline Support**: Graceful handling of network connectivity issues
- **Quick Access**: Fast loading and intuitive navigation for on-site use

### Accessing Fleet Resource Monitor

1. Navigate to **Fleet Resources** from the main menu
2. View real-time resource summary cards for drivers and trucks
3. Expand loading location sections to see detailed truck assignments
4. Monitor trucks currently on routes with trip information
5. Export resource utilization reports as needed

## 🏥 System Health Monitoring

The System Health Monitoring feature provides comprehensive real-time monitoring and automated fixing capabilities for four core modules: Shift Management, Assignment Management, Trip Monitoring, and Database Health.

### Key Features

#### Centralized Health Dashboard
- **Real-time Status Indicators**: ✅ Operational, ⚠️ Issues Detected, ❌ Critical
- **Module-specific Monitoring**: Individual health checks for Shifts, Assignments, Trips, and Database
- **Automated Issue Detection**: Proactive identification of system inconsistencies
- **One-click Fixes**: Automated resolution of common issues
- **Mobile-First Design**: Fully responsive interface optimized for mobile devices and tablets
- **Accessibility Compliant**: WCAG AA compliant with comprehensive screen reader support

#### Shift Management Integration
- **Real-time Shift Verification**: Monitors day shifts (6 AM-6 PM) and night shifts (6 PM-6 AM)
- **Automatic Status Correction**: Executes `schedule_auto_activation()` database function
- **Cross-midnight Handling**: Correctly processes overnight shift transitions
- **Status Synchronization**: Ensures shift displays match actual status

#### Assignment Management Integration
- **Display Issue Detection**: Identifies "⚠️ No Active Shift" errors during active hours
- **Automatic Synchronization**: Aligns assignment displays with active shifts
- **Trip Monitoring Integration**: Updates truck assignment displays in real-time
- **Overnight Shift Support**: Handles "✅ night Shift Active" status correctly

#### Trip Monitoring Integration
- **Workflow Integrity Checks**: Verifies PENDING → IN_PROGRESS → COMPLETED → VERIFIED transitions
- **Driver Status Verification**: Validates driver assignments for dump trucks
- **Real-time Updates**: Automatic refresh of trip monitoring data
- **Critical Issue Alerts**: Immediate notifications for workflow problems

### Accessing System Health Monitor

1. Navigate to **Settings** from the main menu
2. Click on **🏥 System Health Monitor**
3. View real-time status for all modules
4. Click **Fix Issues** buttons to resolve problems automatically
5. Monitor task management and cleanup operations

## ⏱️ Manual Shift Completion

The Manual Shift Completion feature provides administrators with a comprehensive interface for manually managing driver shifts when automated processes need override or intervention.

### Key Features

#### Administrative Control Interface
- **Active Shifts Management**: View and manually complete active driver shifts
- **Scheduled Shifts Management**: View and cancel scheduled shifts before they begin
- **Completion Dialogs**: Confirmation dialogs with optional completion notes
- **Status Summary**: Real-time count of active, scheduled, and completed shifts
- **Refresh Functionality**: Manual refresh of shift statuses and displays

#### Shift Operations
- **Manual Completion**: Complete active shifts with optional notes and confirmation
- **Shift Cancellation**: Cancel both active and scheduled shifts with reason tracking
- **Status Monitoring**: Real-time display of shift counts and current status
- **Transaction Safety**: All operations use database transactions for data integrity

#### User Interface
- **Tabbed Interface**: Separate tabs for Active Shifts and Scheduled Shifts
- **Mobile-Optimized**: Responsive design for mobile devices and tablets
- **Status Cards**: Visual summary cards showing shift counts at a glance
- **Action Buttons**: Color-coded Complete (green) and Cancel (red) buttons

### Accessing Manual Shift Management

1. Navigate to **Settings** from the main menu
2. Click on **⏱️ Manual Shift Management**
3. View active shifts in the "Active Shifts" tab
4. Switch to "Scheduled Shifts" tab to view upcoming shifts
5. Use Complete/Cancel buttons to manage individual shifts
6. Click "Refresh Statuses" to update displays

### Implementation Details

#### Frontend Component
- **Location**: `client/src/pages/settings/components/ManualShiftManagement.jsx`
- **Features**: Complete/Cancel dialogs, tabbed interface, status summary
- **Integration**: Accessible through Settings menu with dedicated icon

#### Backend API
- **Routes**: `server/routes/manual-shift-management.js`
- **Endpoints**: `/active`, `/scheduled`, `/complete/:id`, `/cancel/:id`, `/summary`, `/refresh`
- **Authentication**: Configurable authentication middleware (temporarily disabled for testing)

#### Service Layer
- **Service**: `server/services/ManualShiftManagementService.js`
- **Methods**: `completeShift()`, `cancelShift()`, `getActiveShifts()`, `getScheduledShifts()`
- **Features**: Transaction support, comprehensive logging, data validation

### Testing Manual Completion

```bash
# Start the system
npm run dev

# Access the interface at:
# http://localhost:3000 → Settings → Manual Shift Management

# Test operations:
# 1. View active shifts
# 2. Complete a shift with optional notes
# 3. Cancel scheduled shifts
# 4. Refresh status displays
```

### API Endpoints

- `GET /api/manual-shift-management/active` - Get all active shifts
- `GET /api/manual-shift-management/scheduled` - Get all scheduled shifts
- `POST /api/manual-shift-management/complete/:id` - Complete a shift
- `POST /api/manual-shift-management/cancel/:id` - Cancel a shift
- `GET /api/manual-shift-management/summary` - Get shift status summary
- `POST /api/manual-shift-management/refresh` - Refresh shift statuses

### Recent Fixes and Improvements

#### Driver Attendance API Fixes (January 2025)
- **Empty String Parameter Handling**: Fixed 400 Bad Request errors when frontend sends empty string parameters (`driver_id=&date_from=&`)
- **Database Query Optimization**: Removed restrictive `status = 'completed'` filter to show all shifts in attendance summary
- **Enhanced Validation**: Added helper function to clean empty strings before validation and improved parameter type handling
- **Improved Error Handling**: Added comprehensive debugging logs and better error messages
- **API Robustness**: All attendance endpoints now properly handle empty parameters without errors
- **Debug Endpoint**: Added `/api/driver-admin/debug` endpoint for troubleshooting database connectivity and data integrity

For detailed technical information, see:
- [DRIVER_ATTENDANCE_EMPTY_STRING_FIX.md](DRIVER_ATTENDANCE_EMPTY_STRING_FIX.md) - Latest empty string parameter fix
- [DRIVER_ATTENDANCE_FIX.md](DRIVER_ATTENDANCE_FIX.md) - Earlier validation and query improvements

### Automated Maintenance Features

#### Task Management System
- **Pending Task Tracking**: Monitors maintenance tasks with priority levels
- **Automated Scheduling**: Schedules cleanup and optimization tasks
- **Trend Analysis**: Provides system health recommendations
- **Task History**: Complete audit trail of maintenance activities

#### Code Cleanup Automation
- **Unused Function Detection**: Scans `server/**/*.js` and `scripts/**/*.js` files
- **Safety Preservation**: Protects critical functions (routes, middleware, database operations)
- **Backup and Rollback**: Automatic backup creation with rollback capabilities
- **Impact Analysis**: Detailed reports of cleanup operations

### API Endpoints

The System Health Monitor integrates with dedicated API endpoints:

- `GET /api/system-health/status` - Current health status for all modules
- `POST /api/system-health/fix-shifts` - Execute automated shift fixes
- `POST /api/system-health/fix-assignments` - Synchronize assignment displays
- `POST /api/system-health/fix-trips` - Resolve trip workflow issues
- `GET /api/tasks` - Task management and recommendations
- `POST /api/cleanup/analyze` - Code cleanup analysis
- `POST /api/cleanup/execute` - Execute cleanup operations

### Monitoring and Alerting

- **Automated Health Checks**: Run every 15 minutes during business hours
- **Critical Issue Alerts**: Immediate notifications for system problems
- **Trend Analysis**: Early warning for performance degradation
- **Escalation Management**: Manual intervention for failed automated fixes

## 📚 Documentation

Comprehensive documentation is available in the `/docs` directory:

### Setup and Deployment
- [Development Setup Guide](docs/DEVELOPMENT_SETUP.md) - Complete development environment setup
- [Auto Deployment Ubuntu Guide](docs/AUTO_DEPLOYMENT_UBUNTU_GUIDE.md) - Advanced automated deployment system
- [Deployment Idempotency Features](docs/DEPLOYMENT_IDEMPOTENCY_FEATURES.md) - Component detection, backups, and rollback capabilities
- [Database Migration Guide](docs/DATABASE_MIGRATION_GUIDE.md) - Database schema and migration management

### System Features
- [Driver QR Code System Guide](docs/DRIVER_QR_CODE_SYSTEM_GUIDE.md) - Comprehensive driver time tracking and identification system
- [System Health Monitoring Guide](docs/SYSTEM_HEALTH_MONITORING_GUIDE.md) - Health monitoring and automated maintenance
- [Manual Shift Management Guide](docs/MANUAL_SHIFT_MANAGEMENT_GUIDE.md) - Administrative shift management interface
- [Appearance Settings Guide](docs/APPEARANCE_SETTINGS_GUIDE.md) - Logo and typography customization
- [Validation Error Handling Guide](docs/VALIDATION_ERROR_HANDLING_GUIDE.md) - Enhanced error handling system

### Technical Documentation
- [API Documentation](docs/API_DOCUMENTATION.md) - Complete API reference
- [Multi-Location Workflow Implementation](docs/MULTI_LOCATION_WORKFLOW_IMPLEMENTATION.md) - Advanced workflow patterns
- [Shift Management System](docs/SHIFT_MANAGEMENT_SYSTEM.md) - Core shift management architecture
- [Performance Security Validation Guide](docs/PERFORMANCE_SECURITY_VALIDATION_GUIDE.md) - Deployment validation testing suite
- [Kiro Agent Configuration](docs/KIRO_AGENT_CONFIGURATION.md) - AI assistant configuration guide
- [MCP Integration Guide](docs/MCP_INTEGRATION_GUIDE.md) - Model Context Protocol integration

## 🔧 Configuration

### Environment Variables

The system uses a unified `.env` file for configuration:

```env
# Environment
NODE_ENV=development
AUTO_DETECT_IP=true
ENABLE_HTTPS=false

# Server Configuration
HOST=0.0.0.0
PORT=5000
BACKEND_HTTP_PORT=5000

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=hauling_qr_system
DB_USER=hauling_user
DB_PASSWORD=your_password

# Security
JWT_SECRET=your_jwt_secret
JWT_EXPIRY=24h

# Client Configuration
REACT_APP_API_URL=http://localhost:5000/api
REACT_APP_WS_URL=ws://localhost:5000

# Features
ENABLE_MONITORING=true
ENABLE_BACKUPS=true
```

### Port Configuration
- **Frontend**: Always port 3000 (HTTP/HTTPS)
- **Backend**: Always port 5000 (HTTP/HTTPS)
- **Database**: PostgreSQL on port 5432

## 📊 Core Workflows

### Standard Trip Workflow
1. **Assignment Creation**: Dispatcher assigns truck to route
2. **Loading Start**: Driver scans QR at pickup location
3. **Loading End**: Driver confirms loading completion
4. **Unloading Start**: Driver scans QR at destination
5. **Unloading End**: Driver confirms delivery completion
6. **Trip Completed**: System marks trip as completed

### Multi-Location Workflows

#### A→B→C Extensions
- After completing A→B, truck continues to Point C
- Automatic assignment creation for new route
- Baseline trip marked as "Auto Completed"

#### C→B→C Cycles
- Continuous cycles: load at Point C, unload at Point B, return to Point C
- Sequential cycle numbering (#1, #2, #3...)
- Previous trip marked as "Auto Completed"

## 🔍 QR Code Standards

QR codes follow the format: `{location_id}:{timestamp}:{verification_hash}`

- **Client-side validation** before server submission
- **Fallback mechanisms** for poor lighting/camera conditions
- **Manual code entry** support
- **Consistent format** across all locations

## 📱 Mobile Optimization

- **Mobile-first design** approach
- **Touch-friendly controls** (minimum 44px touch targets)
- **One-handed operation** optimization
- **Responsive layouts** for all screen sizes
- **Offline mode handling** for poor connectivity

## 🔒 Security Features

- **JWT Authentication** with proper expiration handling
- **Parameterized queries** to prevent SQL injection
- **Rate limiting** on API endpoints
- **CORS configuration** for cross-origin requests
- **Helmet.js** for security headers
- **Audit logging** for critical operations
- **Enhanced Error Handling** with proper HTTP status codes and structured validation errors

## ⚠️ Enhanced Error Handling & Validation

The system features comprehensive error handling that distinguishes between validation errors (business rule violations) and system errors, providing users with clear, actionable guidance.

### Key Features

#### Structured Error Responses
- **Validation Errors (400 Bad Request)**: Business rule violations with actionable guidance
- **System Errors (500 Internal Server Error)**: Actual system failures requiring technical intervention
- **Error Type Classification**: `error_type` field distinguishes between `validation` and `system` errors
- **Detailed Context**: Structured error details with field-specific information and suggestions

#### Client-Side Error Handling
- **Visual Distinction**: Orange styling for validation errors, red for system errors
- **Extended Display Time**: 10 seconds for workflow violations vs 6-8 seconds for other errors
- **Next Steps Display**: Bulleted list of actionable steps for workflow violations
- **Context Preservation**: Location data preserved during error scenarios

#### 4-Phase Workflow Validation
- **Proper HTTP Status**: Workflow violations return 400 (validation error) instead of 500 (system error)
- **Actionable Guidance**: Clear next steps for resolving workflow violations
- **Context Awareness**: Error messages include current location and workflow state
- **User-Friendly Messages**: Technical validation errors translated to user-friendly guidance

### Error Response Examples

#### Validation Error (400 Bad Request)
```json
{
  "success": false,
  "error": "Validation Error",
  "error_type": "validation",
  "message": "4-Phase Workflow Violation: Cannot perform loading operation at unloading location",
  "details": {
    "type": "workflow_violation",
    "current_phase": "unloading_end",
    "required_action": "Must scan truck QR at loading location",
    "location_name": "Point B - Primary Dump Site",
    "location_type": "unloading"
  }
}
```

#### System Error (500 Internal Server Error)
```json
{
  "success": false,
  "error": "Scan Processing Error",
  "error_type": "system",
  "message": "Database connection failed",
  "processing_time_ms": 1250
}
```

### Implementation Details

#### ValidationError Class
- **Custom Error Type**: `server/utils/ValidationError.js` provides structured validation errors
- **Stack Trace Capture**: Proper error tracking for debugging
- **Structured Details**: Field-specific error information and suggestions
- **Static Factory Methods**: Convenient error creation for common scenarios

#### Enhanced Server Logic
- **Error Classification**: Automatic detection of validation vs system errors
- **Proper HTTP Status Codes**: 400 for validation, 500 for system errors
- **Structured Logging**: Different log levels and contexts for error types
- **Transaction Safety**: Proper rollback handling for both error types

#### Client Error Handling
- **Error Type Detection**: Checks `error_type` field for appropriate handling
- **Workflow-Specific UI**: Special handling for 4-phase workflow violations
- **Visual Feedback**: Color-coded error messages with appropriate icons
- **Accessibility**: Screen reader compatible error announcements

## 📈 Performance Standards

- **Database queries**: Complete within 500ms for dashboard operations
- **Mobile page loads**: Within 3 seconds on 3G connections
- **Connection pooling**: For database operations
- **Caching strategies**: For frequently accessed data

## 🧪 Testing

### Standard Test Suite
```bash
# Run all tests
npm test

# Server-specific tests
cd server && npm test
cd server && npm run test:coverage

# Client tests
cd client && npm test

# Integration tests
npm run test:integration

# Check database migrations status
node check-migrations.js
```

### Deployment Testing Suite

The system includes comprehensive testing for the Ubuntu deployment script with WSL compatibility:

#### Performance and Security Validation Suite
A comprehensive validation suite for testing deployment performance and security across different VPS configurations:

```bash
# Make validation script executable
chmod +x performance-security-validation.sh

# Run all validation tests
./performance-security-validation.sh --test-all --verbose

# Run specific validation categories
./performance-security-validation.sh --test-performance --verbose
./performance-security-validation.sh --test-security
./performance-security-validation.sh --test-cloudflare
./performance-security-validation.sh --test-monitoring
```

#### WSL Testing Setup
For Windows developers using WSL (Windows Subsystem for Linux), comprehensive testing is available without requiring actual Ubuntu server access:

```bash
# Make test script executable
chmod +x test-deployment-wsl.sh

# Run comprehensive test suite
./test-deployment-wsl.sh --test-all --mock-services --verbose

# Run specific test categories
./test-deployment-wsl.sh --test-config --verbose
./test-deployment-wsl.sh --test-detection --mock-services
./test-deployment-wsl.sh --test-backup
./test-deployment-wsl.sh --test-rollback
```

#### Test Categories Covered

**Configuration Testing**
- ✅ Shell configuration format (`.conf`)
- ✅ JSON configuration format (`.json`)
- ✅ YAML configuration format (`.yaml`)
- ✅ Configuration validation and error handling
- ✅ Default value handling

**Component Detection Testing**
- ✅ Node.js version detection and compatibility checking
- ✅ NPM version detection and compatibility checking
- ✅ Nginx version detection (mocked)
- ✅ PostgreSQL version detection (mocked)
- ✅ PM2 version detection (mocked)
- ✅ Component status reporting

**Backup & Rollback Testing**
- ✅ Backup directory creation and structure
- ✅ Configuration file backup with metadata
- ✅ Backup integrity verification
- ✅ Rollback command line option parsing
- ✅ Service state management during rollback

**Deployment Modes Testing**
- ✅ Interactive mode simulation
- ✅ Non-interactive mode testing
- ✅ Dry-run mode validation
- ✅ CI/CD mode compatibility

#### Additional Test Scripts

```bash
# Performance and security validation suite
./performance-security-validation.sh --test-all --verbose

# Test component detection functions
./test-component-detection.sh

# Test backup functionality
./test-backup-functions.sh

# Test rollback functionality
./test-rollback-functions.sh

# Comprehensive deployment test suite
./test-deployment-suite.sh --test-all --verbose
```

#### WSL-Specific Features

- **Mock Services**: Simulates Nginx, PostgreSQL, PM2 without actual installations
- **Safe Testing Environment**: All tests run in isolated `/tmp` directories
- **Permission Handling**: Accounts for WSL file system permissions
- **Network Simulation**: Mock network configurations and SSL certificates

For detailed WSL testing setup instructions, see [WSL_TESTING_SETUP.md](WSL_TESTING_SETUP.md)

### Database Migration Monitoring

The system includes a comprehensive migration checker that validates database schema integrity and migration status:

#### Migration Status Checker
```bash
# Check current migration status
node check-migrations.js
```

**What it checks:**
- ✅ Recent migrations applied to the database (last 10)
- ✅ Specific migration verification (e.g., Migration 057 status)
- ✅ Database function integrity and signatures
- ✅ Problematic patterns in shift management functions
- ✅ Migration table consistency

**Sample Output:**
```
🔍 Checking applied migrations...

📋 Recent migrations:
  ✅ 057_remove_automatic_completed_status.sql - 2025-01-15 14:30:45
  ✅ 056_fix_shift_status_logic.sql - 2025-01-14 09:15:22
  ✅ 055_update_assignment_display.sql - 2025-01-13 16:45:10

🎯 Migration 057 status:
  ✅ Migration 057 has been applied
  📅 Applied at: 2025-01-15 14:30:45

🔧 Checking database functions...
  ✅ Functions found:
    - evaluate_shift_status
      ✅ Function appears to be corrected (no automatic completion)
    - schedule_auto_activation
```

#### Key Features

- **Parallel Processing**: Uses `Promise.allSettled()` for efficient concurrent checks
- **Error Resilience**: Individual check failures don't stop the entire process
- **Function Analysis**: Detects problematic patterns like automatic completion logic
- **Migration History**: Shows recent migration timeline with timestamps
- **Status Verification**: Confirms specific migrations have been applied correctly

#### Integration with Development Workflow

The migration checker is now integrated into the Kiro IDE workflow and can be triggered:
- Manually via command line: `node check-migrations.js`
- Through Kiro IDE command palette
- As part of automated testing suites
- During troubleshooting database issues

### Database Migration Consolidation

The system currently has 61 migration files with identified consolidation opportunities to improve maintainability and performance:

#### Current Migration Analysis
- **Total migrations**: 61 files (001-060, with one duplicate 060)
- **Consolidation potential**: Reduce to approximately 35-40 migration files
- **Major issues**: Redundant fixes, multiple attempts at same problems, scattered related changes

#### Key Consolidation Groups Identified
1. **Approvals Table Schema Fixes** (4 migrations) - Multiple attempts to fix the same schema issues
2. **Scan Logs Foreign Key Fixes** (2 migrations) - Duplicate constraint fixes
3. **Assignment Table Enhancements** (5 migrations) - Scattered modifications including add/remove cycles
4. **Shift Management System** (6 migrations) - Related features spread across multiple files
5. **Debug Function Fixes** (4 migrations) - Multiple attempts to fix the same debug function
6. **System Health & Logging** (4 migrations) - Related monitoring features

#### Benefits of Consolidation
- **Performance**: Faster migration execution with fewer redundant operations
- **Maintainability**: Cleaner, more logical migration sequence
- **Understanding**: Easier to comprehend system evolution
- **Reliability**: Elimination of conflicting or redundant operations

For detailed consolidation analysis, see [Migration Analysis](database/migrations_analysis.md).

### Comprehensive System Testing

The system includes multiple comprehensive test scripts that validate different aspects of the shift management system:

#### Complete System Test
```bash
# Run complete system test
node scripts/test-complete-system.js
```

**What it tests:**
- ✅ Database function integrity (schedule_auto_activation)
- ✅ Current shift status display across all trucks
- ✅ Assignment display logic for Trip Monitoring
- ✅ Time context validation (day/night shift logic)
- ✅ Function signature verification
- ✅ Shift status statistics and counts

#### Fix Assignment Button Test
```bash
# Test the "Fix Assignment Display Issues" button functionality
node scripts/test-fix-assignment-button.js
```

**What it tests:**
- ✅ Button click simulation and server endpoint functionality
- ✅ Before/after shift status comparison
- ✅ Assignment display logic verification
- ✅ Server response validation
- ✅ Trip Monitoring display accuracy
- ✅ Function call success without signature errors

**Test Output Example:**
```
🔘 Testing "Fix Assignment Display Issues" Button Functionality...

1. Current Status BEFORE Fix:
   Total shifts: 4
   Active: 2
   Scheduled: 2
   Completed: 0

2. Simulating "Fix Assignment Display Issues" Button Click...
   ✅ Called schedule_auto_activation() function

3. Status AFTER Fix:
   Total shifts: 4
   Active: 2
   Scheduled: 2
   Completed: 0

4. Server Response (what the button would show):
   ✅ Success: true
   📝 Message: "Scheduled activation completed using corrected logic."

5. Assignment Display After Fix:
   Truck   | Driver          | Employee | Trip Monitoring Display
   --------|-----------------|----------|------------------------
   T001    | John Smith      | EMP001   | ✅ day Shift Active
   T002    | Jane Doe        | EMP002   | ✅ night Shift Active

🎯 BUTTON TEST RESULTS:
   Button Functionality: ✅ WORKING PERFECTLY
   Server Endpoint: ✅ Updated and functional
   Function Calls: ✅ No signature errors
   Assignment Display: ✅ Showing active shifts correctly
```

#### Complete Functionality Verification
```bash
# Run comprehensive functionality verification
node scripts/verify-complete-functionality.js
```

**Advanced testing features:**
- ✅ Function signature validation with parameter checking
- ✅ Shift status calculation vs expected status comparison
- ✅ Assignment display logic verification with detailed reporting
- ✅ Time context analysis with hour-by-hour validation
- ✅ Server endpoint integration testing
- ✅ Overall system health assessment with actionable recommendations

**Test Output Example:**
```
🔍 Comprehensive System Verification...

1. Testing Database Functions:
   ✅ schedule_auto_activation() function works
   ✅ Function signatures verified:
      evaluate_shift_status(shift_id integer, check_time timestamp without time zone)
      schedule_auto_activation()
      update_all_shift_statuses()

2. Current Shift Status Verification:
   Truck   | Driver        | Type  | Current   | Calculated | Expected  | Status
   --------|---------------|-------|-----------|------------|-----------|--------
   T001    | John Smith    | day   | active    | active     | ACTIVE    | ✅
   T002    | Jane Doe      | night | scheduled | scheduled  | SCHEDULED | ✅
   Overall Status Logic: ✅ CORRECT

3. Assignment Display Logic (Trip Monitoring):
   Assignment | Truck   | Driver          | Employee | Display Status
   -----------|---------|-----------------|----------|----------------
            1 | T001    | John Smith      | EMP001   | ✅ day Shift Active
            2 | T002    | Jane Doe        | EMP002   | 📅 night Shift Scheduled
   Trip Monitoring Status: ✅ ALL SHIFTS SHOWING CORRECTLY

🎯 VERIFICATION SUMMARY:
=====================================
   DATABASE FUNCTIONS    : ✅ Working
   SHIFT STATUS LOGIC    : ✅ Correct
   ASSIGNMENT DISPLAY    : ✅ Working
   SERVER ENDPOINT       : ✅ Updated
   FUNCTION SIGNATURES   : ✅ Fixed

🎉 OVERALL SYSTEM STATUS:
   ✅ FULLY OPERATIONAL
```

These comprehensive tests help diagnose shift management issues and validate that all components are working correctly together, providing detailed analysis and actionable recommendations for any issues found.

## 📚 Documentation

- **[Database Migration Guide](docs/DATABASE_MIGRATION_GUIDE.md)** - Comprehensive migration system documentation and consolidation analysis
- **[Development Setup Guide](docs/DEVELOPMENT_SETUP.md)** - Complete development environment setup instructions
- **[Deployment Guide for Ubuntu 24.04](DEPLOYMENT_GUIDE_UBUNTU_24.04.md)** - Automated deployment guide for Ubuntu 24.04 VPS
- **[Manual Completion Setup Guide](MANUAL_COMPLETION_SETUP_GUIDE.md)** - Complete setup and testing guide for manual shift completion functionality
- **[System Health Monitoring Guide](docs/SYSTEM_HEALTH_MONITORING_GUIDE.md)** - Comprehensive monitoring and automated fixing for system health
- **[Validation Error Handling Guide](docs/VALIDATION_ERROR_HANDLING_GUIDE.md)** - Enhanced error handling with proper HTTP status codes and user guidance
- **[Shift Management System](docs/SHIFT_MANAGEMENT_SYSTEM.md)** - Comprehensive guide to automated shift management and overnight logic
- **[Appearance Settings Guide](docs/APPEARANCE_SETTINGS_GUIDE.md)** - Complete guide to customizing application appearance
- **[Assignment Shift Fix Guide](docs/ASSIGNMENT_SHIFT_FIX_GUIDE.md)** - Troubleshooting shift display issues
- **[Multi-Location Workflow Implementation](docs/MULTI_LOCATION_WORKFLOW_IMPLEMENTATION.md)** - Advanced workflow features
- **[API Documentation](docs/API_DOCUMENTATION.md)** - Complete API reference with enhanced error handling
- **[Architecture Decision Records](docs/adr/)** - Technical decisions and rationale

## 🚨 Troubleshooting

### Common Issues

#### Shift Display Problems
```bash
# Quick fix for shift cache issues
npm run fix:shift-cache

# Test the fix
npm run test:assignment-shift

# Final comprehensive shift status fix (now available)
node scripts/final-shift-status-fix.js
```

#### Shift Editing Issues ✅ RESOLVED
The "recurrence_pattern is not allowed" validation error has been **permanently fixed** as of July 18, 2025.

**What was fixed:**
- ✅ Updated validation schema to include all database fields
- ✅ Added support for `recurrence_pattern`, `display_type`, `shift_date`, and other missing fields
- ✅ Enhanced field validation with proper data types and constraints
- ✅ Maintained backward compatibility with existing API calls

**Available for editing:**
- Date ranges (`start_date`, `end_date`) - Perfect for extending shifts
- Time schedules (`start_time`, `end_time`) - Change shift hours
- Recurrence patterns (`single`, `daily`, `weekly`, `custom`)
- Driver/truck assignments (`driver_id`, `truck_id`)
- Shift status (`scheduled`, `active`, `completed`, `cancelled`)
- Notes and metadata (`handover_notes`, `completion_notes`, `cancellation_reason`)

**API Usage:**
```bash
PUT /api/shifts/{shift_id}
Headers: { "Authorization": "Bearer <jwt-token>" }
Body: {
  "start_date": "2025-07-20",
  "end_date": "2025-08-15", 
  "start_time": "07:00:00",
  "end_time": "19:00:00",
  "recurrence_pattern": "custom"
}
```

#### Database Connection Issues
1. Verify PostgreSQL is running
2. Check database credentials in `.env`
3. Ensure database exists and migrations are applied

#### QR Code Scanning Issues
1. Check camera permissions
2. Ensure adequate lighting
3. Use manual code entry as fallback
4. Verify QR code format

### Performance Issues
1. Check database query performance
2. Verify connection pooling configuration
3. Monitor WebSocket connections
4. Review caching strategies

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines

- Follow the established project structure
- Write tests for new features
- Update documentation for API changes
- Test on mobile devices
- Maintain performance standards

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 👥 Target Users

- **Fleet managers and dispatchers** - Trip planning and monitoring
- **Drivers** - Mobile trip execution and QR scanning
- **Checkers** - Location verification and workflow management
- **Operations supervisors** - Performance monitoring and analytics
- **System administrators** - Configuration and maintenance

## 🎯 Business Value

- **Eliminates manual trip logging** and reduces paperwork
- **Provides real-time visibility** into fleet operations
- **Automates exception detection** and workflow management
- **Generates comprehensive analytics** and performance reports
- **Improves operational efficiency** and reduces errors
- **Enables data-driven decision making** for fleet optimization

---

For technical support or questions, please refer to the documentation in the `/docs` folder or contact the development team.