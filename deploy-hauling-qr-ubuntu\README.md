# Ubuntu VPS Deployment - Hauling QR Trip System

## 🚀 Quick Deploy

Deploy the Hauling QR Trip System on any Ubuntu VPS with automatic IP detection:

```bash
# Clone and deploy in one go
git clone https://github.com/mightybadz18/hauling-qr-trip-management.git
cd hauling-qr-trip-management/deploy-hauling-qr-ubuntu
chmod +x auto-deploy-enhanced.sh
sudo -E ./auto-deploy-enhanced.sh
```

## 📚 Documentation

### Essential Guides
- **[UBUNTU_VPS_DEPLOYMENT_GUIDE.md](UBUNTU_VPS_DEPLOYMENT_GUIDE.md)** - Complete deployment instructions
- **[TROUBLESHOOTING_GUIDE.md](TROUBLESHOOTING_GUIDE.md)** - Solutions for common issues
- **[CLOUDFLARE_DNS_SETUP_GUIDE.md](CLOUDFLARE_DNS_SETUP_GUIDE.md)** - DNS configuration guide

### Specialized Scripts
- **[README-PERMISSION-SCRIPTS.md](README-PERMISSION-SCRIPTS.md)** - Fix ubuntu user permission issues
- **`fix-permissions-ubuntu-user.sh`** - Automated permission fix
- **`post-reboot-check.sh`** - Post-reboot health check

## 🎯 Key Features

- ✅ **Automatic IP Detection** - Works on any VPS provider
- ✅ **Single Command Deploy** - Zero configuration required
- ✅ **Development-Style Production** - Preserves dev flexibility
- ✅ **VPS Provider Agnostic** - DigitalOcean, AWS, Linode, etc.

## 🔧 Requirements

- Ubuntu 24.04 LTS (or 22.04 LTS)
- 2GB+ RAM, 20GB+ storage
- Root/sudo access
- GitHub PAT (for private repos)

## 🆘 Need Help?

1. **First deployment**: Start with [UBUNTU_VPS_DEPLOYMENT_GUIDE.md](UBUNTU_VPS_DEPLOYMENT_GUIDE.md)
2. **Having issues**: Check [TROUBLESHOOTING_GUIDE.md](TROUBLESHOOTING_GUIDE.md)
3. **Permission problems**: Use [README-PERMISSION-SCRIPTS.md](README-PERMISSION-SCRIPTS.md)
4. **DNS setup**: Follow [CLOUDFLARE_DNS_SETUP_GUIDE.md](CLOUDFLARE_DNS_SETUP_GUIDE.md)

---

**Ready to deploy? Start with the Quick Deploy section above!**