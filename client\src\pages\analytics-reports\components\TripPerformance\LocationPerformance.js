import React, { useState } from 'react';

const LocationPerformanceTable = ({ data, type }) => {
  if (!data || data.length === 0) {
    return (
      <div className="text-center py-8 text-secondary-500">
        <span className="text-4xl block mb-2">📍</span>
        No {type} location data available
      </div>
    );
  }

  const filteredData = data.filter(item => item.locationType === type);

  return (
    <div className="bg-white rounded-lg shadow border border-secondary-200 overflow-hidden">
      <div className="px-6 py-4 bg-secondary-50 border-b border-secondary-200">
        <h3 className="text-lg font-medium text-secondary-900">
          {type === 'loading' ? '⬆️ Loading' : '⬇️ Unloading'} Location Performance
        </h3>
      </div>
      
      {/* Desktop View */}
      <div className="hidden md:block">
        <table className="min-w-full divide-y divide-secondary-200">
          <thead className="bg-secondary-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                Location
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                Trips
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                Avg Duration
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                Efficiency
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                Exceptions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-secondary-200">
            {filteredData.slice(0, 10).map((location, index) => (
              <tr key={index} className="hover:bg-secondary-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-secondary-900">
                    {location.locationName}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-secondary-900">
                    {location.tripCount}
                  </div>
                  <div className="text-xs text-secondary-500">
                    {location.completedCount} completed
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-secondary-900">
                    {location.avgDuration}m
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="text-sm text-secondary-900">
                      {location.efficiency}%
                    </div>
                    <div className="ml-2 w-16 bg-secondary-200 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full ${
                          parseFloat(location.efficiency) >= 90 ? 'bg-green-500' :
                          parseFloat(location.efficiency) >= 75 ? 'bg-yellow-500' : 'bg-red-500'
                        }`}
                        style={{ width: `${location.efficiency}%` }}
                      ></div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    location.exceptionCount > 0 ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'
                  }`}>
                    {location.exceptionCount}
                  </span>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Mobile View */}
      <div className="md:hidden">
        <div className="space-y-4 p-4">
          {filteredData.slice(0, 10).map((location, index) => (
            <div key={index} className="bg-secondary-50 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <div className="font-medium text-secondary-900">
                  {location.locationName}
                </div>
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  parseFloat(location.efficiency) >= 90 ? 'bg-green-100 text-green-800' :
                  parseFloat(location.efficiency) >= 75 ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'
                }`}>
                  {location.efficiency}%
                </span>
              </div>
              
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-secondary-500">Trips:</span>
                  <span className="ml-2 text-secondary-900">{location.tripCount}</span>
                </div>
                <div>
                  <span className="text-secondary-500">Duration:</span>
                  <span className="ml-2 text-secondary-900">{location.avgDuration}m</span>
                </div>
                <div>
                  <span className="text-secondary-500">Completed:</span>
                  <span className="ml-2 text-secondary-900">{location.completedCount}</span>
                </div>
                <div>
                  <span className="text-secondary-500">Exceptions:</span>
                  <span className="ml-2 text-secondary-900">{location.exceptionCount}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

const RoutePatternAnalysis = ({ data }) => {
  if (!data || data.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow border border-secondary-200 p-6">
        <h3 className="text-lg font-medium text-secondary-900 mb-4">
          Route Pattern Analysis
        </h3>
        <div className="text-center py-8 text-secondary-500">
          <span className="text-4xl block mb-2">🛣️</span>
          No route pattern data available
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow border border-secondary-200 p-6">
      <h3 className="text-lg font-medium text-secondary-900 mb-4">
        Top Route Patterns
      </h3>
      
      <div className="space-y-4">
        {data.slice(0, 8).map((route, index) => (
          <div key={index} className="flex items-center justify-between p-4 bg-secondary-50 rounded-lg">
            <div className="flex-1">
              <div className="font-medium text-secondary-900">
                {route.route}
              </div>
              <div className="text-sm text-secondary-500">
                {route.workflowType} • {route.tripCount} trips
              </div>
            </div>
            
            <div className="flex items-center space-x-4 text-sm">
              <div className="text-center">
                <div className="font-medium text-secondary-900">
                  {route.avgDuration}m
                </div>
                <div className="text-xs text-secondary-500">Avg Time</div>
              </div>
              
              <div className="text-center">
                <div className="font-medium text-secondary-900">
                  {route.avgTravelTime}m
                </div>
                <div className="text-xs text-secondary-500">Travel</div>
              </div>
              
              <div className="text-center">
                <div className={`font-medium ${
                  parseFloat(route.efficiency) >= 90 ? 'text-green-600' :
                  parseFloat(route.efficiency) >= 75 ? 'text-yellow-600' : 'text-red-600'
                }`}>
                  {route.efficiency}%
                </div>
                <div className="text-xs text-secondary-500">Efficiency</div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

const LocationPerformance = ({ data, routeData, loading }) => {
  const [activeTab, setActiveTab] = useState('loading');

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse bg-secondary-200 h-8 rounded-lg w-48"></div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {[...Array(2)].map((_, i) => (
            <div key={i} className="animate-pulse bg-secondary-200 h-64 rounded-lg"></div>
          ))}
        </div>
        <div className="animate-pulse bg-secondary-200 h-96 rounded-lg"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Tab Navigation */}
      <div className="flex space-x-1 bg-secondary-100 p-1 rounded-lg w-fit">
        <button
          onClick={() => setActiveTab('loading')}
          className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
            activeTab === 'loading'
              ? 'bg-white text-primary-600 shadow-sm'
              : 'text-secondary-600 hover:text-secondary-900'
          }`}
        >
          ⬆️ Loading Locations
        </button>
        <button
          onClick={() => setActiveTab('unloading')}
          className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
            activeTab === 'unloading'
              ? 'bg-white text-primary-600 shadow-sm'
              : 'text-secondary-600 hover:text-secondary-900'
          }`}
        >
          ⬇️ Unloading Locations
        </button>
      </div>

      {/* Location Performance Tables */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <LocationPerformanceTable data={data} type="loading" />
        <LocationPerformanceTable data={data} type="unloading" />
      </div>

      {/* Route Pattern Analysis */}
      <RoutePatternAnalysis data={routeData} />
    </div>
  );
};

export default LocationPerformance;
