import React from 'react';

const DriverFilter = ({
  drivers,
  selectedDriverName,
  onDriverChange,
  loading = false
}) => {
  return (
    <div>
      <label htmlFor="driver_filter" className="block text-sm font-medium text-secondary-700 mb-2">
        Driver
      </label>
      <select
        id="driver_filter"
        value={selectedDriverName}
        onChange={(e) => onDriverChange(e.target.value)}
        className="input"
        disabled={loading}
      >
        <option value="">All Drivers</option>
        {drivers.map((driver) => (
          <option key={driver.value} value={driver.value}>
            {driver.label}
          </option>
        ))}
      </select>
      {loading && (
        <div className="mt-1 text-xs text-secondary-500">
          Loading drivers...
        </div>
      )}
    </div>
  );
};

export default DriverFilter;
