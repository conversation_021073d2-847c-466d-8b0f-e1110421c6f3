/**
 * Duration Calculator Utility
 * Provides comprehensive trip duration calculations with precise time logic
 */

const calculateDurations = (trip) => {
  // Helper function to calculate time difference in minutes
  const getMinutesDifference = (startTime, endTime) => {
    if (!startTime || !endTime) return null;
    const start = new Date(startTime);
    const end = new Date(endTime);
    const diffMs = end - start;
    return Math.max(0, Math.round(diffMs / (1000 * 60))); // Convert to minutes
  };

  // Initialize duration object
  const durations = {
    total_duration_minutes: trip.total_duration_minutes || null,
    loading_duration_minutes: trip.loading_duration_minutes || null,
    travel_duration_minutes: trip.travel_duration_minutes || null,
    unloading_duration_minutes: trip.unloading_duration_minutes || null,
    loading_to_unloading_travel_minutes: null,
    unloading_to_loading_travel_minutes: null,
    calculated_at: new Date().toISOString()
  };

  // 1. Calculate Loading Duration
  if (!durations.loading_duration_minutes && trip.loading_start_time && trip.loading_end_time) {
    durations.loading_duration_minutes = getMinutesDifference(trip.loading_start_time, trip.loading_end_time);
  }

  // 2. Calculate Unloading Duration
  if (!durations.unloading_duration_minutes && trip.unloading_start_time && trip.unloading_end_time) {
    durations.unloading_duration_minutes = getMinutesDifference(trip.unloading_start_time, trip.unloading_end_time);
  }

  // 3. Calculate Travel Time from Loading to Unloading Location
  if (trip.loading_end_time && trip.unloading_start_time) {
    durations.loading_to_unloading_travel_minutes = getMinutesDifference(trip.loading_end_time, trip.unloading_start_time);
    durations.travel_1_minutes = durations.loading_to_unloading_travel_minutes; // Add explicit Travel_1 field
    
    // Update general travel_duration_minutes if not set
    if (!durations.travel_duration_minutes) {
      durations.travel_duration_minutes = durations.loading_to_unloading_travel_minutes;
    }
  }

  // 4. Calculate Total Trip Duration
  if (!durations.total_duration_minutes) {
    // Method 1: From loading start to trip completion
    if (trip.loading_start_time && trip.trip_completed_time) {
      durations.total_duration_minutes = getMinutesDifference(trip.loading_start_time, trip.trip_completed_time);
    }
    // Method 2: From loading start to unloading end
    else if (trip.loading_start_time && trip.unloading_end_time) {
      durations.total_duration_minutes = getMinutesDifference(trip.loading_start_time, trip.unloading_end_time);
    }
    // Method 3: Sum of individual components
    else if (durations.loading_duration_minutes || durations.travel_duration_minutes || durations.unloading_duration_minutes) {
      const loading = durations.loading_duration_minutes || 0;
      const travel = durations.travel_duration_minutes || 0;
      const unloading = durations.unloading_duration_minutes || 0;
      if (loading + travel + unloading > 0) {
        durations.total_duration_minutes = loading + travel + unloading;
      }
    }
  }

  // 5. Calculate Return Travel Time (Unloading to Loading)
  // This is complex as it requires next trip data or route estimation
  // For now, we'll estimate it as similar to forward travel if available
  if (durations.loading_to_unloading_travel_minutes && !durations.unloading_to_loading_travel_minutes) {
    // Use forward travel time as estimate for return (common approximation)
    durations.unloading_to_loading_travel_minutes = durations.loading_to_unloading_travel_minutes;
  }

  // Clean up null values - only return fields that have actual calculated values
  const result = {};
  Object.keys(durations).forEach(key => {
    if (durations[key] !== null && durations[key] !== undefined) {
      result[key] = durations[key];
    }
  });

  return result;
};

/**
 * Calculate durations for multiple trips with cross-trip analysis
 * This allows for better return travel time calculations
 */
const calculateTripsDurations = (trips) => {
  if (!trips || trips.length === 0) return trips;

  // Sort trips by truck and time for better analysis
  const sortedTrips = [...trips].sort((a, b) => {
    const truckCompare = (a.truck_number || '').localeCompare(b.truck_number || '');
    if (truckCompare !== 0) return truckCompare;
    return new Date(a.created_at || 0) - new Date(b.created_at || 0);
  });

  return sortedTrips.map((trip, index) => {
    const baseDurations = calculateDurations(trip);

    // Try to calculate actual return travel time using next trip
    if (trip.unloading_end_time) {
      const nextTrip = sortedTrips.find((t, i) => 
        i > index &&
        t.truck_number === trip.truck_number &&
        t.loading_start_time &&
        new Date(t.loading_start_time) > new Date(trip.unloading_end_time)
      );

      if (nextTrip) {
        const actualReturnTime = Math.round(
          (new Date(nextTrip.loading_start_time) - new Date(trip.unloading_end_time)) / (1000 * 60)
        );
        
        if (actualReturnTime > 0 && actualReturnTime < (24 * 60)) { // Reasonable return time (less than 24 hours)
          baseDurations.unloading_to_loading_travel_minutes = actualReturnTime;
          baseDurations.travel_2_minutes = actualReturnTime; // Add explicit Travel_2 field
        }
      }
    }

    return {
      ...trip,
      ...baseDurations
    };
  });
};

/**
 * Calculate comprehensive duration metrics for a set of trips
 */
const calculateDurationMetrics = (trips) => {
  if (!trips || trips.length === 0) {
    return {
      totalTripDuration: 0,
      totalLoadingDuration: 0,
      totalUnloadingDuration: 0,
      totalLoadingToUnloadingTravel: 0,
      totalUnloadingToLoadingTravel: 0,
      averages: {
        avgTripDuration: 0,
        avgLoadingDuration: 0,
        avgUnloadingDuration: 0,
        avgLoadingToUnloadingTravel: 0,
        avgUnloadingToLoadingTravel: 0
      },
      validCounts: {
        validTripDurationCount: 0,
        validLoadingDurationCount: 0,
        validUnloadingDurationCount: 0,
        validLoadingToUnloadingTravelCount: 0,
        validUnloadingToLoadingTravelCount: 0
      }
    };
  }

  const tripsWithDurations = calculateTripsDurations(trips);

  let totalTripDuration = 0;
  let totalLoadingDuration = 0;
  let totalUnloadingDuration = 0;
  let totalLoadingToUnloadingTravel = 0;
  let totalUnloadingToLoadingTravel = 0;

  let validTripDurationCount = 0;
  let validLoadingDurationCount = 0;
  let validUnloadingDurationCount = 0;
  let validLoadingToUnloadingTravelCount = 0;
  let validUnloadingToLoadingTravelCount = 0;

  tripsWithDurations.forEach(trip => {
    if (trip.total_duration_minutes > 0) {
      totalTripDuration += trip.total_duration_minutes;
      validTripDurationCount++;
    }

    if (trip.loading_duration_minutes > 0) {
      totalLoadingDuration += trip.loading_duration_minutes;
      validLoadingDurationCount++;
    }

    if (trip.unloading_duration_minutes > 0) {
      totalUnloadingDuration += trip.unloading_duration_minutes;
      validUnloadingDurationCount++;
    }

    if (trip.loading_to_unloading_travel_minutes > 0) {
      totalLoadingToUnloadingTravel += trip.loading_to_unloading_travel_minutes;
      validLoadingToUnloadingTravelCount++;
    }

    if (trip.unloading_to_loading_travel_minutes > 0) {
      totalUnloadingToLoadingTravel += trip.unloading_to_loading_travel_minutes;
      validUnloadingToLoadingTravelCount++;
    }
  });

  return {
    totalTripDuration,
    totalLoadingDuration,
    totalUnloadingDuration,
    totalLoadingToUnloadingTravel,
    totalUnloadingToLoadingTravel,
    averages: {
      avgTripDuration: validTripDurationCount > 0 ? Math.round(totalTripDuration / validTripDurationCount) : 0,
      avgLoadingDuration: validLoadingDurationCount > 0 ? Math.round(totalLoadingDuration / validLoadingDurationCount) : 0,
      avgUnloadingDuration: validUnloadingDurationCount > 0 ? Math.round(totalUnloadingDuration / validUnloadingDurationCount) : 0,
      avgLoadingToUnloadingTravel: validLoadingToUnloadingTravelCount > 0 ? Math.round(totalLoadingToUnloadingTravel / validLoadingToUnloadingTravelCount) : 0,
      avgUnloadingToLoadingTravel: validUnloadingToLoadingTravelCount > 0 ? Math.round(totalUnloadingToLoadingTravel / validUnloadingToLoadingTravelCount) : 0
    },
    validCounts: {
      validTripDurationCount,
      validLoadingDurationCount,
      validUnloadingDurationCount,
      validLoadingToUnloadingTravelCount,
      validUnloadingToLoadingTravelCount
    },
    tripsWithCalculatedDurations: tripsWithDurations
  };
};

module.exports = {
  calculateDurations,
  calculateTripsDurations,
  calculateDurationMetrics
};