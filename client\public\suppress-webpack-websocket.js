/**
 * Suppress Webpack WebSocket Errors in Dev Tunnels
 * This script intercepts and suppresses the webpack WebSocket connection errors
 * that appear in dev tunnel environments while preserving functionality.
 */

(function() {
  'use strict';
  
  // Check if we're in a dev tunnel environment
  const isDevTunnel = window.location.hostname.includes('devtunnels.ms') ||
                     window.location.hostname.includes('ngrok.io') ||
                     window.location.hostname.includes('localtunnel.me') ||
                     window.location.hostname.includes('github.dev') ||
                     window.location.hostname.includes('gitpod.io');
  
  if (!isDevTunnel) {
    return; // Only run in dev tunnel environments
  }
  
  console.log('🔇 Suppressing webpack WebSocket errors in dev tunnel environment');
  
  // Override console.error to filter out webpack WebSocket errors
  const originalConsoleError = console.error;
  console.error = function(...args) {
    const message = args.join(' ');
    
    // Filter out webpack WebSocket connection errors
    if (message.includes('WebSocket connection') && 
        message.includes('failed') && 
        message.includes(':3000/ws')) {
      // Silently ignore these errors
      return;
    }
    
    // Filter out webpack-dev-server related errors
    if (message.includes('WebSocketClient') || 
        message.includes('sockjs-node') ||
        message.includes('webpack-dev-server')) {
      return;
    }
    
    // Allow all other errors through
    originalConsoleError.apply(console, args);
  };
  
  // Also suppress WebSocket constructor errors for webpack
  const OriginalWebSocket = window.WebSocket;
  window.WebSocket = function(url, protocols) {
    // If this is a webpack WebSocket with port in dev tunnel, create a dummy
    if (isDevTunnel && url.includes(':3000/ws') && url.includes('devtunnels.ms')) {
      console.log('🚫 Blocked webpack WebSocket connection to:', url);
      
      // Create a dummy WebSocket that immediately closes
      const dummyWs = {
        readyState: 3, // CLOSED
        close: function() {},
        addEventListener: function() {},
        removeEventListener: function() {},
        send: function() {}
      };
      
      // Simulate immediate close event
      setTimeout(() => {
        if (dummyWs.onclose) {
          dummyWs.onclose({ code: 1000, reason: 'Blocked in dev tunnel' });
        }
      }, 100);
      
      return dummyWs;
    }
    
    // For all other WebSockets (including our application WebSocket), use normal constructor
    return new OriginalWebSocket(url, protocols);
  };
  
  // Copy static properties
  Object.setPrototypeOf(window.WebSocket, OriginalWebSocket);
  Object.defineProperty(window.WebSocket, 'prototype', {
    value: OriginalWebSocket.prototype,
    writable: false
  });
  
  console.log('✅ Webpack WebSocket error suppression active');
})();
