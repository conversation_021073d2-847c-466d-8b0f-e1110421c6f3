import { useState, useCallback } from 'react';
import { getWeekStart, getWeekEnd, getMonthStart, getMonthEnd, getToday } from '../utils/dateHelpers';

/**
 * Custom hook for managing shift filters
 * Extracted from SimplifiedShiftManagement to improve reusability
 */
const useShiftFilters = () => {
  const [filters, setFilters] = useState({
    truck_id: '',
    driver_id: '',
    status: '',
    shift_type: '',
    date_from: getWeekStart(),
    date_to: getWeekEnd(),
    sort_by: 'start_date',
    sort_order: 'DESC'
  });

  const updateFilter = useCallback((key, value) => {
    console.log('🔧 Filter updated:', { key, value });
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  }, []);

  const clearAllFilters = useCallback(() => {
    console.log('🧹 Clearing all filters');
    setFilters({
      truck_id: '',
      driver_id: '',
      status: '',
      shift_type: '',
      date_from: getWeekStart(),
      date_to: getWeekEnd(),
      sort_by: 'start_date',
      sort_order: 'DESC'
    });
  }, []);

  const setDatePreset = useCallback((preset) => {
    const today = getToday();
    switch (preset) {
      case 'today':
        setFilters(prev => ({
          ...prev,
          date_from: today,
          date_to: today
        }));
        break;
      case 'week':
        setFilters(prev => ({
          ...prev,
          date_from: getWeekStart(),
          date_to: getWeekEnd()
        }));
        break;
      case 'month':
        setFilters(prev => ({
          ...prev,
          date_from: getMonthStart(),
          date_to: getMonthEnd()
        }));
        break;
      default:
        console.warn('Unknown date preset:', preset);
        break;
    }
  }, []);

  const getActiveFilterCount = useCallback(() => {
    let count = 0;
    if (filters.truck_id) count++;
    if (filters.driver_id) count++;
    if (filters.status) count++;
    if (filters.shift_type) count++;
    return count;
  }, [filters]);

  return {
    filters,
    updateFilter,
    clearAllFilters,
    setDatePreset,
    getActiveFilterCount
  };
};

export default useShiftFilters;