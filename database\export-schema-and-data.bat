@echo off
REM PostgreSQL Export - Schema + Data for Windows
REM Uses your provided password: PostgreSQLPassword

setlocal enabledelayedexpansion

echo ================================================
echo PostgreSQL Export - Schema + Data
echo ================================================

REM Configuration
set DB_NAME=hauling_qr_system
set DB_USER=postgres
set DB_PASSWORD=PostgreSQLPassword

REM Get current directory
set "CURRENT_DIR=%~dp0"
set "OUTPUT_FILE=%CURRENT_DIR%init-exported.sql"

REM Ensure database directory exists
if not exist "%CURRENT_DIR%" mkdir "%CURRENT_DIR%" 2>nul

REM Check PostgreSQL
where pg_dump >nul 2>nul
if %errorlevel% neq 0 (
    if exist "C:\PostgreSQL\bin\pg_dump.exe" (
        set PG_DUMP="C:\PostgreSQL\bin\pg_dump.exe"
    ) else if exist "C:\Program Files\PostgreSQL\15\bin\pg_dump.exe" (
        set PG_DUMP="C:\Program Files\PostgreSQL\15\bin\pg_dump.exe"
    ) else if exist "C:\Program Files\PostgreSQL\14\bin\pg_dump.exe" (
        set PG_DUMP="C:\Program Files\PostgreSQL\14\bin\pg_dump.exe"
    ) else if exist "C:\Program Files\PostgreSQL\13\bin\pg_dump.exe" (
        set PG_DUMP="C:\Program Files\PostgreSQL\13\bin\pg_dump.exe"
    ) else (
        echo ERROR: PostgreSQL not found
        echo Checked locations:
        echo   C:\PostgreSQL\bin\pg_dump.exe
        echo   C:\Program Files\PostgreSQL\15\bin\pg_dump.exe
        echo   C:\Program Files\PostgreSQL\14\bin\pg_dump.exe
        echo   C:\Program Files\PostgreSQL\13\bin\pg_dump.exe
        pause
        exit /b 1
    )
) else (
    set PG_DUMP=pg_dump
)

echo Using: %PG_DUMP%
echo Output file: %OUTPUT_FILE%

REM Set environment variable for password
set PGPASSWORD=%DB_PASSWORD%

REM Create absolute path for output
echo Creating output directory: %CURRENT_DIR%
if not exist "%CURRENT_DIR%" mkdir "%CURRENT_DIR%"

REM Export complete database (schema + data)
echo Exporting complete database (schema + data): %DB_NAME%
echo This may take a moment...

%PG_DUMP% -h localhost -p 5432 -U %DB_USER% -d %DB_NAME% ^
    --clean ^
    --if-exists ^
    --no-owner ^
    --no-privileges ^
    --inserts ^
    > "%OUTPUT_FILE%" 2>&1

if %errorlevel% neq 0 (
    echo ERROR: Export failed
    echo Check if database '%DB_NAME%' exists
    echo To see available databases, run:
    echo   psql -U %DB_USER% -c "\l"
    pause
    exit /b 1
)

REM Verify export with absolute path
if exist "%OUTPUT_FILE%" (
    echo ================================================
    echo SUCCESS! Complete database exported to:
    echo %OUTPUT_FILE%
    echo ================================================
    for %%A in ("%OUTPUT_FILE%") do echo File size: %%~zA bytes
    echo.
    echo This file contains:
    echo - Complete database schema
    echo - All table data
    echo - Functions and procedures
    echo - Indexes and constraints
    echo.
    echo To use for fresh deployment:
    echo   psql -U %DB_USER% -d new_database_name -f "%OUTPUT_FILE%"
    echo.
    echo To check file contents:
    echo   type "%OUTPUT_FILE%" | more
) else (
    echo ERROR: Export file was not created at: %OUTPUT_FILE%
    echo Current directory: %CD%
)

pause