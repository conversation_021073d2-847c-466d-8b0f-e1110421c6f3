const { query, getClient } = require('../config/database');
const { logger } = require('../utils/logger');

/**
 * Service for validating trip_logs field population completeness
 * Implements requirements 5.1, 5.2, 5.3, 5.4, 5.5
 */
class TripLogsValidationService {
  
  /**
   * Validate that all new trip_logs entries have complete driver information
   * Requirement 5.1: Validate driver information capture success
   */
  static async validateDriverInformationCompleteness(timeRangeHours = 24) {
    try {
      const validationQuery = `
        SELECT 
          id,
          assignment_id,
          trip_number,
          status,
          created_at,
          performed_by_driver_id,
          performed_by_driver_name,
          performed_by_employee_id,
          performed_by_shift_id,
          performed_by_shift_type,
          CASE 
            WHEN performed_by_driver_id IS NULL THEN 'missing_driver_id'
            WHEN performed_by_driver_name IS NULL THEN 'missing_driver_name'
            WHEN performed_by_employee_id IS NULL THEN 'missing_employee_id'
            WH<PERSON> performed_by_shift_id IS NULL THEN 'missing_shift_id'
            WH<PERSON> performed_by_shift_type IS NULL THEN 'missing_shift_type'
            ELSE 'complete'
          END as driver_info_status
        FROM trip_logs 
        WHERE created_at >= NOW() - INTERVAL '${timeRangeHours} hours'
        ORDER BY created_at DESC
      `;

      const result = await query(validationQuery);
      
      const summary = {
        total_trips: result.rows.length,
        complete_driver_info: result.rows.filter(row => row.driver_info_status === 'complete').length,
        missing_driver_info: result.rows.filter(row => row.driver_info_status !== 'complete').length,
        missing_breakdown: {},
        incomplete_trips: result.rows.filter(row => row.driver_info_status !== 'complete')
      };

      // Count specific missing field types
      result.rows.forEach(row => {
        if (row.driver_info_status !== 'complete') {
          summary.missing_breakdown[row.driver_info_status] = 
            (summary.missing_breakdown[row.driver_info_status] || 0) + 1;
        }
      });

      // Calculate success rate
      summary.success_rate = summary.total_trips > 0 
        ? (summary.complete_driver_info / summary.total_trips * 100).toFixed(2) + '%'
        : '0%';

      logger.info('Driver information validation completed', {
        context: 'TRIP_LOGS_VALIDATION.DRIVER_INFO',
        summary: {
          total_trips: summary.total_trips,
          complete_driver_info: summary.complete_driver_info,
          success_rate: summary.success_rate,
          missing_breakdown: summary.missing_breakdown
        }
      });

      return summary;
    } catch (error) {
      logger.error('Error validating driver information completeness', {
        context: 'TRIP_LOGS_VALIDATION.DRIVER_INFO_ERROR',
        error: error.message,
        stack: error.stack
      });
      throw error;
    }
  }

  /**
   * Verify that notes field contains meaningful contextual information
   * Requirement 5.2: Validate notes field quality
   */
  static async validateNotesFieldQuality(timeRangeHours = 24) {
    try {
      const notesValidationQuery = `
        SELECT 
          id,
          assignment_id,
          trip_number,
          status,
          created_at,
          notes,
          CASE 
            WHEN notes IS NULL THEN 'missing_notes'
            WHEN jsonb_typeof(notes) != 'string' AND jsonb_typeof(notes) != 'object' THEN 'invalid_notes_format'
            WHEN (jsonb_typeof(notes) = 'string' AND LENGTH(notes::text) < 10) THEN 'notes_too_short'
            WHEN (jsonb_typeof(notes) = 'object' AND notes::text = '{}') THEN 'empty_notes_object'
            ELSE 'valid_notes'
          END as notes_status,
          CASE 
            WHEN notes IS NOT NULL THEN LENGTH(notes::text)
            ELSE 0
          END as notes_length
        FROM trip_logs 
        WHERE created_at >= NOW() - INTERVAL '${timeRangeHours} hours'
        ORDER BY created_at DESC
      `;

      const result = await query(notesValidationQuery);
      
      const summary = {
        total_trips: result.rows.length,
        valid_notes: result.rows.filter(row => row.notes_status === 'valid_notes').length,
        invalid_notes: result.rows.filter(row => row.notes_status !== 'valid_notes').length,
        notes_breakdown: {},
        invalid_notes_trips: result.rows.filter(row => row.notes_status !== 'valid_notes'),
        average_notes_length: 0
      };

      // Count specific notes issues
      result.rows.forEach(row => {
        if (row.notes_status !== 'valid_notes') {
          summary.notes_breakdown[row.notes_status] = 
            (summary.notes_breakdown[row.notes_status] || 0) + 1;
        }
      });

      // Calculate average notes length for valid notes
      const validNotesLengths = result.rows
        .filter(row => row.notes_status === 'valid_notes')
        .map(row => row.notes_length);
      
      if (validNotesLengths.length > 0) {
        summary.average_notes_length = Math.round(
          validNotesLengths.reduce((sum, length) => sum + length, 0) / validNotesLengths.length
        );
      }

      // Calculate success rate
      summary.success_rate = summary.total_trips > 0 
        ? (summary.valid_notes / summary.total_trips * 100).toFixed(2) + '%'
        : '0%';

      logger.info('Notes field validation completed', {
        context: 'TRIP_LOGS_VALIDATION.NOTES_QUALITY',
        summary: {
          total_trips: summary.total_trips,
          valid_notes: summary.valid_notes,
          success_rate: summary.success_rate,
          average_notes_length: summary.average_notes_length,
          notes_breakdown: summary.notes_breakdown
        }
      });

      return summary;
    } catch (error) {
      logger.error('Error validating notes field quality', {
        context: 'TRIP_LOGS_VALIDATION.NOTES_ERROR',
        error: error.message,
        stack: error.stack
      });
      throw error;
    }
  }

  /**
   * Verify that location_sequence field is correctly calculated for different workflow types
   * Requirement 5.3: Validate location sequence accuracy
   */
  static async validateLocationSequenceAccuracy(timeRangeHours = 24) {
    try {
      const sequenceValidationQuery = `
        SELECT 
          tl.id,
          tl.assignment_id,
          tl.trip_number,
          tl.status,
          tl.created_at,
          tl.location_sequence,
          tl.workflow_type,
          tl.is_extended_trip,
          tl.cycle_number,
          a.assignment_code,
          CASE 
            WHEN tl.location_sequence IS NULL THEN 'missing_sequence'
            WHEN jsonb_typeof(tl.location_sequence) != 'array' AND jsonb_typeof(tl.location_sequence) != 'number' THEN 'invalid_sequence_format'
            WHEN tl.workflow_type = 'standard' AND jsonb_typeof(tl.location_sequence) = 'number' AND (tl.location_sequence::text)::int BETWEEN 1 AND 2 THEN 'valid_standard'
            WHEN tl.workflow_type = 'extended' AND jsonb_typeof(tl.location_sequence) = 'number' AND (tl.location_sequence::text)::int BETWEEN 1 AND 3 THEN 'valid_extended'
            WHEN tl.workflow_type = 'cycle' AND jsonb_typeof(tl.location_sequence) = 'number' AND (tl.location_sequence::text)::int >= 1 THEN 'valid_cycle'
            WHEN tl.workflow_type = 'dynamic' AND tl.location_sequence IS NOT NULL THEN 'valid_dynamic'
            ELSE 'invalid_sequence_value'
          END as sequence_status
        FROM trip_logs tl
        LEFT JOIN assignments a ON tl.assignment_id = a.id
        WHERE tl.created_at >= NOW() - INTERVAL '${timeRangeHours} hours'
        ORDER BY tl.created_at DESC
      `;

      const result = await query(sequenceValidationQuery);
      
      const summary = {
        total_trips: result.rows.length,
        valid_sequences: result.rows.filter(row => row.sequence_status.startsWith('valid_')).length,
        invalid_sequences: result.rows.filter(row => !row.sequence_status.startsWith('valid_')).length,
        sequence_breakdown: {},
        workflow_breakdown: {},
        invalid_sequence_trips: result.rows.filter(row => !row.sequence_status.startsWith('valid_'))
      };

      // Count specific sequence issues and workflow types
      result.rows.forEach(row => {
        // Sequence status breakdown
        summary.sequence_breakdown[row.sequence_status] = 
          (summary.sequence_breakdown[row.sequence_status] || 0) + 1;
        
        // Workflow type breakdown
        summary.workflow_breakdown[row.workflow_type] = 
          (summary.workflow_breakdown[row.workflow_type] || 0) + 1;
      });

      // Calculate success rate
      summary.success_rate = summary.total_trips > 0 
        ? (summary.valid_sequences / summary.total_trips * 100).toFixed(2) + '%'
        : '0%';

      logger.info('Location sequence validation completed', {
        context: 'TRIP_LOGS_VALIDATION.LOCATION_SEQUENCE',
        summary: {
          total_trips: summary.total_trips,
          valid_sequences: summary.valid_sequences,
          success_rate: summary.success_rate,
          sequence_breakdown: summary.sequence_breakdown,
          workflow_breakdown: summary.workflow_breakdown
        }
      });

      return summary;
    } catch (error) {
      logger.error('Error validating location sequence accuracy', {
        context: 'TRIP_LOGS_VALIDATION.SEQUENCE_ERROR',
        error: error.message,
        stack: error.stack
      });
      throw error;
    }
  }

  /**
   * Add automated checks to detect trip_logs entries with missing required fields
   * Requirement 5.4: Automated missing field detection
   */
  static async detectMissingRequiredFields(timeRangeHours = 24) {
    try {
      const missingFieldsQuery = `
        SELECT 
          id,
          assignment_id,
          trip_number,
          status,
          created_at,
          -- Check all critical fields
          CASE WHEN assignment_id IS NULL THEN 'assignment_id' END as missing_assignment_id,
          CASE WHEN trip_number IS NULL THEN 'trip_number' END as missing_trip_number,
          CASE WHEN status IS NULL THEN 'status' END as missing_status,
          CASE WHEN performed_by_driver_id IS NULL THEN 'performed_by_driver_id' END as missing_driver_id,
          CASE WHEN performed_by_driver_name IS NULL THEN 'performed_by_driver_name' END as missing_driver_name,
          CASE WHEN performed_by_employee_id IS NULL THEN 'performed_by_employee_id' END as missing_employee_id,
          CASE WHEN performed_by_shift_id IS NULL THEN 'performed_by_shift_id' END as missing_shift_id,
          CASE WHEN performed_by_shift_type IS NULL THEN 'performed_by_shift_type' END as missing_shift_type,
          CASE WHEN notes IS NULL THEN 'notes' END as missing_notes,
          CASE WHEN location_sequence IS NULL THEN 'location_sequence' END as missing_location_sequence,
          -- Count total missing fields
          (
            CASE WHEN assignment_id IS NULL THEN 1 ELSE 0 END +
            CASE WHEN trip_number IS NULL THEN 1 ELSE 0 END +
            CASE WHEN status IS NULL THEN 1 ELSE 0 END +
            CASE WHEN performed_by_driver_id IS NULL THEN 1 ELSE 0 END +
            CASE WHEN performed_by_driver_name IS NULL THEN 1 ELSE 0 END +
            CASE WHEN performed_by_employee_id IS NULL THEN 1 ELSE 0 END +
            CASE WHEN performed_by_shift_id IS NULL THEN 1 ELSE 0 END +
            CASE WHEN performed_by_shift_type IS NULL THEN 1 ELSE 0 END +
            CASE WHEN notes IS NULL THEN 1 ELSE 0 END +
            CASE WHEN location_sequence IS NULL THEN 1 ELSE 0 END
          ) as total_missing_fields
        FROM trip_logs 
        WHERE created_at >= NOW() - INTERVAL '${timeRangeHours} hours'
        ORDER BY total_missing_fields DESC, created_at DESC
      `;

      const result = await query(missingFieldsQuery);
      
      const summary = {
        total_trips: result.rows.length,
        complete_trips: result.rows.filter(row => row.total_missing_fields === 0).length,
        incomplete_trips: result.rows.filter(row => row.total_missing_fields > 0).length,
        field_missing_counts: {},
        most_problematic_trips: result.rows.filter(row => row.total_missing_fields > 0).slice(0, 10)
      };

      // Count how often each field is missing
      const fieldNames = [
        'missing_assignment_id', 'missing_trip_number', 'missing_status',
        'missing_driver_id', 'missing_driver_name', 'missing_employee_id',
        'missing_shift_id', 'missing_shift_type', 'missing_notes', 'missing_location_sequence'
      ];

      fieldNames.forEach(fieldName => {
        summary.field_missing_counts[fieldName.replace('missing_', '')] = 
          result.rows.filter(row => row[fieldName] !== null).length;
      });

      // Calculate completeness rate
      summary.completeness_rate = summary.total_trips > 0 
        ? (summary.complete_trips / summary.total_trips * 100).toFixed(2) + '%'
        : '0%';

      logger.info('Missing required fields detection completed', {
        context: 'TRIP_LOGS_VALIDATION.MISSING_FIELDS',
        summary: {
          total_trips: summary.total_trips,
          complete_trips: summary.complete_trips,
          completeness_rate: summary.completeness_rate,
          field_missing_counts: summary.field_missing_counts
        }
      });

      return summary;
    } catch (error) {
      logger.error('Error detecting missing required fields', {
        context: 'TRIP_LOGS_VALIDATION.MISSING_FIELDS_ERROR',
        error: error.message,
        stack: error.stack
      });
      throw error;
    }
  }

  /**
   * Create monitoring alerts for data quality issues
   * Requirement 5.5: Data quality monitoring and alerting
   */
  static async createDataQualityAlerts(thresholds = {}) {
    try {
      const defaultThresholds = {
        driver_info_success_rate: 95, // Minimum 95% success rate for driver info
        notes_success_rate: 90,       // Minimum 90% success rate for notes
        sequence_success_rate: 95,    // Minimum 95% success rate for location sequence
        completeness_rate: 98,        // Minimum 98% completeness rate
        time_range_hours: 1           // Check last 1 hour for alerts
      };

      const alertThresholds = { ...defaultThresholds, ...thresholds };
      
      // Run all validation checks
      const [driverValidation, notesValidation, sequenceValidation, missingFieldsValidation] = 
        await Promise.all([
          this.validateDriverInformationCompleteness(alertThresholds.time_range_hours),
          this.validateNotesFieldQuality(alertThresholds.time_range_hours),
          this.validateLocationSequenceAccuracy(alertThresholds.time_range_hours),
          this.detectMissingRequiredFields(alertThresholds.time_range_hours)
        ]);

      const alerts = [];

      // Check driver information success rate
      const driverSuccessRate = parseFloat(driverValidation.success_rate.replace('%', ''));
      if (driverSuccessRate < alertThresholds.driver_info_success_rate) {
        alerts.push({
          type: 'DRIVER_INFO_LOW_SUCCESS_RATE',
          severity: 'HIGH',
          message: `Driver information capture success rate (${driverValidation.success_rate}) is below threshold (${alertThresholds.driver_info_success_rate}%)`,
          details: {
            current_rate: driverValidation.success_rate,
            threshold: alertThresholds.driver_info_success_rate + '%',
            total_trips: driverValidation.total_trips,
            missing_breakdown: driverValidation.missing_breakdown
          }
        });
      }

      // Check notes success rate
      const notesSuccessRate = parseFloat(notesValidation.success_rate.replace('%', ''));
      if (notesSuccessRate < alertThresholds.notes_success_rate) {
        alerts.push({
          type: 'NOTES_LOW_SUCCESS_RATE',
          severity: 'MEDIUM',
          message: `Notes field quality success rate (${notesValidation.success_rate}) is below threshold (${alertThresholds.notes_success_rate}%)`,
          details: {
            current_rate: notesValidation.success_rate,
            threshold: alertThresholds.notes_success_rate + '%',
            total_trips: notesValidation.total_trips,
            notes_breakdown: notesValidation.notes_breakdown
          }
        });
      }

      // Check location sequence success rate
      const sequenceSuccessRate = parseFloat(sequenceValidation.success_rate.replace('%', ''));
      if (sequenceSuccessRate < alertThresholds.sequence_success_rate) {
        alerts.push({
          type: 'SEQUENCE_LOW_SUCCESS_RATE',
          severity: 'HIGH',
          message: `Location sequence accuracy (${sequenceValidation.success_rate}) is below threshold (${alertThresholds.sequence_success_rate}%)`,
          details: {
            current_rate: sequenceValidation.success_rate,
            threshold: alertThresholds.sequence_success_rate + '%',
            total_trips: sequenceValidation.total_trips,
            sequence_breakdown: sequenceValidation.sequence_breakdown
          }
        });
      }

      // Check overall completeness rate
      const completenessRate = parseFloat(missingFieldsValidation.completeness_rate.replace('%', ''));
      if (completenessRate < alertThresholds.completeness_rate) {
        alerts.push({
          type: 'OVERALL_LOW_COMPLETENESS_RATE',
          severity: 'CRITICAL',
          message: `Overall field completeness rate (${missingFieldsValidation.completeness_rate}) is below threshold (${alertThresholds.completeness_rate}%)`,
          details: {
            current_rate: missingFieldsValidation.completeness_rate,
            threshold: alertThresholds.completeness_rate + '%',
            total_trips: missingFieldsValidation.total_trips,
            field_missing_counts: missingFieldsValidation.field_missing_counts
          }
        });
      }

      // Log alerts
      if (alerts.length > 0) {
        logger.warn('Data quality alerts generated', {
          context: 'TRIP_LOGS_VALIDATION.ALERTS',
          alert_count: alerts.length,
          alerts: alerts
        });

        // Store alerts in database for tracking
        await this.storeDataQualityAlerts(alerts);
      } else {
        logger.info('No data quality alerts - all metrics within thresholds', {
          context: 'TRIP_LOGS_VALIDATION.ALERTS',
          thresholds: alertThresholds,
          validation_summary: {
            driver_success_rate: driverValidation.success_rate,
            notes_success_rate: notesValidation.success_rate,
            sequence_success_rate: sequenceValidation.success_rate,
            completeness_rate: missingFieldsValidation.completeness_rate
          }
        });
      }

      return {
        alerts_generated: alerts.length,
        alerts: alerts,
        validation_summary: {
          driver_validation: driverValidation,
          notes_validation: notesValidation,
          sequence_validation: sequenceValidation,
          missing_fields_validation: missingFieldsValidation
        },
        thresholds_used: alertThresholds
      };

    } catch (error) {
      logger.error('Error creating data quality alerts', {
        context: 'TRIP_LOGS_VALIDATION.ALERTS_ERROR',
        error: error.message,
        stack: error.stack
      });
      throw error;
    }
  }

  /**
   * Store data quality alerts in the database for tracking and historical analysis
   */
  static async storeDataQualityAlerts(alerts) {
    try {
      const client = await getClient();
      
      for (const alert of alerts) {
        await client.query(`
          INSERT INTO system_logs (
            log_type, 
            message, 
            details, 
            created_at
          ) VALUES ($1, $2, $3, NOW())
        `, [
          'DATA_QUALITY_ALERT',
          alert.message,
          JSON.stringify({
            alert_type: alert.type,
            severity: alert.severity,
            details: alert.details
          })
        ]);
      }

      logger.info('Data quality alerts stored in database', {
        context: 'TRIP_LOGS_VALIDATION.ALERT_STORAGE',
        alerts_stored: alerts.length
      });

    } catch (error) {
      logger.error('Error storing data quality alerts', {
        context: 'TRIP_LOGS_VALIDATION.ALERT_STORAGE_ERROR',
        error: error.message,
        stack: error.stack
      });
      // Don't throw - alert storage failure shouldn't break the validation process
    }
  }

  /**
   * Run comprehensive validation report
   * Combines all validation checks into a single comprehensive report
   */
  static async runComprehensiveValidation(timeRangeHours = 24) {
    try {
      logger.info('Starting comprehensive trip_logs validation', {
        context: 'TRIP_LOGS_VALIDATION.COMPREHENSIVE',
        time_range_hours: timeRangeHours
      });

      const startTime = Date.now();

      const [driverValidation, notesValidation, sequenceValidation, missingFieldsValidation] = 
        await Promise.all([
          this.validateDriverInformationCompleteness(timeRangeHours),
          this.validateNotesFieldQuality(timeRangeHours),
          this.validateLocationSequenceAccuracy(timeRangeHours),
          this.detectMissingRequiredFields(timeRangeHours)
        ]);

      const executionTime = Date.now() - startTime;

      const comprehensiveReport = {
        validation_timestamp: new Date().toISOString(),
        time_range_hours: timeRangeHours,
        execution_time_ms: executionTime,
        overall_summary: {
          total_trips_analyzed: driverValidation.total_trips,
          driver_info_success_rate: driverValidation.success_rate,
          notes_quality_success_rate: notesValidation.success_rate,
          location_sequence_success_rate: sequenceValidation.success_rate,
          overall_completeness_rate: missingFieldsValidation.completeness_rate
        },
        detailed_results: {
          driver_validation: driverValidation,
          notes_validation: notesValidation,
          sequence_validation: sequenceValidation,
          missing_fields_validation: missingFieldsValidation
        }
      };

      logger.info('Comprehensive trip_logs validation completed', {
        context: 'TRIP_LOGS_VALIDATION.COMPREHENSIVE_COMPLETE',
        execution_time_ms: executionTime,
        overall_summary: comprehensiveReport.overall_summary
      });

      return comprehensiveReport;

    } catch (error) {
      logger.error('Error running comprehensive validation', {
        context: 'TRIP_LOGS_VALIDATION.COMPREHENSIVE_ERROR',
        error: error.message,
        stack: error.stack
      });
      throw error;
    }
  }
}

module.exports = TripLogsValidationService;