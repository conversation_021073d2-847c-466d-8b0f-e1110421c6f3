const express = require('express');
const cors = require('cors');
const rateLimit = require('express-rate-limit');
const https = require('https');
const http = require('http');
const path = require('path');
const fs = require('fs');
const { initializeWebSocket } = require('./websocket');
const { getServerConfig, getSSLOptions, displayServerConfig } = require('./config/unified-config');
const { initializeShiftTransitions, shutdownShiftTransitions, updateCaptureDriverFunction } = require('./utils/shift-transition-integration');
const shiftSyncMonitor = require('./utils/SimpleShiftSyncMonitor');
const statusSyncMonitor = require('./services/StatusSynchronizationMonitor');
const EnhancedShiftStatusService = require('./services/EnhancedShiftStatusService');
const { query } = require('./config/database');

// Load unified configuration
console.log('🔧 Loading unified configuration...');
const config = getServerConfig();
displayServerConfig(config);

console.log('🚀 Initializing Express application...');
const app = express();

// Cloudflare proxy detection middleware
app.use((req, res, next) => {
  // Detect if request is coming through Cloudflare
  const cfConnectingIP = req.headers['cf-connecting-ip'];
  const cfRay = req.headers['cf-ray'];
  const xForwardedProto = req.headers['x-forwarded-proto'];
  
  if (cfConnectingIP || cfRay) {
    // Request is coming through Cloudflare
    req.isCloudflare = true;
    req.realIP = cfConnectingIP || req.headers['x-forwarded-for'] || req.connection.remoteAddress;
    req.protocol = xForwardedProto || req.protocol;
    
    // Set secure flag for cookies when behind Cloudflare HTTPS
    if (xForwardedProto === 'https') {
      req.secure = true;
    }
  } else {
    req.isCloudflare = false;
    req.realIP = req.connection.remoteAddress;
  }
  
  next();
});

// Trust proxy when behind Cloudflare or other reverse proxies
if (config.IS_PRODUCTION) {
  app.set('trust proxy', true);
}

// Security middleware removed for development

// Helmet security middleware removed for development

// HTTPS redirect middleware removed for development

// CORS configuration - COMPLETELY DISABLED FOR DEVELOPMENT
console.log('🌐 CORS Configuration:');
console.log('   NODE_ENV:', config.NODE_ENV);
console.log('   IS_DEVELOPMENT:', config.IS_DEVELOPMENT);
console.log('   DEV_ENABLE_CORS_ALL:', config.DEV_ENABLE_CORS_ALL);
console.log('   Allowed origins:', config.CORS_ORIGINS);
console.log('   Process ENV NODE_ENV:', process.env.NODE_ENV);

// ENHANCED CORS CONFIGURATION WITH DEV TUNNEL SUPPORT
console.log('🌐 ENHANCED CORS: Supporting dev tunnels and local development');
app.use((req, res, next) => {
  // Get the origin from the request
  const origin = req.headers.origin;

  // Simplified allowed origins (localhost, 127.0.0.1, 0.0.0.0 with ports 3000 and 5000)
  const allowedOrigins = [
    'http://localhost:3000',
    'https://localhost:3000',
    'http://localhost:5000',
    'https://localhost:5000',
    'http://127.0.0.1:3000',
    'https://127.0.0.1:3000',
    'http://127.0.0.1:5000',
    'https://127.0.0.1:5000',
    'http://0.0.0.0:3000',
    'https://0.0.0.0:3000',
    'http://0.0.0.0:5000',
    'https://0.0.0.0:5000'
  ];

  // Check if origin is allowed
  if (origin && allowedOrigins.includes(origin)) {
    res.header('Access-Control-Allow-Origin', origin);
  } else if (config.IS_DEVELOPMENT) {
    // In development, allow various patterns
    if (origin && (
      origin.includes('localhost') ||
      origin.includes('127.0.0.1') ||
      origin.includes('0.0.0.0') ||
      origin.includes('devtunnels.ms') ||  // VS Code dev tunnels
      origin.includes('ngrok.io') ||       // ngrok tunnels
      origin.includes('localtunnel.me') || // localtunnel
      origin.includes('github.dev') ||     // GitHub Codespaces
      origin.includes('gitpod.io')         // Gitpod
    )) {
      res.header('Access-Control-Allow-Origin', origin);
      console.log(`🔓 CORS: Allowing development origin: ${origin}`);
    } else {
      // Fallback to localhost:3000 for development
      res.header('Access-Control-Allow-Origin', 'http://localhost:3000');
    }
  } else {
    // Production: handle domain-based origins and Cloudflare proxy
    const productionDomain = process.env.PRODUCTION_DOMAIN || 'localhost';
    const allowedProductionOrigins = [
      `https://${productionDomain}`,
      `https://www.${productionDomain}`,
      `http://${productionDomain}`,
      `http://www.${productionDomain}`,
      'http://localhost:3000',  // Fallback for local testing
      'https://localhost:3000'
    ];
    
    if (origin && allowedProductionOrigins.includes(origin)) {
      res.header('Access-Control-Allow-Origin', origin);
    } else {
      // Default to HTTPS domain for production
      res.header('Access-Control-Allow-Origin', `https://${productionDomain}`);
    }
  }

  // Allow ALL methods
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS, PATCH, HEAD');

  // Allow ALL headers
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control, Pragma, X-CSRF-Token');

  // Allow credentials
  res.header('Access-Control-Allow-Credentials', 'true');

  // Expose headers
  res.header('Access-Control-Expose-Headers', 'Authorization, Content-Length, X-Requested-With');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  next();
});

// Rate limiting - Using unified configuration
const limiter = rateLimit(config.RATE_LIMIT_CONFIG);

// More restrictive rate limiting for auth endpoints
const authLimiter = rateLimit({
  ...config.AUTH_RATE_LIMIT_CONFIG,
  skipSuccessfulRequests: true // Don't count successful requests
});

app.use('/api/', limiter);
app.use('/api/auth/', authLimiter);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Static files for uploads
app.use('/uploads', express.static('uploads'));

// Static files for logo images
const imagesPath = path.join(__dirname, '..', 'client', 'public', 'images');
const logoPath = path.join(imagesPath, 'logo.jpg');
console.log('📁 Images static path:', imagesPath);
console.log('🖼️  Logo file exists:', fs.existsSync(logoPath));
app.use('/images', express.static(imagesPath));

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    message: 'Hauling QR Trip Management System API is running',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// CORS test endpoint
app.get('/cors-test', (req, res) => {
  console.log(`🧪 CORS Test request from origin: ${req.headers.origin || 'no-origin'}`);
  res.status(200).json({
    message: 'CORS test successful',
    origin: req.headers.origin,
    timestamp: new Date().toISOString(),
    cors_config: {
      NODE_ENV: config.NODE_ENV,
      IS_DEVELOPMENT: config.IS_DEVELOPMENT,
      DEV_ENABLE_CORS_ALL: config.DEV_ENABLE_CORS_ALL
    }
  });
});

// Health check endpoint for monitoring
app.get('/api/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// Database health check endpoint
app.get('/api/health/db', async (req, res) => {
  try {
    const start = Date.now();
    const result = await query('SELECT 1 as health_check');
    const duration = Date.now() - start;
    
    res.json({
      status: 'OK',
      database: 'connected',
      query_time_ms: duration,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      status: 'ERROR',
      database: 'disconnected',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// API Routes with error handling
const routes = [
  { path: '/api/auth', file: './routes/auth' },
  { path: '/api/users', file: './routes/users' },
  { path: '/api/roles', file: './routes/roles' },
  { path: '/api/permissions', file: './routes/permissions' },
  { path: '/api/trucks', file: './routes/trucks' },
  { path: '/api/drivers', file: './routes/drivers' },
  { path: '/api/driver', file: './routes/driver' }, // Public driver connect API
  { path: '/api/driver-admin', file: './routes/driver-admin' }, // Admin driver management API
  { path: '/api/locations', file: './routes/locations' },
  { path: '/api/assignments', file: './routes/assignments' },
  { path: '/api/dynamic-assignments', file: './routes/dynamic-assignments' },
  { path: '/api/shifts', file: './routes/shifts' },
  { path: '/api/shift-transitions', file: './routes/shift-transitions' },
  { path: '/api/driver-sync', file: './routes/driverSync' },
  { path: '/api/trips', file: './routes/trips' },
  { path: '/api/scanner', file: './routes/scanner' },
  { path: '/api/analytics', file: './routes/analytics' },
  { path: '/api/approvals', file: './routes/approvals.js' },
  { path: '/api/admin', file: './routes/admin.js' },
  { path: '/api/system-health', file: './routes/system-health-routes' },
  { path: '/api/tasks', file: './routes/task-routes' },
  { path: '/api/upload', file: './routes/upload' },
  { path: '/api/validation', file: './routes/validation' },
  { path: '/api/data-flow-validation', file: './routes/data-flow-validation' },
  { path: '/api/log-management', file: './routes/log-management' },
  { path: '/api/fleet-resources', file: './routes/fleet-resources' }
];

routes.forEach(route => {
  try {
    app.use(route.path, require(route.file));
    console.log(`✅ Loaded route: ${route.path}`);
  } catch (error) {
    console.error(`❌ Failed to load route ${route.path}:`, error.message);
    // Create a fallback route that returns an error
    app.use(route.path, (req, res) => {
      res.status(500).json({
        error: 'Route Loading Error',
        message: `Route ${route.path} failed to load: ${error.message}`
      });
    });
  }
});

// Error handling middleware
// SSL error handler removed for development

app.use((err, req, res, next) => {
  console.error(err.stack);

  if (err.name === 'ValidationError') {
    return res.status(400).json({
      error: 'Validation Error',
      message: err.message,
      details: err.details
    });
  }

  if (err.name === 'UnauthorizedError') {
    return res.status(401).json({
      error: 'Unauthorized',
      message: 'Invalid token'
    });
  }

  res.status(err.status || 500).json({
    error: 'Internal Server Error',
    message: process.env.NODE_ENV === 'production' ? 'Something went wrong' : err.message
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Not Found',
    message: 'The requested resource was not found'
  });
});

// SSL Certificate loading with error handling (now using unified config)
function loadSSLCertificates() {
  return getSSLOptions(config);
}

// Start server with dual HTTP/HTTPS support
function startServer() {
  let server;
  let wss;

  if (config.ENABLE_HTTPS) {
    const sslOptions = loadSSLCertificates();

    if (sslOptions) {
      // Start HTTPS server
      server = https.createServer(sslOptions, app);

      console.log(`🔧 Attempting to bind HTTPS server to 0.0.0.0:${config.HTTPS_PORT}...`);

      server.listen(config.HTTPS_PORT, '0.0.0.0', () => {
        console.log(`🔐 HTTPS Server successfully bound to 0.0.0.0:${config.HTTPS_PORT}`);
        console.log(`🌍 Environment: ${config.NODE_ENV}`);
        console.log(`📊 Health check: https://localhost:${config.HTTPS_PORT}/health`);
        console.log(`📊 Health check: https://${config.IP_ADDRESS}:${config.HTTPS_PORT}/health`);

        // Display network information for mobile access
        console.log(`🌐 Network Access: https://${config.IP_ADDRESS}:${config.HTTPS_PORT}`);

      });

      // Start HTTP server for redirects (production only)
      if (config.IS_PRODUCTION) {
        const httpApp = express();
        httpApp.use((req, res) => {
          res.redirect(301, `https://${req.header('host')}${req.url}`);
        });

        httpApp.listen(config.BACKEND_HTTP_PORT, '0.0.0.0', () => {
          console.log(`🔄 HTTP Redirect server running on port ${config.BACKEND_HTTP_PORT} (redirects to HTTPS)`);
        });
      }

    } else {
      console.warn('⚠️  HTTPS enabled but SSL certificates not available, falling back to HTTP');
      server = http.createServer(app);

      server.listen(config.BACKEND_HTTP_PORT, '0.0.0.0', () => {
        console.log(`🚀 HTTP Server running on port ${config.BACKEND_HTTP_PORT} (SSL fallback)`);
        console.log(`🌍 Environment: ${config.NODE_ENV}`);
        console.log(`📊 Health check: http://localhost:${config.BACKEND_HTTP_PORT}/health`);
        console.log(`📊 Health check: http://${config.IP_ADDRESS}:${config.BACKEND_HTTP_PORT}/health`);
      });
    }
  } else {
    // Start HTTP server only
    server = http.createServer(app);

    server.listen(config.BACKEND_HTTP_PORT, '0.0.0.0', () => {
      console.log(`🚀 HTTP Server running on port ${config.BACKEND_HTTP_PORT}`);
      console.log(`🌍 Environment: ${config.NODE_ENV}`);
      console.log(`📊 Health check: http://localhost:${config.BACKEND_HTTP_PORT}/health`);
      console.log(`📊 Health check: http://${config.IP_ADDRESS}:${config.BACKEND_HTTP_PORT}/health`);

      // Display network information for mobile access
      console.log(`🌐 Network Access: http://${config.IP_ADDRESS}:${config.BACKEND_HTTP_PORT}`);
    });
  }

  // Initialize WebSocket server
  wss = initializeWebSocket(server);
  console.log(`🔌 WebSocket server initialized for real-time notifications`);

  // Initialize enhanced shift transition system (async)
  (async () => {
    console.log('🔄 Initializing Enhanced Shift Management System...');
    try {
      // Update database functions for enhanced compatibility
      await updateCaptureDriverFunction();

      // Start automatic shift transitions (disabled by configuration)
      const shiftSystemStarted = false; // Disabled automatic shift transitions

      if (shiftSystemStarted) {
        console.log('✅ Enhanced Shift Management System initialized successfully');
        console.log('   • Automatic shift transitions: ACTIVE');
        console.log('   • Date range scheduling: ENABLED');
        console.log('   • Intelligent shift classification: ENABLED');
        console.log('   • Performance monitoring: <300ms target');

        // Start enhanced shift status service
        console.log('🔄 Starting Enhanced Shift Status Service...');
        await EnhancedShiftStatusService.start();
        console.log('✅ Enhanced Shift Status Service started successfully');
        console.log('   • Real-time status evaluation: ACTIVE');
        console.log('   • Overnight logic: ENHANCED');
        console.log('   • Cross-system integration: ENABLED');
        console.log('   • Performance target: <300ms');

        // Start shift synchronization monitoring with environment-based intervals
        console.log('ℹ️ Shift Synchronization Monitor disabled (diagnostics-only).');

        // Start status synchronization monitoring with environment-based intervals
        console.log('🔄 Starting Status Synchronization Monitor...');
        statusSyncMonitor.start(); // Uses environment-based interval
        console.log('ℹ️ Status Synchronization Monitor startup skipped (automatic transitions disabled)');
        console.log(`   • Check interval: ${statusSyncMonitor.checkInterval / 1000} seconds`);
        console.log(`   • Environment: ${config.NODE_ENV}`);
        console.log(`   • Monitoring logs: ${config.LOG_CONFIG.monitoring.enabled ? 'ENABLED' : 'REDUCED'}`);
        console.log(`   • Log level: ${config.LOG_CONFIG.level.toUpperCase()}`);
      } else {
        console.warn('⚠️  Shift Management System initialization failed - manual shifts only');
      }
    } catch (error) {
      console.error('❌ Shift Management System initialization error:', error);
      console.warn('⚠️  Continuing with basic shift functionality');
    }
  })();

  // Initialize driver synchronization service
  (async () => {
    console.log('🔄 Initializing Driver Synchronization Service...');
    try {
      const driverSyncService = require('./services/driverSynchronization');

      // Start automatic driver synchronization
      driverSyncService.startAutoSync();

      console.log('✅ Driver Synchronization Service initialized successfully');
      console.log('   • Automatic shift-assignment sync: ACTIVE');
      console.log('   • 4-phase workflow protection: ENABLED');
      console.log('   • Cross-system consistency: ENABLED');
      console.log('   • Sync interval: 30 seconds');
    } catch (error) {
      console.error('❌ Driver Synchronization Service initialization error:', error);
      console.warn('⚠️  Continuing without automatic driver synchronization');
    }
  })();

  // Handle server errors
  server.on('error', (error) => {
    if (error.code === 'EADDRINUSE') {
      console.error(`❌ Port ${config.ENABLE_HTTPS ? config.HTTPS_PORT : config.BACKEND_HTTP_PORT} is already in use`);
    } else {
      console.error('❌ Server error:', error);
    }
    process.exit(1);
  });

  return { server, wss };
}

// Global error handlers
process.on('uncaughtException', (error) => {
  console.error('🚨 Uncaught Exception:', error);
  console.error('Stack:', error.stack);
  EnhancedShiftStatusService.stop();
  shutdownShiftTransitions();
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('🚨 Unhandled Rejection at:', promise, 'reason:', reason);
  EnhancedShiftStatusService.stop();
  shutdownShiftTransitions();
  process.exit(1);
});

// Graceful shutdown handlers
process.on('SIGTERM', () => {
  console.log('🛑 SIGTERM received, shutting down gracefully...');
  EnhancedShiftStatusService.stop();
  shiftSyncMonitor.stop();
  statusSyncMonitor.stop();
  shutdownShiftTransitions();
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('🛑 SIGINT received, shutting down gracefully...');
  EnhancedShiftStatusService.stop();
  shiftSyncMonitor.stop();
  statusSyncMonitor.stop();
  shutdownShiftTransitions();
  process.exit(0);
});

// Start the server
const { server, wss } = startServer();

module.exports = { app, server, wss };