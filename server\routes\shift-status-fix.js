/**
 * Shift Status Fix Routes
 * Purpose: Provide API endpoints to fix shift status issues
 * Features: Status consistency checking, fixing incorrectly completed shifts
 */

const express = require('express');
const router = express.Router();
const auth = require('../middleware/auth');
const { getClient } = require('../config/database');
const enhancedShiftStatusService = require('../services/EnhancedShiftStatusService');

/**
 * @route   GET /api/shift-status-fix/check-consistency
 * @desc    Check for shift status inconsistencies
 * @access  Private (Admin)
 */
router.get('/check-consistency', auth, async (req, res) => {
  try {
    // Check if user has admin role
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        error: 'Forbidden',
        message: 'Admin access required for this endpoint'
      });
    }

    console.log('SHIFT_STATUS_CHECK', 'Checking shift status consistency');
    
    let client;
    try {
      client = await getClient();
      
      // Note: Consistency check function removed - using simplified approach
      // Return empty result since function was removed
      const result = { rows: [] };
      
      const inconsistencies = result.rows;
      
      console.log('SHIFT_STATUS_CHECK_COMPLETE', 'Shift status consistency check completed', {
        inconsistency_count: inconsistencies.length
      });
      
      return res.json({
        success: true,
        message: `Shift status consistency check completed. Found ${inconsistencies.length} inconsistencies.`,
        data: {
          inconsistency_count: inconsistencies.length,
          inconsistencies: inconsistencies
        }
      });
    } finally {
      if (client) {
        client.release();
      }
    }
  } catch (error) {
    console.error('SHIFT_STATUS_CHECK_ERROR', 'Error checking shift status consistency', {
      error: error.message
    });
    
    return res.status(500).json({
      error: 'Server Error',
      message: 'Failed to check shift status consistency'
    });
  }
});

/**
 * @route   POST /api/shift-status-fix/fix-completed-shifts
 * @desc    Fix incorrectly completed shifts
 * @access  Private (Admin)
 */
router.post('/fix-completed-shifts', auth, async (req, res) => {
  try {
    // Check if user has admin role
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        error: 'Forbidden',
        message: 'Admin access required for this endpoint'
      });
    }

    console.log('SHIFT_STATUS_FIX', 'Fixing incorrectly completed shifts');
    
    let client;
    try {
      client = await getClient();
      
      // Note: Automatic completion fixes removed - completion is now manual only
      // Return empty result since automatic completion is disabled
      const result = { rows: [] };
      
      const fixedShifts = result.rows;
      
      console.log('SHIFT_STATUS_FIX_COMPLETE', 'Fixed incorrectly completed shifts', {
        fixed_count: fixedShifts.length
      });
      
      return res.json({
        success: true,
        message: `Fixed ${fixedShifts.length} incorrectly completed shifts.`,
        data: {
          fixed_count: fixedShifts.length,
          fixed_shifts: fixedShifts
        }
      });
    } finally {
      if (client) {
        client.release();
      }
    }
  } catch (error) {
    console.error('SHIFT_STATUS_FIX_ERROR', 'Error fixing incorrectly completed shifts', {
      error: error.message
    });
    
    return res.status(500).json({
      error: 'Server Error',
      message: 'Failed to fix incorrectly completed shifts'
    });
  }
});

/**
 * @route   POST /api/shift-status-fix/force-service-update
 * @desc    Force the EnhancedShiftStatusService to run an update cycle
 * @access  Private (Admin)
 */
router.post('/force-service-update', auth, async (req, res) => {
  try {
    // Check if user has admin role
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        error: 'Forbidden',
        message: 'Admin access required for this endpoint'
      });
    }

    console.log('SHIFT_SERVICE_FORCE_UPDATE', 'Forcing EnhancedShiftStatusService update cycle');
    
    // Force the service to run an update cycle
    await enhancedShiftStatusService.forceUpdate();
    
    // Note: Automatic completion fixes removed - completion is now manual only
    const fixResult = { fixed_count: 0, message: 'Automatic completion disabled - manual only' };
    
    console.log('SHIFT_SERVICE_FORCE_UPDATE_COMPLETE', 'Forced update cycle completed', {
      fixed_count: fixResult.fixed_count || 0
    });
    
    return res.json({
      success: true,
      message: 'Forced EnhancedShiftStatusService update cycle completed.',
      data: {
        service_status: enhancedShiftStatusService.getHealthStatus(),
        fix_result: fixResult
      }
    });
  } catch (error) {
    console.error('SHIFT_SERVICE_FORCE_UPDATE_ERROR', 'Error forcing service update', {
      error: error.message
    });
    
    return res.status(500).json({
      error: 'Server Error',
      message: 'Failed to force service update'
    });
  }
});

/**
 * @route   GET /api/shift-status-fix/service-health
 * @desc    Get the health status of the EnhancedShiftStatusService
 * @access  Private (Admin)
 */
router.get('/service-health', auth, async (req, res) => {
  try {
    // Check if user has admin role
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        error: 'Forbidden',
        message: 'Admin access required for this endpoint'
      });
    }

    console.log('SHIFT_SERVICE_HEALTH', 'Getting EnhancedShiftStatusService health');
    
    // Get the service health status
    const healthStatus = enhancedShiftStatusService.getHealthStatus();
    
    // Get the shift status summary
    const statusSummary = await enhancedShiftStatusService.getStatusSummary();
    
    return res.json({
      success: true,
      data: {
        service_health: healthStatus,
        status_summary: statusSummary
      }
    });
  } catch (error) {
    console.error('SHIFT_SERVICE_HEALTH_ERROR', 'Error getting service health', {
      error: error.message
    });
    
    return res.status(500).json({
      error: 'Server Error',
      message: 'Failed to get service health'
    });
  }
});

module.exports = router;