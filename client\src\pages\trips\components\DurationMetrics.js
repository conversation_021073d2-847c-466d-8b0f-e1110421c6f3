import React, { useMemo, useState, useEffect } from 'react';
import { tripsAPI } from '../../../services/api';

const DurationMetrics = ({ trips, loading = false, useServerCalculations = false, compact = false }) => {
  const [serverMetrics, setServerMetrics] = useState(null);
  const [serverLoading, setServerLoading] = useState(false);

  // Fetch server-side calculated metrics for better performance
  useEffect(() => {
    if (useServerCalculations && !loading) {
      const fetchServerMetrics = async () => {
        setServerLoading(true);
        try {
          const response = await tripsAPI.getDurationStats();
          setServerMetrics(response.data.data);
        } catch (error) {
          console.error('Failed to fetch server metrics:', error);
          // Fallback to client-side calculations
        } finally {
          setServerLoading(false);
        }
      };

      fetchServerMetrics();
    }
  }, [useServerCalculations, loading, trips.length]);
  // Enhanced duration calculations with precise time logic
  const durationMetrics = useMemo(() => {
    // Use server-side calculations if available for better performance
    if (useServerCalculations && serverMetrics && !serverLoading) {
      return serverMetrics;
    }
    if (!trips || trips.length === 0) {
      return {
        totalTripDuration: 0,
        totalLoadingDuration: 0,
        totalUnloadingDuration: 0,
        totalLoadingToUnloadingTravel: 0,
        totalUnloadingToLoadingTravel: 0,
        averages: {
          avgTripDuration: 0,
          avgLoadingDuration: 0,
          avgUnloadingDuration: 0,
          avgLoadingToUnloadingTravel: 0,
          avgUnloadingToLoadingTravel: 0
        },
        completedTripsCount: 0,
        activeTripsCount: 0
      };
    }

    // Helper function to calculate time difference in minutes
    const calculateTimeDifference = (startTime, endTime) => {
      if (!startTime || !endTime) return 0;
      const start = new Date(startTime);
      const end = new Date(endTime);
      return Math.max(0, Math.round((end - start) / (1000 * 60))); // Convert to minutes
    };

    // Helper function to determine if trip has complete data for calculations
    const hasCompleteTimestamps = (trip) => {
      return trip.loading_start_time && trip.loading_end_time && 
             trip.unloading_start_time && trip.unloading_end_time;
    };

    // Filter trips for different calculations
    const completedTrips = trips.filter(trip => 
      trip.status === 'trip_completed' || hasCompleteTimestamps(trip)
    );
    
    const activeTrips = trips.filter(trip => 
      !['trip_completed', 'cancelled'].includes(trip.status)
    );

    let totalTripDuration = 0;
    let totalLoadingDuration = 0;
    let totalUnloadingDuration = 0;
    let totalLoadingToUnloadingTravel = 0;
    let totalUnloadingToLoadingTravel = 0;

    // Counters for accurate averaging (only count trips with valid data)
    let validTripDurationCount = 0;
    let validLoadingDurationCount = 0;
    let validUnloadingDurationCount = 0;
    let validLoadingToUnloadingTravelCount = 0;
    let validUnloadingToLoadingTravelCount = 0;

    trips.forEach(trip => {
      // Calculate all durations first so they are available for tripDuration calculation
      // 2. Loading Duration Calculation
      let loadingDuration = 0;
      if (trip.loading_duration_minutes) {
        loadingDuration = trip.loading_duration_minutes;
      } else if (trip.loading_start_time && trip.loading_end_time) {
        loadingDuration = calculateTimeDifference(trip.loading_start_time, trip.loading_end_time);
      }

      // 3. Unloading Duration Calculation
      let unloadingDuration = 0;
      if (trip.unloading_duration_minutes) {
        unloadingDuration = trip.unloading_duration_minutes;
      } else if (trip.unloading_start_time && trip.unloading_end_time) {
        unloadingDuration = calculateTimeDifference(trip.unloading_start_time, trip.unloading_end_time);
      }

      // 4. Travel Time from Loading to Unloading Location (Travel_1)
      let loadingToUnloadingTravel = 0;
      
      // Method 1: Direct calculation from timestamps
      if (trip.loading_end_time && trip.unloading_start_time) {
        loadingToUnloadingTravel = calculateTimeDifference(trip.loading_end_time, trip.unloading_start_time);
      }
      // Method 2: Use server-provided travel_1_minutes or travel_duration_minutes
      else if (trip.travel_1_minutes) {
        loadingToUnloadingTravel = trip.travel_1_minutes;
      }
      else if (trip.travel_duration_minutes) {
        loadingToUnloadingTravel = trip.travel_duration_minutes;
      }
      // Method 3: Calculate from total minus loading and unloading durations (account for return travel)
      else if (trip.total_duration_minutes && loadingDuration > 0 && unloadingDuration > 0) {
        // Check if we have any return travel data first
        let knownReturnTravel = 0;
        if (trip.travel_2_minutes) {
          knownReturnTravel = trip.travel_2_minutes;
        } else if (trip.unloading_to_loading_travel_minutes) {
          knownReturnTravel = trip.unloading_to_loading_travel_minutes;
        }
        
        if (knownReturnTravel > 0) {
          // Forward = Total - Load - Unload - Return
          loadingToUnloadingTravel = Math.max(0, trip.total_duration_minutes - loadingDuration - unloadingDuration - knownReturnTravel);
        } else {
          // No return data, assume Forward = Total - Load - Unload
          loadingToUnloadingTravel = Math.max(0, trip.total_duration_minutes - loadingDuration - unloadingDuration);
        }
      }

      // 5. Travel Time from Unloading back to Loading Location
      // This is calculated from unloading_end to next trip's loading_start (for return trips)
      let unloadingToLoadingTravel = 0;
      
      // Method 1: CORRECT CALCULATION FIRST - unloading_end_time to trip_completed_time
      if (trip.unloading_end_time && trip.trip_completed_time) {
        unloadingToLoadingTravel = calculateTimeDifference(trip.unloading_end_time, trip.trip_completed_time);
      }
      // Method 2: Check for server-provided return travel data
      else if (trip.travel_2_minutes) {
        unloadingToLoadingTravel = trip.travel_2_minutes;
      } else if (trip.unloading_to_loading_travel_minutes) {
        unloadingToLoadingTravel = trip.unloading_to_loading_travel_minutes;
      }
      // Method 3: Fallback - Calculate from total duration
      else if (trip.total_duration_minutes && loadingDuration > 0 && unloadingDuration > 0 && loadingToUnloadingTravel > 0) {
        unloadingToLoadingTravel = Math.max(0, trip.total_duration_minutes - loadingDuration - loadingToUnloadingTravel - unloadingDuration);
      }
      // Method 4: Don't use estimation - leave as 0 if no actual data
      // This prevents incorrect identical values for forward and return travel

      // 1. Total Trip Duration Calculation - Always calculate from components for accuracy
      let tripDuration = 0;
      
      // Priority 1: Calculate from all components (most accurate with corrected values)
      if (loadingDuration >= 0 && loadingToUnloadingTravel >= 0 && unloadingDuration >= 0 && unloadingToLoadingTravel >= 0) {
        tripDuration = loadingDuration + loadingToUnloadingTravel + unloadingDuration + unloadingToLoadingTravel;
      }
      // Priority 2: Direct timestamp calculation
      else if (trip.loading_start_time && trip.trip_completed_time) {
        tripDuration = calculateTimeDifference(trip.loading_start_time, trip.trip_completed_time);
      }
      // Priority 3: Alternative timestamp calculation
      else if (trip.loading_start_time && trip.unloading_end_time) {
        tripDuration = calculateTimeDifference(trip.loading_start_time, trip.unloading_end_time);
      }
      // Priority 4: Calculate from complete timestamps
      else if (hasCompleteTimestamps(trip)) {
        const loadingDur = calculateTimeDifference(trip.loading_start_time, trip.loading_end_time);
        const travelDur = calculateTimeDifference(trip.loading_end_time, trip.unloading_start_time);
        const unloadingDur = calculateTimeDifference(trip.unloading_start_time, trip.unloading_end_time);
        tripDuration = loadingDur + travelDur + unloadingDur;
      }
      // Priority 5: Fallback to database value
      else {
        tripDuration = trip.total_duration_minutes || 0;
      }

      if (tripDuration > 0) {
        totalTripDuration += tripDuration;
        validTripDurationCount++;
      }

      if (loadingDuration > 0) {
        totalLoadingDuration += loadingDuration;
        validLoadingDurationCount++;
      }

      if (unloadingDuration > 0) {
        totalUnloadingDuration += unloadingDuration;
        validUnloadingDurationCount++;
      }

      if (loadingToUnloadingTravel > 0) {
        totalLoadingToUnloadingTravel += loadingToUnloadingTravel;
        validLoadingToUnloadingTravelCount++;
      }

      if (unloadingToLoadingTravel > 0) {
        totalUnloadingToLoadingTravel += unloadingToLoadingTravel;
        validUnloadingToLoadingTravelCount++;
      }
    });

    // Calculate averages
    const averages = {
      avgTripDuration: validTripDurationCount > 0 ? Math.round(totalTripDuration / validTripDurationCount) : 0,
      avgLoadingDuration: validLoadingDurationCount > 0 ? Math.round(totalLoadingDuration / validLoadingDurationCount) : 0,
      avgUnloadingDuration: validUnloadingDurationCount > 0 ? Math.round(totalUnloadingDuration / validUnloadingDurationCount) : 0,
      avgLoadingToUnloadingTravel: validLoadingToUnloadingTravelCount > 0 ? Math.round(totalLoadingToUnloadingTravel / validLoadingToUnloadingTravelCount) : 0,
      avgUnloadingToLoadingTravel: validUnloadingToLoadingTravelCount > 0 ? Math.round(totalUnloadingToLoadingTravel / validUnloadingToLoadingTravelCount) : 0
    };

    return {
      totalTripDuration,
      totalLoadingDuration,
      totalUnloadingDuration,
      totalLoadingToUnloadingTravel,
      totalUnloadingToLoadingTravel,
      averages,
      completedTripsCount: completedTrips.length,
      activeTripsCount: activeTrips.length,
      validCounts: {
        validTripDurationCount,
        validLoadingDurationCount,
        validUnloadingDurationCount,
        validLoadingToUnloadingTravelCount,
        validUnloadingToLoadingTravelCount
      }
    };
  }, [trips, serverMetrics, serverLoading, useServerCalculations]);

  // Format duration helper
  const formatDuration = (minutes) => {
    if (!minutes || minutes === 0) return '0m';
    
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    
    if (hours > 0) {
      return `${hours}h ${mins}m`;
    }
    return `${mins}m`;
  };

  // Format efficiency percentage
  const calculateEfficiency = (actualDuration, totalTime) => {
    if (!totalTime || totalTime === 0) return 0;
    return Math.round((actualDuration / totalTime) * 100);
  };

  if (loading || serverLoading) {
    return (
      <div className="bg-white p-4 rounded-lg shadow-sm border border-secondary-200">
        <div className="animate-pulse space-y-4">
          <div className="h-4 bg-secondary-200 rounded w-1/2"></div>
          <div className={`grid gap-3 ${compact ? 'grid-cols-1' : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-5'}`}>
            {[...Array(compact ? 3 : 5)].map((_, i) => (
              <div key={i} className="space-y-2">
                <div className="h-3 bg-secondary-200 rounded w-full"></div>
                <div className="h-6 bg-secondary-200 rounded w-2/3"></div>
                <div className="h-2 bg-secondary-200 rounded w-full"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (compact) {
    return (
      <div className="bg-white p-4 rounded-lg shadow-sm border border-secondary-200">
        <div className="space-y-4">
          {/* Compact Summary */}
          <div className="text-center p-3 bg-blue-50 rounded-lg">
            <div className="text-lg font-semibold text-blue-900">
              {formatDuration(durationMetrics.averages?.avgTripDuration || 0)}
            </div>
            <div className="text-xs text-blue-600">Average Trip Time</div>
          </div>

          {/* Key Metrics */}
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm text-secondary-600">Loading</span>
              <span className="text-sm font-medium text-secondary-900">
                {formatDuration(durationMetrics.averages?.avgLoadingDuration || 0)}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-secondary-600">Travel</span>
              <span className="text-sm font-medium text-secondary-900">
                {formatDuration(durationMetrics.averages?.avgLoadingToUnloadingTravel || 0)}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-secondary-600">Unloading</span>
              <span className="text-sm font-medium text-secondary-900">
                {formatDuration(durationMetrics.averages?.avgUnloadingDuration || 0)}
              </span>
            </div>
          </div>

          {/* Trip Counts */}
          <div className="pt-3 border-t border-secondary-200">
            <div className="flex justify-between text-xs text-secondary-500">
              <span>{durationMetrics.completedTripsCount || 0} completed</span>
              <span>{durationMetrics.activeTripsCount || 0} active</span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border border-secondary-200">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-lg font-semibold text-secondary-900">Duration Metrics Analysis</h2>
          <p className="text-sm text-secondary-600 mt-1">
            Comprehensive analysis of trip duration components ({durationMetrics.completedTripsCount || 0} completed, {durationMetrics.activeTripsCount || 0} active trips)
          </p>
        </div>
        <div className="text-right">
          <div className="text-sm text-secondary-500">
            Loading Efficiency: {calculateEfficiency(durationMetrics.totalLoadingDuration || 0, durationMetrics.totalTripDuration)}%
          </div>
          <div className="text-sm text-secondary-500">
            Travel Efficiency: {calculateEfficiency(durationMetrics.totalLoadingToUnloadingTravel || 0, durationMetrics.totalTripDuration)}%
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
        {/* 1. Total Trip Duration */}
        <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-4 rounded-lg border border-blue-200">
          <div className="flex items-center justify-between mb-2">
            <span className="text-2xl">🕐</span>
            <span className="text-xs bg-blue-200 text-blue-800 px-2 py-1 rounded-full">
              {durationMetrics.validCounts?.validTripDurationCount || 0} trips
            </span>
          </div>
          <div className="space-y-1">
            <h3 className="text-sm font-medium text-blue-900">Total Trip Duration</h3>
            <div className="text-2xl font-bold text-blue-800">
              {formatDuration(durationMetrics.totalTripDuration || 0)}
            </div>
            <div className="text-xs text-blue-600">
              Avg: {formatDuration(durationMetrics.averages?.avgTripDuration || 0)}
            </div>
            <div className="text-xs text-blue-500">
              From assignment start to completion
            </div>
          </div>
        </div>

        {/* 2. Loading Duration */}
        <div className="bg-gradient-to-br from-green-50 to-green-100 p-4 rounded-lg border border-green-200">
          <div className="flex items-center justify-between mb-2">
            <span className="text-2xl">⬆️</span>
            <span className="text-xs bg-green-200 text-green-800 px-2 py-1 rounded-full">
              {durationMetrics.validCounts?.validLoadingDurationCount || 0} loads
            </span>
          </div>
          <div className="space-y-1">
            <h3 className="text-sm font-medium text-green-900">Loading Duration</h3>
            <div className="text-2xl font-bold text-green-800">
              {formatDuration(durationMetrics.totalLoadingDuration || 0)}
            </div>
            <div className="text-xs text-green-600">
              Avg: {formatDuration(durationMetrics.averages?.avgLoadingDuration || 0)}
            </div>
            <div className="text-xs text-green-500">
              Time spent at loading locations
            </div>
          </div>
        </div>

        {/* 3. Unloading Duration */}
        <div className="bg-gradient-to-br from-purple-50 to-purple-100 p-4 rounded-lg border border-purple-200">
          <div className="flex items-center justify-between mb-2">
            <span className="text-2xl">⬇️</span>
            <span className="text-xs bg-purple-200 text-purple-800 px-2 py-1 rounded-full">
              {durationMetrics.validCounts?.validUnloadingDurationCount || 0} unloads
            </span>
          </div>
          <div className="space-y-1">
            <h3 className="text-sm font-medium text-purple-900">Unloading Duration</h3>
            <div className="text-2xl font-bold text-purple-800">
              {formatDuration(durationMetrics.totalUnloadingDuration || 0)}
            </div>
            <div className="text-xs text-purple-600">
              Avg: {formatDuration(durationMetrics.averages?.avgUnloadingDuration || 0)}
            </div>
            <div className="text-xs text-purple-500">
              Time spent at unloading locations
            </div>
          </div>
        </div>

        {/* 4. Loading to Unloading Travel */}
        <div className="bg-gradient-to-br from-orange-50 to-orange-100 p-4 rounded-lg border border-orange-200">
          <div className="flex items-center justify-between mb-2">
            <span className="text-2xl">🚛</span>
            <span className="text-xs bg-orange-200 text-orange-800 px-2 py-1 rounded-full">
              {durationMetrics.validCounts?.validLoadingToUnloadingTravelCount || 0} routes
            </span>
          </div>
          <div className="space-y-1">
            <h3 className="text-sm font-medium text-orange-900">Forward Travel</h3>
            <div className="text-2xl font-bold text-orange-800">
              {formatDuration(durationMetrics.totalLoadingToUnloadingTravel || 0)}
            </div>
            <div className="text-xs text-orange-600">
              Avg: {formatDuration(durationMetrics.averages?.avgLoadingToUnloadingTravel || 0)}
            </div>
            <div className="text-xs text-orange-500">
              Loading → Unloading locations
            </div>
          </div>
        </div>

        {/* 5. Unloading to Loading Travel (Return) */}
        <div className="bg-gradient-to-br from-teal-50 to-teal-100 p-4 rounded-lg border border-teal-200">
          <div className="flex items-center justify-between mb-2">
            <span className="text-2xl">🔄</span>
            <span className="text-xs bg-teal-200 text-teal-800 px-2 py-1 rounded-full">
              {durationMetrics.validCounts?.validUnloadingToLoadingTravelCount || 0} returns
            </span>
          </div>
          <div className="space-y-1">
            <h3 className="text-sm font-medium text-teal-900">Return Travel</h3>
            <div className="text-2xl font-bold text-teal-800">
              {formatDuration(durationMetrics.totalUnloadingToLoadingTravel || 0)}
            </div>
            <div className="text-xs text-teal-600">
              Avg: {formatDuration(durationMetrics.averages?.avgUnloadingToLoadingTravel || 0)}
            </div>
            <div className="text-xs text-teal-500">
              Unloading → Loading locations
            </div>
          </div>
        </div>
      </div>

      {/* Additional Insights */}
      <div className="mt-6 pt-4 border-t border-secondary-200">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center p-3 bg-secondary-50 rounded-lg">
            <div className="text-lg font-semibold text-secondary-900">
              {formatDuration(durationMetrics.averages?.avgTripDuration || 0)}
            </div>
            <div className="text-sm text-secondary-600">Average Trip Time</div>
          </div>
          <div className="text-center p-3 bg-secondary-50 rounded-lg">
            <div className="text-lg font-semibold text-secondary-900">
              {calculateEfficiency(
                durationMetrics.totalLoadingDuration || 0 + durationMetrics.totalUnloadingDuration || 0,
                durationMetrics.totalTripDuration
              )}%
            </div>
            <div className="text-sm text-secondary-600">Operational Efficiency</div>
          </div>
          <div className="text-center p-3 bg-secondary-50 rounded-lg">
            <div className="text-lg font-semibold text-secondary-900">
              {formatDuration(
                durationMetrics.averages?.avgLoadingToUnloadingTravel || 0 + 
                durationMetrics.averages?.avgUnloadingToLoadingTravel || 0
              )}
            </div>
            <div className="text-sm text-secondary-600">Avg Round Trip Travel</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DurationMetrics;