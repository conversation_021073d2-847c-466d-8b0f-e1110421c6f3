// Performance monitoring utilities
class PerformanceMonitor {
  constructor() {
    this.marks = new Map();
    this.measures = new Map();
  }

  // Mark a performance point
  mark(name) {
    if ('performance' in window && 'mark' in performance) {
      performance.mark(name);
      this.marks.set(name, performance.now());
    }
  }

  // Measure time between two marks
  measure(name, startMark, endMark) {
    if ('performance' in window && 'measure' in performance) {
      try {
        performance.measure(name, startMark, endMark);
        const measure = performance.getEntriesByName(name, 'measure')[0];
        this.measures.set(name, measure.duration);
        return measure.duration;
      } catch (error) {
        console.warn('Performance measurement failed:', error);
        return null;
      }
    }
    return null;
  }

  // Get all performance metrics
  getMetrics() {
    return {
      marks: Object.fromEntries(this.marks),
      measures: Object.fromEntries(this.measures),
      navigation: this.getNavigationTiming(),
      memory: this.getMemoryInfo()
    };
  }

  // Get navigation timing information
  getNavigationTiming() {
    if ('performance' in window && 'getEntriesByType' in performance) {
      const navigation = performance.getEntriesByType('navigation')[0];
      if (navigation) {
        return {
          domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
          loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
          domInteractive: navigation.domInteractive - navigation.navigationStart,
          firstPaint: this.getFirstPaint(),
          firstContentfulPaint: this.getFirstContentfulPaint()
        };
      }
    }
    return null;
  }

  // Get memory information (Chrome only)
  getMemoryInfo() {
    if ('memory' in performance) {
      return {
        usedJSHeapSize: performance.memory.usedJSHeapSize,
        totalJSHeapSize: performance.memory.totalJSHeapSize,
        jsHeapSizeLimit: performance.memory.jsHeapSizeLimit
      };
    }
    return null;
  }

  // Get First Paint timing
  getFirstPaint() {
    if ('performance' in window && 'getEntriesByType' in performance) {
      const paintEntries = performance.getEntriesByType('paint');
      const firstPaint = paintEntries.find(entry => entry.name === 'first-paint');
      return firstPaint ? firstPaint.startTime : null;
    }
    return null;
  }

  // Get First Contentful Paint timing
  getFirstContentfulPaint() {
    if ('performance' in window && 'getEntriesByType' in performance) {
      const paintEntries = performance.getEntriesByType('paint');
      const firstContentfulPaint = paintEntries.find(entry => entry.name === 'first-contentful-paint');
      return firstContentfulPaint ? firstContentfulPaint.startTime : null;
    }
    return null;
  }

  // Log performance metrics to console (development only)
  logMetrics() {
    if (process.env.NODE_ENV === 'development') {
      console.group('Performance Metrics');
      console.table(this.getMetrics());
      console.groupEnd();
    }
  }

  // Report metrics to analytics service
  reportMetrics(analyticsService) {
    if (analyticsService && typeof analyticsService.track === 'function') {
      analyticsService.track('app_performance', this.getMetrics());
    }
  }
}

export const performanceMonitor = new PerformanceMonitor();

// Initialize performance monitoring
export function initializePerformanceMonitoring() {
  performanceMonitor.mark('app-init-start');
  
  // Monitor when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      performanceMonitor.mark('dom-content-loaded');
    });
  } else {
    performanceMonitor.mark('dom-content-loaded');
  }

  // Monitor when everything is loaded
  window.addEventListener('load', () => {
    performanceMonitor.mark('app-init-end');
    performanceMonitor.measure('app-initialization', 'app-init-start', 'app-init-end');
    
    // Log metrics in development
    setTimeout(() => {
      performanceMonitor.logMetrics();
    }, 1000);
  });
}

export default performanceMonitor;