/**
 * Shift Transition Integration
 * Purpose: Integrate enhanced shift transitions with existing server infrastructure
 * Issues Addressed: ISSUE 1 - Missing Automatic Shift Status Transition
 * 
 * Integration Points:
 * 1. Server startup initialization
 * 2. Existing captureActiveDriverInfo function enhancement
 * 3. API endpoints for manual control
 * 4. Health monitoring and logging
 */

const { shiftTransitionManager } = require('./enhanced-shift-transitions');
const { getClient } = require('../config/database');

/**
 * Initialize shift transition system on server startup
 */
function initializeShiftTransitions() {
  console.log('ℹ️ Enhanced Shift Transition System startup skipped (automatic transitions disabled)');

  try {
    // Do not start the automatic transition system
    // shiftTransitionManager.start();

    // Diagnostics-only: no automatic activation or completion logs
    return false;
  } catch (error) {
    console.error('❌ Failed to initialize shift transition system:', error);
    return false;
  }
}

/**
 * Gracefully shutdown shift transition system
 */
function shutdownShiftTransitions() {
  console.log('🛑 Shutting down Enhanced Shift Transition System...');
  
  try {
    shiftTransitionManager.stop();
    console.log('✅ Shift transition system stopped gracefully');
  } catch (error) {
    console.error('❌ Error during shift transition shutdown:', error);
  }
}

/**
 * Enhanced version of captureActiveDriverInfo that integrates with new system
 * This maintains backward compatibility while adding enhanced features
 */
async function captureActiveDriverInfoEnhanced(client, truckId, timestamp = new Date()) {
  try {
    // Use the enhanced driver lookup that supports date ranges and recurrence patterns
    const driverInfo = await shiftTransitionManager.getCurrentActiveDriverEnhanced(truckId, timestamp);
    
    if (driverInfo) {
      console.log('DRIVER_CAPTURE_ENHANCED', 'Active driver captured with enhanced features', {
        truck_id: truckId,
        driver_id: driverInfo.driver_id,
        driver_name: driverInfo.driver_name,
        shift_type: driverInfo.shift_type,
        display_type: driverInfo.display_type,
        recurrence_pattern: driverInfo.recurrence_pattern,
        timestamp: timestamp.toISOString()
      });
      
      return {
        driver_id: driverInfo.driver_id,
        driver_name: driverInfo.driver_name,
        employee_id: driverInfo.employee_id,
        shift_id: driverInfo.shift_id,
        shift_type: driverInfo.shift_type,
        display_type: driverInfo.display_type // New field for intelligent classification
      };
    } else {
      console.log('DRIVER_CAPTURE_ENHANCED', 'No active driver found for truck', {
        truck_id: truckId,
        timestamp: timestamp.toISOString()
      });
      return null;
    }
  } catch (error) {
    console.error('DRIVER_CAPTURE_ENHANCED_ERROR', 'Error capturing active driver', {
      truck_id: truckId,
      timestamp: timestamp.toISOString(),
      error: error.message
    });
    
    // Fallback to original function if enhanced version fails
    try {
      const fallbackResult = await client.query(`
        SELECT * FROM capture_active_driver_for_trip($1, $2)
      `, [truckId, timestamp]);
      
      return fallbackResult.rows[0] || null;
    } catch (fallbackError) {
      console.error('DRIVER_CAPTURE_FALLBACK_ERROR', 'Fallback also failed', {
        truck_id: truckId,
        error: fallbackError.message
      });
      return null;
    }
  }
}

/**
 * Health check function for shift transition system
 */
async function getShiftTransitionHealth() {
  try {
    const status = await shiftTransitionManager.getSystemStatus();
    
    const health = {
      status: 'healthy',
      system_running: status.is_running,
      transition_interval_ms: status.transition_interval_ms,
      shift_counts: status.shift_counts,
      timestamp: new Date().toISOString()
    };
    
    // Check for potential issues
    const warnings = [];
    
    if (!status.is_running) {
      warnings.push('Automatic transitions are not running');
      health.status = 'warning';
    }
    
    if (status.shift_counts.scheduled > 50) {
      warnings.push('High number of scheduled shifts - check for activation issues');
    }
    
    if (status.shift_counts.active > 100) {
      warnings.push('High number of active shifts - check for completion issues');
    }
    
    if (warnings.length > 0) {
      health.warnings = warnings;
      if (health.status === 'healthy') {
        health.status = 'warning';
      }
    }
    
    return health;
  } catch (error) {
    return {
      status: 'error',
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * Manual shift control functions for API endpoints
 */
const manualShiftControls = {
  /**
   * Manually activate a specific shift
   */
  async activateShift(shiftId) {
    try {
      const result = await shiftTransitionManager.activateShift(shiftId);
      return {
        success: true,
        message: 'Shift activated successfully',
        shift_id: shiftId,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        shift_id: shiftId,
        timestamp: new Date().toISOString()
      };
    }
  },

  /**
   * Force run transition cycle
   */
  async forceTransitionCycle() {
    try {
      await shiftTransitionManager.runTransitions();
      return {
        success: true,
        message: 'Transition cycle completed',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  },

  /**
   * Get current system status
   */
  async getStatus() {
    return await getShiftTransitionHealth();
  }
};

/**
 * Database function to update existing capture_active_driver_for_trip function
 * This ensures backward compatibility while adding enhanced features
 */
async function updateCaptureDriverFunction() {
  let client;
  
  try {
    client = await getClient();
    
    // Create enhanced version that maintains backward compatibility
    await client.query(`
      CREATE OR REPLACE FUNCTION capture_active_driver_for_trip_enhanced(
        p_truck_id INTEGER,
        p_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      ) RETURNS TABLE (
        driver_id INTEGER,
        driver_name VARCHAR(100),
        employee_id VARCHAR(20),
        shift_id INTEGER,
        shift_type shift_type,
        display_type shift_type
      ) AS $$
      BEGIN
        RETURN QUERY
        SELECT 
          ds.driver_id,
          d.full_name as driver_name,
          d.employee_id,
          ds.id as shift_id,
          ds.shift_type,
          COALESCE(ds.display_type, ds.shift_type) as display_type
        FROM driver_shifts ds
        JOIN drivers d ON ds.driver_id = d.id
        WHERE ds.truck_id = p_truck_id
          AND ds.status = 'active'
          AND (
            -- Single date shifts (backward compatibility)
            (ds.recurrence_pattern = 'single' AND ds.shift_date = p_timestamp::date)
            OR
            -- Date range shifts with recurrence patterns
            (ds.recurrence_pattern != 'single' AND p_timestamp::date BETWEEN ds.start_date AND ds.end_date
              AND (
                (ds.recurrence_pattern = 'daily')
                OR
                (ds.recurrence_pattern = 'weekly' AND EXTRACT(DOW FROM p_timestamp::date) = EXTRACT(DOW FROM ds.start_date))
                OR
                (ds.recurrence_pattern = 'weekdays' AND EXTRACT(DOW FROM p_timestamp::date) BETWEEN 1 AND 5)
                OR
                (ds.recurrence_pattern = 'weekends' AND EXTRACT(DOW FROM p_timestamp::date) IN (0, 6))
                OR
                (ds.recurrence_pattern = 'custom')
              )
            )
          )
          AND p_timestamp::time BETWEEN ds.start_time AND 
              CASE 
                WHEN ds.end_time < ds.start_time 
                THEN ds.end_time + interval '24 hours'
                ELSE ds.end_time 
              END
        ORDER BY ds.created_at DESC
        LIMIT 1;
      END;
      $$ LANGUAGE plpgsql;
    `);

    // Update the original function to use enhanced logic while maintaining interface
    await client.query(`
      CREATE OR REPLACE FUNCTION capture_active_driver_for_trip(
        p_truck_id INTEGER,
        p_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      ) RETURNS TABLE (
        driver_id INTEGER,
        driver_name VARCHAR(100),
        employee_id VARCHAR(20),
        shift_id INTEGER,
        shift_type shift_type
      ) AS $$
      BEGIN
        RETURN QUERY
        SELECT 
          enh.driver_id,
          enh.driver_name,
          enh.employee_id,
          enh.shift_id,
          enh.shift_type
        FROM capture_active_driver_for_trip_enhanced(p_truck_id, p_timestamp) enh;
      END;
      $$ LANGUAGE plpgsql;
    `);

    console.log('✅ Enhanced capture_active_driver_for_trip function updated');
    return true;
  } catch (error) {
    console.error('❌ Failed to update capture driver function:', error);
    return false;
  } finally {
    if (client) {
      client.release();
    }
  }
}

/**
 * Performance monitoring for shift transitions
 */
class ShiftTransitionMonitor {
  constructor() {
    this.metrics = {
      cycles_run: 0,
      total_activations: 0,
      total_completions: 0,
      average_cycle_time: 0,
      last_cycle_time: 0,
      errors: 0
    };
  }

  recordCycle(duration, activations, completions, hasError = false) {
    this.metrics.cycles_run++;
    this.metrics.total_activations += activations;
    this.metrics.total_completions += completions;
    this.metrics.last_cycle_time = duration;
    
    // Calculate rolling average
    this.metrics.average_cycle_time = 
      (this.metrics.average_cycle_time * (this.metrics.cycles_run - 1) + duration) / this.metrics.cycles_run;
    
    if (hasError) {
      this.metrics.errors++;
    }
  }

  getMetrics() {
    return {
      ...this.metrics,
      error_rate: this.metrics.cycles_run > 0 ? this.metrics.errors / this.metrics.cycles_run : 0,
      timestamp: new Date().toISOString()
    };
  }

  reset() {
    this.metrics = {
      cycles_run: 0,
      total_activations: 0,
      total_completions: 0,
      average_cycle_time: 0,
      last_cycle_time: 0,
      errors: 0
    };
  }
}

const performanceMonitor = new ShiftTransitionMonitor();

module.exports = {
  initializeShiftTransitions,
  shutdownShiftTransitions,
  captureActiveDriverInfoEnhanced,
  getShiftTransitionHealth,
  manualShiftControls,
  updateCaptureDriverFunction,
  performanceMonitor
};
