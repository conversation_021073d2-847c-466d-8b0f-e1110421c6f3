import React, { useState, useEffect, useCallback } from 'react';
import useWebSocket from '../../hooks/useWebSocket';
import { useAuth } from '../../context/AuthContext';
import toast from 'react-hot-toast';

// Tab Components
import FleetOverview from './components/FleetOverview/FleetOverview';
import TripPerformance from './components/TripPerformance/TripPerformance';
import LiveOperations from './components/LiveOperations/LiveOperations';

const AnalyticsReports = () => {
  const [activeTab, setActiveTab] = useState('fleet-overview');
  const [loading, setLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState(new Date());
  const { user } = useAuth();
  const { lastMessage, isConnected } = useWebSocket(user);

  // Handle real-time WebSocket updates
  useEffect(() => {
    if (lastMessage && lastMessage.type) {
      switch (lastMessage.type) {
        case 'analytics_update':
        case 'fleet_status_changed':
        case 'live_operations_update':
        case 'stopped_analytics_update':
        case 'trip_performance_update':
          setLastUpdated(new Date());
          // Trigger refresh in child components via timestamp
          break;
        case 'performance_alert':
          toast.error(`Performance Alert: ${lastMessage.message}`, {
            duration: 6000,
            icon: '⚠️'
          });
          break;
        default:
          break;
      }
    }
  }, [lastMessage]);

  // Auto-refresh data every 30 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setLastUpdated(new Date());
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  const tabs = [
    {
      id: 'fleet-overview',
      name: 'Fleet Overview',
      icon: '🚛',
      description: 'Real-time fleet metrics and status'
    },
    {
      id: 'trip-performance',
      name: 'Trip Performance',
      icon: '📊',
      description: 'Performance analytics and insights'
    },
    {
      id: 'live-operations',
      name: 'Live Operations',
      icon: '🔴',
      description: 'Real-time operations monitoring'
    }
  ];

  const handleTabChange = (tabId) => {
    setActiveTab(tabId);
  };

  const handleRefresh = useCallback(() => {
    setLoading(true);
    setLastUpdated(new Date());
    
    // Show refresh feedback
    toast.success('Analytics data refreshed', {
      duration: 2000,
      icon: '🔄'
    });
    
    setTimeout(() => setLoading(false), 1000);
  }, []);

  return (
    <div className="min-h-screen bg-secondary-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-secondary-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-secondary-900">
                📈 Analytics & Reports
              </h1>
              <div className="ml-4 flex items-center text-sm text-secondary-500">
                <div className={`w-2 h-2 rounded-full mr-2 ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
                {isConnected ? 'Live' : 'Disconnected'}
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="text-sm text-secondary-500">
                Last updated: {lastUpdated.toLocaleTimeString()}
              </div>
              <button
                onClick={handleRefresh}
                disabled={loading}
                className="inline-flex items-center px-3 py-2 border border-secondary-300 shadow-sm text-sm leading-4 font-medium rounded-md text-secondary-700 bg-white hover:bg-secondary-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
              >
                <span className={`mr-2 ${loading ? 'animate-spin' : ''}`}>🔄</span>
                Refresh
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <nav className="flex space-x-8" aria-label="Tabs">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => handleTabChange(tab.id)}
                className={`${
                  activeTab === tab.id
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-secondary-500 hover:text-secondary-700 hover:border-secondary-300'
                } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200`}
              >
                <div className="flex items-center">
                  <span className="mr-2 text-lg">{tab.icon}</span>
                  <div className="text-left">
                    <div className="font-medium">{tab.name}</div>
                    <div className="text-xs text-secondary-400 hidden sm:block">
                      {tab.description}
                    </div>
                  </div>
                </div>
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Tab Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {activeTab === 'fleet-overview' && (
          <FleetOverview 
            lastUpdated={lastUpdated}
            isConnected={isConnected}
            loading={loading}
          />
        )}
        
        {activeTab === 'trip-performance' && (
          <TripPerformance 
            lastUpdated={lastUpdated}
            isConnected={isConnected}
            loading={loading}
          />
        )}
        
        {activeTab === 'live-operations' && (
          <LiveOperations 
            lastUpdated={lastUpdated}
            isConnected={isConnected}
            loading={loading}
          />
        )}
      </div>
    </div>
  );
};

export default AnalyticsReports;
