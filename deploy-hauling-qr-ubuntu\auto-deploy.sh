#!/bin/bash

# =============================================================================
# HAULING QR TRIP SYSTEM - ENHANCED UBUNTU VPS DEPLOYMENT SCRIPT
# =============================================================================
# Version: 2.1.0
# Compatible with: Ubuntu 24.04 LTS, 22.04 LTS, 20.04 LTS
# Description: Complete automated deployment with IP detection and health checks
# =============================================================================

set -euo pipefail

# =============================================================================
# CONFIGURATION VARIABLES
# =============================================================================
readonly VERSION="2.1.0"
readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly LOG_DIR="/var/log/hauling-deployment"
readonly LOG_FILE="${LOG_DIR}/auto-deploy-$(date +%Y%m%d-%H%M%S).log"
readonly GITHUB_REPO="mightybadz18/hauling-qr-trip-management"
readonly GITHUB_PAT="${GITHUB_PAT:-}"
readonly GITHUB_USERNAME="${GITHUB_USERNAME:-}"
readonly PRODUCTION_DOMAIN="truckhaul.top"
readonly MANUAL_IP="${MANUAL_IP:-}"

# Application Configuration
readonly APP_NAME="hauling-qr-system"
readonly APP_DIR="/var/www/${APP_NAME}"
readonly DB_NAME="hauling_qr_db"
readonly DB_USER="hauling_qr_user"
readonly DB_PASSWORD="$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-25)"
readonly JWT_SECRET="$(openssl rand -base64 64 | tr -d "=+/" | cut -c1-50)"

# Network Configuration
DETECTED_VPS_IP=""
readonly CLIENT_PORT=3000
readonly SERVER_HTTP_PORT=8080
readonly SERVER_HTTPS_PORT=8443
# Legacy support
readonly SERVER_PORT=8080

# Color codes for output
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly CYAN='\033[0;36m'
readonly NC='\033[0m' # No Color

# =============================================================================
# LOGGING FUNCTIONS
# =============================================================================
# Create log directory
mkdir -p "$LOG_DIR"
chmod 755 "$LOG_DIR"

ts() { date '+%Y-%m-%d %H:%M:%S'; }
log() { echo "[$(ts)] $*" | tee -a "$LOG_FILE"; }

log_info() {
  log "INFO  | $1"
}

log_success() {
  log "OK    | $1"
}

log_warning() {
  log "WARN  | $1"
}

log_error() {
  log "ERROR | $1"
}

# =============================================================================
# VPS IP DETECTION FUNCTIONS
# =============================================================================
detect_vps_ip() {
  log_info "🔍 Detecting VPS IP address..."
  
  # Use manual IP if provided
  if [[ -n "${MANUAL_IP}" ]]; then
    DETECTED_VPS_IP="${MANUAL_IP}"
    log_success "✅ Using manual IP: ${DETECTED_VPS_IP}"
    return 0
  fi
  
  # Method 1: ipinfo.io
  if [[ -z "${DETECTED_VPS_IP}" ]]; then
    DETECTED_VPS_IP=$(curl -s --connect-timeout 10 ipinfo.io/ip 2>/dev/null || echo "")
    if [[ -n "${DETECTED_VPS_IP}" ]] && [[ "${DETECTED_VPS_IP}" =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]]; then
      log_success "✅ IP detected via ipinfo.io: ${DETECTED_VPS_IP}"
      return 0
    fi
  fi
  
  # Method 2: ipify.org
  if [[ -z "${DETECTED_VPS_IP}" ]]; then
    DETECTED_VPS_IP=$(curl -s --connect-timeout 10 https://api.ipify.org 2>/dev/null || echo "")
    if [[ -n "${DETECTED_VPS_IP}" ]] && [[ "${DETECTED_VPS_IP}" =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]]; then
      log_success "✅ IP detected via ipify.org: ${DETECTED_VPS_IP}"
      return 0
    fi
  fi
  
  # Method 3: OpenDNS
  if [[ -z "${DETECTED_VPS_IP}" ]]; then
    DETECTED_VPS_IP=$(dig +short myip.opendns.com @resolver1.opendns.com 2>/dev/null || echo "")
    if [[ -n "${DETECTED_VPS_IP}" ]] && [[ "${DETECTED_VPS_IP}" =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]]; then
      log_success "✅ IP detected via OpenDNS: ${DETECTED_VPS_IP}"
      return 0
    fi
  fi
  
  # Method 4: ip route (local network interface)
  if [[ -z "${DETECTED_VPS_IP}" ]]; then
    DETECTED_VPS_IP=$(ip route get ******* 2>/dev/null | awk '{print $7; exit}' || echo "")
    if [[ -n "${DETECTED_VPS_IP}" ]] && [[ "${DETECTED_VPS_IP}" =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]]; then
      log_success "✅ IP detected via ip route: ${DETECTED_VPS_IP}"
      return 0
    fi
  fi
  
  # Method 5: httpbin.org
  if [[ -z "${DETECTED_VPS_IP}" ]]; then
    DETECTED_VPS_IP=$(curl -s --connect-timeout 10 https://httpbin.org/ip 2>/dev/null | grep -oP '(?<="origin": ")[^"]*' || echo "")
    if [[ -n "${DETECTED_VPS_IP}" ]] && [[ "${DETECTED_VPS_IP}" =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]]; then
      log_success "✅ IP detected via httpbin.org: ${DETECTED_VPS_IP}"
      return 0
    fi
  fi
  
  log_error "❌ Failed to detect VPS IP address"
  log_error "Please set MANUAL_IP environment variable with your VPS IP"
  return 1
}

# =============================================================================
# SYSTEM DEPENDENCIES INSTALLATION
# =============================================================================
install_dependencies() {
  log_info "Installing system dependencies..."
  export DEBIAN_FRONTEND=noninteractive
  apt-get update -y >>"$LOG_FILE" 2>&1
  apt-get install -y ca-certificates gnupg lsb-release build-essential git curl nginx postgresql postgresql-contrib net-tools tzdata >>"$LOG_FILE" 2>&1

  # Node.js LTS (20.x) via NodeSource
  if ! command -v node >/dev/null 2>&1; then
    log_info "Installing Node.js LTS (20.x)"
    curl -fsSL https://deb.nodesource.com/setup_20.x | bash - >>"$LOG_FILE" 2>&1
    apt-get install -y nodejs >>"$LOG_FILE" 2>&1
  else
    log_info "Node.js already installed: $(node -v)"
  fi

  # PM2 global
  if ! command -v pm2 >/dev/null 2>&1; then
    log_info "Installing PM2 globally"
    npm install -g pm2 >>"$LOG_FILE" 2>&1
  else
    log_info "PM2 already installed: $(pm2 -v)"
  fi

  systemctl enable nginx >/dev/null 2>&1 || true
  systemctl start nginx >/dev/null 2>&1 || true

  # Ensure PostgreSQL is enabled and started
  systemctl enable postgresql >/dev/null 2>&1 || true
  systemctl start postgresql >/dev/null 2>&1 || true
  
  log_success "✅ System dependencies installed"
}

# =============================================================================
# TIMEZONE CONFIGURATION
# =============================================================================
configure_timezone() {
  log_info "🌏 Configuring timezone to Asia/Manila (Philippines)..."
  
  # Check current timezone
  local current_tz=$(timedatectl show --property=Timezone --value 2>/dev/null || cat /etc/timezone 2>/dev/null || echo "Unknown")
  log_info "Current timezone: $current_tz"
  
  if [[ "$current_tz" == "Asia/Manila" ]]; then
    log_success "✅ Timezone already set to Asia/Manila"
    return 0
  fi
  
  # Set timezone to Manila/Philippines
  if command -v timedatectl >/dev/null 2>&1; then
    # Method 1: Using timedatectl (systemd)
    log_info "Setting timezone using timedatectl..."
    timedatectl set-timezone Asia/Manila >>"$LOG_FILE" 2>&1
    
    if [[ $? -eq 0 ]]; then
      log_success "✅ Timezone set to Asia/Manila using timedatectl"
    else
      log_warning "⚠️ timedatectl failed, trying manual method..."
      # Fallback to manual method
      ln -sf /usr/share/zoneinfo/Asia/Manila /etc/localtime
      echo "Asia/Manila" > /etc/timezone
      log_success "✅ Timezone set to Asia/Manila using manual method"
    fi
  else
    # Method 2: Manual method for older systems
    log_info "Setting timezone using manual method..."
    ln -sf /usr/share/zoneinfo/Asia/Manila /etc/localtime
    echo "Asia/Manila" > /etc/timezone
    log_success "✅ Timezone set to Asia/Manila using manual method"
  fi
  
  # Verify timezone change
  local new_tz=$(timedatectl show --property=Timezone --value 2>/dev/null || cat /etc/timezone 2>/dev/null || echo "Unknown")
  local current_time=$(date)
  
  log_success "✅ Timezone Configuration Complete"
  log_info "New timezone: $new_tz"
  log_info "Current time: $current_time"
  
  # Update system time if needed
  if command -v timedatectl >/dev/null 2>&1; then
    timedatectl set-ntp true >>"$LOG_FILE" 2>&1 || true
    log_info "NTP synchronization enabled"
  fi
}

# =============================================================================
# CLEANUP AND PREPARATION
# =============================================================================
cleanup_previous_deployments() {
  log_info "🧹 Cleaning up previous deployments and fixing issues..."
  
  # Install net-tools if missing (for netstat command)
  if ! command -v netstat &> /dev/null; then
    log_info "Installing net-tools for system monitoring..."
    sudo apt-get update -y
    sudo apt-get install -y net-tools
  fi
  
  # Check and fix existing deployment in /opt/hauling-qr
  if [[ -d "/opt/hauling-qr" ]]; then
    log_warning "Found existing deployment in /opt/hauling-qr, cleaning up..."
    
    # Stop any running PM2 processes
    pm2 delete all 2>/dev/null || true
    
    # Remove old deployment
    sudo rm -rf /opt/hauling-qr
    log_success "✅ Removed old deployment from /opt/hauling-qr"
  fi
  
  # Ensure target directory is clean
  if [[ -d "${APP_DIR}" ]]; then
    log_warning "Removing existing application directory..."
    rm -rf "${APP_DIR}"
  fi
  
  # Stop any conflicting services
  sudo systemctl stop nginx 2>/dev/null || true
  pm2 kill 2>/dev/null || true
  
  log_success "✅ Cleanup completed"
}

# =============================================================================
# DEPENDENCY VERIFICATION AND SETUP
# =============================================================================
verify_and_install_node_dependencies() {
  log_info "🔍 Verifying Node.js dependencies..."
  
  cd "${APP_DIR}"
  
  # Check if package.json exists
  if [[ ! -f "package.json" ]]; then
    log_warning "No package.json found in root directory"
    return 0
  fi
  
  # Clean install to avoid conflicts
  log_info "Cleaning npm cache and node_modules..."
  npm cache clean --force 2>/dev/null || true
  rm -rf node_modules package-lock.json 2>/dev/null || true
  
  # Install all dependencies including dev dependencies initially
  log_info "Installing all Node.js dependencies..."
  npm install
  
  # Force install critical modules
  local critical_modules=("pg" "express" "cors" "helmet" "bcryptjs" "jsonwebtoken")
  for module in "${critical_modules[@]}"; do
    log_info "Force installing critical module: $module"
    npm install "$module" --save
  done
  
  # Verify pg module specifically for database operations
  log_info "Testing pg module accessibility..."
  if node -e "require('pg'); console.log('pg module works')" 2>/dev/null; then
    log_success "✅ pg module verified and working"
  else
    log_warning "⚠️ pg module not working, attempting multiple fixes..."
    npm uninstall pg 2>/dev/null || true
    npm install pg --save --force
    npm rebuild pg 2>/dev/null || true
    
    # Test again
    if node -e "require('pg'); console.log('pg module works')" 2>/dev/null; then
      log_success "✅ pg module fixed and working"
    else
      log_error "❌ pg module still not working - will attempt manual fix during migration"
    fi
  fi
  
  log_success "✅ Node.js dependencies verification completed"
}

# =============================================================================
# REPOSITORY FETCH AND SETUP
# =============================================================================


# =============================================================================
# REPOSITORY AUTHENTICATION HELPER
# =============================================================================
compose_auth_repo_url() {
  local url="$1"
  local pat="$2"
  local username="$3"
  if [[ -z "$pat" ]]; then echo "$url"; return; fi
  # Only inject PAT for https GitHub URLs
  if [[ "$url" =~ ^https://github.com/ ]]; then
    if [[ -n "$username" ]]; then
      echo "https://${username}:${pat}@github.com/${url#https://github.com/}"
    else
      # Token-only authentication (GitHub accepts PAT as username)
      echo "https://${pat}@github.com/${url#https://github.com/}"
    fi
  else
    echo "$url" # leave as-is for ssh or already-authenticated
  fi
}

fetch_repository() {
  # Mask credentials in logs for security
  local repo_url="https://github.com/${GITHUB_REPO}.git"
  local safe_url="$repo_url"
  if [[ -n "$GITHUB_USERNAME" && -n "$GITHUB_PAT" ]]; then
    safe_url=${safe_url//${GITHUB_USERNAME}:${GITHUB_PAT}/***:***}
  fi
  safe_url=${safe_url//https:\/\/[^@]*@/https:\/\/***@}

  log_info "Performing fresh repository clone: $safe_url (branch: main)"

  local auth_url="$repo_url"
  if [[ -n "$GITHUB_PAT" ]]; then
    auth_url=$(compose_auth_repo_url "$repo_url" "$GITHUB_PAT" "$GITHUB_USERNAME")
  fi

  # Force fresh clone - remove existing directory if it exists
  if [[ -d "$APP_DIR" ]]; then
    log_info "Removing existing directory: $APP_DIR"
    rm -rf "$APP_DIR"
  fi

  # Create parent directory
  local parent_dir=$(dirname "$APP_DIR")
  mkdir -p "$parent_dir"

  log_info "Cloning fresh repository to $APP_DIR"
  if git clone --branch main --depth 1 "$auth_url" "$APP_DIR" >>"$LOG_FILE" 2>&1; then
    log_success "Repository cloned successfully"
    log_info "Repository contents:"
    ls -la "$APP_DIR" | head -10 >>"$LOG_FILE" 2>&1
  else
    log_error "Failed to clone repository. Possible causes:"
    log_error "1. Repository is private and requires GITHUB_PAT"
    log_error "2. Invalid GITHUB_PAT or insufficient permissions"
    log_error "3. Network connectivity issues"
    log_error "4. Invalid repository URL or branch name"
    log_error ""
    log_error "For private repositories, set GITHUB_PAT environment variable:"
    log_error "export GITHUB_PAT=your_token_here"
    log_error "sudo -E ./auto-deploy-complete-fixed.sh"
    exit 1
  fi
  log_success "Fresh repository clone completed"
}

# =============================================================================
# ENVIRONMENT HANDLING
# =============================================================================
ensure_env_file() {
  # The .env file should be cloned with the repository
  if [[ -f "$APP_DIR/.env" ]]; then
    log_info ".env found in cloned repository"
  elif [[ -f "$APP_DIR/.env.example" ]]; then
    log_info "Creating .env from .env.example template"
    cp -f "$APP_DIR/.env.example" "$APP_DIR/.env"
  else
    # Create a basic production .env file as fallback
    log_warning "No .env or .env.example found in repository; creating basic production .env"
    create_basic_env_file
  fi

  # Update .env for production deployment
  update_env_for_production
  chmod 600 "$APP_DIR/.env"
}

create_basic_env_file() {
  log_info "Creating production .env file (mirroring development configuration)..."
  cat > "$APP_DIR/.env" << 'EOF'
# Environment Configuration - Production mode with development-style flexibility
NODE_ENV=production
ENABLE_HTTPS=false
AUTO_DETECT_IP=false

# Manual IP Configuration (replaces VPS_IP)
MANUAL_IP=${DETECTED_VPS_IP}
PRODUCTION_DOMAIN=truckhaul.top

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=hauling_qr_system
DB_USER=hauling_app
DB_PASSWORD=PostgreSQLPasswordHaulingQRSystem

# Server Configuration - Cloudflare Compatible Ports
BACKEND_HTTP_PORT=8080
HTTPS_PORT=8443
FRONTEND_PORT=3000
CLIENT_PORT=3000
# Legacy support
PORT=8080
BACKEND_PORT=8080

# Security
JWT_SECRET=hauling_qr_jwt_secret_2025_secure_key_for_production
JWT_EXPIRY=24h

# Frontend URLs - API Subdomain Configuration (No Port 5000)
REACT_APP_API_URL=https://api.truckhaul.top/api
REACT_APP_API_URL_FALLBACK=http://${DETECTED_VPS_IP}/api
REACT_APP_WS_URL=wss://api.truckhaul.top/ws
REACT_APP_WS_URL_FALLBACK=ws://${DETECTED_VPS_IP}/ws
REACT_APP_USE_HTTPS=true
REACT_APP_VPS_IP=${DETECTED_VPS_IP}

# CORS Configuration - API Subdomain Support (No Port 5000)
CORS_ORIGIN=https://truckhaul.top,http://truckhaul.top,https://api.truckhaul.top,http://api.truckhaul.top,http://${DETECTED_VPS_IP},https://${DETECTED_VPS_IP},http://localhost:3000
ALLOWED_ORIGINS=https://truckhaul.top,http://truckhaul.top,https://api.truckhaul.top,http://api.truckhaul.top,http://${DETECTED_VPS_IP},https://${DETECTED_VPS_IP},http://localhost:3000

# Development-style CORS enablement for production flexibility
DEV_ENABLE_CORS_ALL=true
DEV_ENABLE_DETAILED_LOGS=false
DEV_DISABLE_RATE_LIMITING=true

# Rate Limiting - Disabled (development-style for production)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=10000
RATE_LIMIT_MAX_REQUESTS_PROD=20000
AUTH_RATE_LIMIT_WINDOW_MS=900000
AUTH_RATE_LIMIT_MAX_REQUESTS=500

# Logging - Production appropriate with CORS logging suppression
LOG_LEVEL=warn
MONITORING_LOGS_ENABLED=true
SUPPRESS_CORS_CONFIG_MESSAGES=true
DEV_ENABLE_CORS_LOGGING=false
CORS_LOGGING_ENABLED=false
EOF
  log_success "Production .env file created (development-style configuration)"
}

update_env_for_production() {
  log_info "Updating .env for production deployment (preserving development-style configuration)..."

  # Ensure NODE_ENV is production (primary change)
  sed -i 's/^NODE_ENV=.*/NODE_ENV=production/' "$APP_DIR/.env"

  # Keep AUTO_DETECT_IP=true (development-style)
  sed -i 's/^AUTO_DETECT_IP=.*/AUTO_DETECT_IP=true/' "$APP_DIR/.env"

  # Use MANUAL_IP instead of VPS_IP
  sed -i "s/^MANUAL_IP=.*/MANUAL_IP=${DETECTED_VPS_IP}/" "$APP_DIR/.env"
  sed -i "s/^PRODUCTION_DOMAIN=.*/PRODUCTION_DOMAIN=truckhaul.top/" "$APP_DIR/.env"

  # Ensure development-style CORS settings are enabled
  sed -i 's/^DEV_ENABLE_CORS_ALL=.*/DEV_ENABLE_CORS_ALL=true/' "$APP_DIR/.env"
  sed -i 's/^DEV_DISABLE_RATE_LIMITING=.*/DEV_DISABLE_RATE_LIMITING=true/' "$APP_DIR/.env"

  # Add fallback URLs for IP access (if not already present)
  if ! grep -q "^REACT_APP_API_URL_FALLBACK=" "$APP_DIR/.env"; then
    echo "REACT_APP_API_URL_FALLBACK=http://${DETECTED_VPS_IP}/api" >> "$APP_DIR/.env"
  fi
  if ! grep -q "^REACT_APP_WS_URL_FALLBACK=" "$APP_DIR/.env"; then
    echo "REACT_APP_WS_URL_FALLBACK=ws://${DETECTED_VPS_IP}" >> "$APP_DIR/.env"
  fi
  if ! grep -q "^REACT_APP_VPS_IP=" "$APP_DIR/.env"; then
    echo "REACT_APP_VPS_IP=${DETECTED_VPS_IP}" >> "$APP_DIR/.env"
  fi

  # Update primary URLs to use api subdomain (no port 5000)
  sed -i "s|^REACT_APP_API_URL=.*|REACT_APP_API_URL=https://api.truckhaul.top/api|" "$APP_DIR/.env"
  sed -i "s|^REACT_APP_WS_URL=.*|REACT_APP_WS_URL=wss://api.truckhaul.top/ws|" "$APP_DIR/.env"
  
  # Update CORS to include API subdomain
  sed -i "s|^CORS_ORIGIN=.*|CORS_ORIGIN=https://truckhaul.top,http://truckhaul.top,https://api.truckhaul.top,http://api.truckhaul.top,http://${DETECTED_VPS_IP},https://${DETECTED_VPS_IP},http://localhost:3000|" "$APP_DIR/.env"
  sed -i "s|^ALLOWED_ORIGINS=.*|ALLOWED_ORIGINS=https://truckhaul.top,http://truckhaul.top,https://api.truckhaul.top,http://api.truckhaul.top,http://${DETECTED_VPS_IP},https://${DETECTED_VPS_IP},http://localhost:3000|" "$APP_DIR/.env"

  # Add MANUAL_IP if it doesn't exist
  if ! grep -q "^MANUAL_IP=" "$APP_DIR/.env"; then
    echo "MANUAL_IP=${DETECTED_VPS_IP}" >> "$APP_DIR/.env"
  fi

  # Update database user for production (if using default postgres user)
  if grep -q "^DB_USER=postgres" "$APP_DIR/.env"; then
    sed -i 's/^DB_USER=.*/DB_USER=hauling_app/' "$APP_DIR/.env"
  fi

  # Ensure production JWT secret is used
  if grep -q "development" "$APP_DIR/.env" | grep -q "JWT_SECRET"; then
    sed -i 's/development/production/g' "$APP_DIR/.env"
  fi

  log_success "Production .env updated (development-style configuration with NODE_ENV=production)"
}

# =============================================================================
# ENVIRONMENT CONFIGURATION
# =============================================================================
create_production_env() {
  log_info "⚙️ Creating production environment configuration..."
  
  cat > "${APP_DIR}/.env" << EOF
# =============================================================================
# HAULING QR TRIP SYSTEM - PRODUCTION ENVIRONMENT
# =============================================================================
# Generated: $(date)
# VPS IP: ${DETECTED_VPS_IP}
# =============================================================================

# Environment
NODE_ENV=production
PORT=${SERVER_PORT}

# Network Configuration
DETECTED_VPS_IP=${DETECTED_VPS_IP}
PRODUCTION_DOMAIN=${PRODUCTION_DOMAIN}
AUTO_DETECT_IP=false
ENABLE_HTTPS=false

# CORS Configuration (Development-style for production flexibility)
CORS_ORIGIN=http://${DETECTED_VPS_IP}:${CLIENT_PORT},http://localhost:${CLIENT_PORT},http://${PRODUCTION_DOMAIN}
CORS_CREDENTIALS=true

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=${DB_NAME}
DB_USER=${DB_USER}
DB_PASSWORD=${DB_PASSWORD}
DATABASE_URL=postgresql://${DB_USER}:${DB_PASSWORD}@localhost:5432/${DB_NAME}

# Authentication
JWT_SECRET=${JWT_SECRET}
JWT_EXPIRES_IN=24h

# Client Configuration
REACT_APP_API_URL=http://${DETECTED_VPS_IP}:${SERVER_PORT}
REACT_APP_WS_URL=ws://${DETECTED_VPS_IP}:${SERVER_PORT}
REACT_APP_ENVIRONMENT=production

# Logging - Production with CORS suppression
LOG_LEVEL=warn
LOG_FILE=/home/<USER>/logs/hauling-qr-app.log
SUPPRESS_CORS_CONFIG_MESSAGES=true
DEV_ENABLE_CORS_LOGGING=false
CORS_LOGGING_ENABLED=false

# Performance
MAX_CONNECTIONS=100
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
EOF

  log_success "✅ Environment configuration created"
}

# =============================================================================
# DATABASE SETUP
# =============================================================================
setup_database() {
  log_info "🗄️ Setting up PostgreSQL database..."
  
  # Start PostgreSQL service
  sudo systemctl start postgresql
  sudo systemctl enable postgresql
  
  # Create database and user
  sudo -u postgres psql << EOF
CREATE DATABASE ${DB_NAME};
CREATE USER ${DB_USER} WITH ENCRYPTED PASSWORD '${DB_PASSWORD}';
GRANT ALL PRIVILEGES ON DATABASE ${DB_NAME} TO ${DB_USER};
ALTER USER ${DB_USER} CREATEDB;
\q
EOF
  
  # Run database migrations
  cd "${APP_DIR}"
  if [[ -d "database" ]]; then
    log_info "Running database migrations..."
    export DATABASE_URL="postgresql://${DB_USER}:${DB_PASSWORD}@localhost:5432/${DB_NAME}"
    
    # Ensure we're in the correct directory
    log_info "Current directory: $(pwd)"
    log_info "APP_DIR: ${APP_DIR}"
    
    # Double-check pg module availability
    if [[ ! -d "node_modules/pg" ]]; then
      log_info "pg module directory not found, installing..."
      npm install pg --save
    fi
    
    # Verify pg module is accessible with detailed error reporting
    log_info "Testing pg module accessibility..."
    if node -e "require('pg'); console.log('SUCCESS: pg module loaded')" 2>&1; then
      log_success "✅ pg module is available and working"
    else
      log_error "❌ pg module test failed, attempting comprehensive fix..."
      
      # Multiple fix attempts
      npm cache clean --force
      rm -rf node_modules/pg
      npm install pg --save --force
      npm rebuild pg
      
      # Final test
      if node -e "require('pg'); console.log('SUCCESS: pg module loaded')" 2>&1; then
        log_success "✅ pg module fixed successfully"
      else
        log_error "❌ pg module still failing - will skip migrations"
        return 1
      fi
    fi
    
    # Run migrations with better error handling
    if [[ -f "database/run-migration.js" ]]; then
      log_info "Executing database migrations..."
      if node database/run-migration.js; then
        log_success "✅ Database migrations completed successfully"
      else
        log_error "❌ Database migrations failed"
        log_info "Attempting to run initial schema setup..."
        
        # Try to run initial schema if migrations fail
        if [[ -f "database/init.sql" ]]; then
          log_info "Running initial database schema..."
          sudo -u postgres psql -d "${DB_NAME}" -f database/init.sql || log_warning "Initial schema setup failed"
        fi
        
        log_warning "⚠️ Migration issues detected - manual intervention may be required"
        log_info "You can run migrations manually later with: cd ${APP_DIR} && node database/run-migration.js"
      fi
    else
      log_warning "Migration script not found, checking for init.sql..."
      if [[ -f "database/init.sql" ]]; then
        log_info "Running initial database schema from init.sql..."
        sudo -u postgres psql -d "${DB_NAME}" -f database/init.sql
        log_success "✅ Initial database schema applied"
      else
        log_warning "No migration script or init.sql found, skipping database setup"
      fi
    fi
  fi
  
  log_success "✅ Database setup completed"
}

# =============================================================================
# APPLICATION BUILD
# =============================================================================
build_application() {
  log_info "Installing root dependencies (for migration runner)..."
  pushd "$APP_DIR" >/dev/null
  npm install >>"$LOG_FILE" 2>&1 || true
  popd >/dev/null

  log_info "Installing server dependencies..."
  pushd "$APP_DIR/server" >/dev/null
  npm install --production >>"$LOG_FILE" 2>&1
  popd >/dev/null

  log_info "Installing client dependencies and building with API subdomain configuration..."
  pushd "$APP_DIR/client" >/dev/null
  
  # Clean previous build to ensure fresh build with new environment
  rm -rf build node_modules/.cache 2>/dev/null || true
  
  npm install >>"$LOG_FILE" 2>&1
  
  # Verify environment variables are loaded for build
  log_info "Building client with API subdomain: https://api.truckhaul.top/api"
  
  # Build with ESLint warnings allowed and ensure environment variables are used
  GENERATE_SOURCEMAP=false DISABLE_ESLINT_PLUGIN=true npm run build >>"$LOG_FILE" 2>&1
  
  # Verify the build includes the correct API URL
  if grep -r "api.truckhaul.top" build/ >/dev/null 2>&1; then
    log_success "✅ Frontend built with API subdomain configuration"
  else
    log_warning "⚠️ Frontend may not have picked up API subdomain configuration"
  fi
  
  popd >/dev/null
}

# =============================================================================
# NGINX CONFIGURATION
# =============================================================================
configure_nginx() {
  log_info "Configuring Nginx (Cloudflare SSL termination)"
  # Load domain from .env if present
  local domain="${PRODUCTION_DOMAIN}"
  if [[ -f "$APP_DIR/.env" ]]; then
    domain=$(grep -E '^PRODUCTION_DOMAIN=' "$APP_DIR/.env" | sed -E 's/PRODUCTION_DOMAIN=\"?([^\"]*)\"?/\1/' || echo "${PRODUCTION_DOMAIN}")
  fi

  cat >/etc/nginx/sites-available/hauling-qr-system <<EOF
server {
    listen 80;
    server_name ${domain} www.${domain} api.${domain} ${DETECTED_VPS_IP};

    # Real IP from Cloudflare
    set_real_ip_from ************/22;
    set_real_ip_from ************/22;
    set_real_ip_from **********/22;
    set_real_ip_from **********/13;
    set_real_ip_from **********/14;
    set_real_ip_from *************/18;
    set_real_ip_from **********/22;
    set_real_ip_from ************/18;
    set_real_ip_from ***********/15;
    set_real_ip_from **********/13;
    set_real_ip_from ************/20;
    set_real_ip_from ************/20;
    set_real_ip_from ************/20;
    set_real_ip_from *************/22;
    set_real_ip_from ************/17;
    real_ip_header CF-Connecting-IP;

    # Security headers with WebSocket and WebAssembly support
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: ws: wss: data: blob: 'unsafe-inline' 'unsafe-eval'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; worker-src 'self' blob:; connect-src 'self' http: https: ws: wss: https://api.truckhaul.top wss://api.truckhaul.top;" always;

    # Static frontend
    root /var/www/hauling-qr-system/client/build;
    index index.html;

    location / {
        try_files \$uri \$uri/ /index.html;
    }

    # Images static path
    location /images {
        root /var/www/hauling-qr-system/client/public;
        expires 1d;
        add_header Cache-Control "public";
    }

    # API - Using Cloudflare compatible HTTPS port
    location /api {
        proxy_pass https://localhost:8443;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto https; # indicate HTTPS via Cloudflare
        proxy_read_timeout 86400;
        # SSL verification for internal HTTPS
        proxy_ssl_verify off;
    }

    # WebSocket support - Using Cloudflare compatible HTTPS port
    location /ws {
        proxy_pass https://localhost:8443;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "Upgrade";
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto https;
        proxy_read_timeout 86400;
        # SSL verification for internal HTTPS
        proxy_ssl_verify off;
    }

    # Socket.IO WebSocket support - Using Cloudflare compatible HTTPS port
    location /socket.io/ {
        proxy_pass https://localhost:8443;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto https;
        proxy_read_timeout 86400;
        # SSL verification for internal HTTPS
        proxy_ssl_verify off;
    }
}
EOF
  ln -sf /etc/nginx/sites-available/hauling-qr-system /etc/nginx/sites-enabled/
  rm -f /etc/nginx/sites-enabled/default || true
  nginx -t
  systemctl reload nginx
  log_success "Nginx configured"
}

# =============================================================================
# PM2 PROCESS MANAGEMENT
# =============================================================================
write_pm2_ecosystem() {
  log_info "Creating PM2 ecosystem.config.js"
  cat >"$APP_DIR/ecosystem.config.js" <<'EOF'
module.exports = {
  apps: [{
    name: 'hauling-qr-server',
    script: './server/server.js',
    cwd: '/var/www/hauling-qr-system',
    instances: 1,
    exec_mode: 'fork',
    env: {
      NODE_ENV: 'production',
      HTTPS_PORT: 8443,
      BACKEND_HTTP_PORT: 8080,
      ENABLE_HTTPS: true
    },
    log_file: '/home/<USER>/.pm2/logs/hauling-qr-server.log',
    out_file: '/home/<USER>/.pm2/logs/hauling-qr-server-out.log',
    error_file: '/home/<USER>/.pm2/logs/hauling-qr-server-error.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    merge_logs: true,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024'
  }]
};
EOF
  chown root:root "$APP_DIR/ecosystem.config.js"
  chmod 644 "$APP_DIR/ecosystem.config.js"
}

ensure_app_dirs() {
  mkdir -p "$APP_DIR/server/logs" "$APP_DIR/server/uploads"
  chown -R root:root "$APP_DIR/server"
  chmod -R 755 "$APP_DIR/server"
}

configure_pm2() {
  log_info "Starting PM2 app"
  pushd "$APP_DIR" >/dev/null

  # Stop any existing PM2 processes first
  pm2 delete hauling-qr-server 2>/dev/null || true
  pm2 kill 2>/dev/null || true

  # Ensure ecosystem.config.js exists and is correct
  if [[ ! -f "ecosystem.config.js" ]]; then
    log_error "ecosystem.config.js not found in $APP_DIR"
    exit 1
  fi

  # Start PM2 application with explicit configuration
  log_info "Starting PM2 with ecosystem.config.js"
  if pm2 start ecosystem.config.js --env production; then
    log_success "PM2 application started successfully"
  else
    log_error "Failed to start PM2 application"
    # Try alternative startup method
    log_info "Trying alternative PM2 startup method"
    if pm2 start server/server.js --name hauling-qr-server --env production; then
      log_success "PM2 application started with alternative method"
    else
      log_error "PM2 startup failed completely"
      exit 1
    fi
  fi

  # Save PM2 configuration
  pm2 save

  # Enable PM2 startup (systemd) for root
  pm2 startup systemd -u root --hp /root >/dev/null 2>&1 || true

  # Verify PM2 is running
  if pm2 list | grep -q "hauling-qr-server.*online"; then
    log_success "PM2 process verified as online"
  else
    log_error "PM2 process is not online"
    pm2 list
    pm2 logs hauling-qr-server --lines 10
    exit 1
  fi

  popd >/dev/null
}

# =============================================================================
# RESTART SERVICES AFTER PATCHES
# =============================================================================
restart_services_after_patches() {
  log_info "Restarting services to apply CORS patches and API subdomain configuration..."
  
  # Update Nginx configuration to fix CSP for WebSockets (no port 5000)
  log_info "Updating Nginx CSP headers for API subdomain WebSocket support..."
  
  # Fix CSP in the current Nginx config to support WebAssembly
  if [[ -f "/etc/nginx/sites-available/hauling-qr-system" ]]; then
    sed -i "s|Content-Security-Policy.*|Content-Security-Policy \"default-src 'self' http: https: ws: wss: data: blob: 'unsafe-inline' 'unsafe-eval'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; worker-src 'self' blob:; connect-src 'self' http: https: ws: wss: https://api.truckhaul.top wss://api.truckhaul.top https://truckhaul.top;\" always;|" /etc/nginx/sites-available/hauling-qr-system
  fi
  
  # Force rebuild frontend with new environment configuration
  log_info "Force rebuilding frontend with API subdomain configuration..."
  pushd "$APP_DIR/client" >/dev/null
  
  # Clean everything to ensure fresh build
  rm -rf build node_modules/.cache .env.local 2>/dev/null || true
  
  # Create a temporary .env.local to override any cached values
  cat > .env.local << EOF
REACT_APP_API_URL=https://api.truckhaul.top/api
REACT_APP_WS_URL=wss://api.truckhaul.top/ws
REACT_APP_USE_HTTPS=true
EOF
  
  # Rebuild with explicit environment
  REACT_APP_API_URL=https://api.truckhaul.top/api REACT_APP_WS_URL=wss://api.truckhaul.top/ws npm run build >>"$LOG_FILE" 2>&1
  
  # Verify the build contains the correct API URL
  if grep -r "api.truckhaul.top" build/ >/dev/null 2>&1; then
    log_success "✅ Frontend rebuilt with API subdomain"
  else
    log_error "❌ Frontend build may not have correct API configuration"
  fi
  
  popd >/dev/null
  
  # Restart PM2 application to apply server.js changes
  if pm2 list | grep -q "hauling-qr-server"; then
    log_info "Restarting PM2 application with CORS fixes..."
    pm2 restart hauling-qr-server
    sleep 5
    
    # Verify it's running
    if pm2 list | grep -q "hauling-qr-server.*online"; then
      log_success "PM2 application restarted successfully"
      
      # Test CORS configuration
      log_info "Testing CORS configuration..."
      sleep 2
      local cors_test=$(curl -s -I -H "Origin: https://truckhaul.top" https://api.truckhaul.top/api/health | grep "Access-Control-Allow-Origin" || echo "No CORS header")
      log_info "CORS test result: $cors_test"
      
    else
      log_error "PM2 application failed to restart"
      pm2 logs hauling-qr-server --lines 10
    fi
  fi
  
  # Restart Nginx
  log_info "Restarting Nginx with updated configuration..."
  nginx -t && systemctl restart nginx
  
  # Verify CORS logging suppression is active
  log_info "Verifying CORS logging suppression..."
  if grep -q "DEV_ENABLE_CORS_LOGGING=false" "$APP_DIR/.env" && grep -q "SUPPRESS_CORS_CONFIG_MESSAGES=true" "$APP_DIR/.env"; then
    log_success "✅ CORS logging suppression verified in environment"
  else
    log_warning "⚠️ CORS logging suppression may not be fully configured"
  fi
  
  log_success "Services restarted - API subdomain configuration and CORS logging suppression active"
  log_info "Frontend: https://truckhaul.top"
  log_info "API: https://api.truckhaul.top/api"
  log_info "WebSocket: wss://api.truckhaul.top/ws"
  log_info "🔇 CORS logging suppression: ENABLED (reduces PM2 log spam)"
}

# =============================================================================
# FIX CSP FOR WEBASSEMBLY (QR SCANNER CAMERA)
# =============================================================================
fix_csp_for_webassembly() {
  log_info "🔧 Fixing CSP headers to support WebAssembly (QR Scanner Camera)..."
  
  # Update Nginx CSP to allow unsafe-eval for WebAssembly
  if [[ -f "/etc/nginx/sites-available/hauling-qr-system" ]]; then
    # Backup current config
    cp /etc/nginx/sites-available/hauling-qr-system /etc/nginx/sites-available/hauling-qr-system.backup.csp
    
    # Update CSP to support WebAssembly
    sed -i "s|Content-Security-Policy.*|Content-Security-Policy \"default-src 'self' http: https: ws: wss: data: blob: 'unsafe-inline' 'unsafe-eval'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; worker-src 'self' blob:; connect-src 'self' http: https: ws: wss: https://api.truckhaul.top wss://api.truckhaul.top https://truckhaul.top;\" always;|" /etc/nginx/sites-available/hauling-qr-system
    
    # Test and reload Nginx
    if nginx -t >/dev/null 2>&1; then
      systemctl reload nginx
      log_success "✅ CSP updated to support WebAssembly for QR scanner"
      log_info "📷 Camera QR scanning should now work properly"
    else
      log_error "❌ Nginx config error, restoring backup"
      cp /etc/nginx/sites-available/hauling-qr-system.backup.csp /etc/nginx/sites-available/hauling-qr-system
    fi
  else
    log_warning "⚠️ Nginx config file not found"
  fi
}

# =============================================================================
# HEALTH CHECKS
# =============================================================================
run_health_checks() {
  log_info "🏥 Running health checks..."
  
  local health_check_passed=true
  
  # Check Nginx
  if sudo systemctl is-active --quiet nginx; then
    log_success "✅ Nginx is running"
  else
    log_error "❌ Nginx is not running"
    health_check_passed=false
  fi
  
  # Check PostgreSQL
  if sudo systemctl is-active --quiet postgresql; then
    log_success "✅ PostgreSQL is running"
  else
    log_error "❌ PostgreSQL is not running"
    health_check_passed=false
  fi
  
  # Check PM2 application
  if pm2 list | grep -q "hauling-qr-server.*online"; then
    log_success "✅ Application is running via PM2"
  else
    log_error "❌ Application is not running"
    health_check_passed=false
  fi
  
  # Check application response
  sleep 5
  if curl -f -s "http://localhost:${SERVER_PORT}/health" > /dev/null 2>&1; then
    log_success "✅ Application health endpoint responding"
  else
    log_warning "⚠️ Application health endpoint not responding (may be normal)"
  fi
  
  # Check if ports are listening (try multiple methods)
  local port_check_cmd=""
  if command -v netstat &> /dev/null; then
    port_check_cmd="netstat -tlnp"
  elif command -v ss &> /dev/null; then
    port_check_cmd="ss -tlnp"
  else
    log_warning "⚠️ Neither netstat nor ss available for port checking"
  fi
  
  if [[ -n "$port_check_cmd" ]]; then
    if $port_check_cmd | grep -q ":${SERVER_PORT}"; then
      log_success "✅ Server port ${SERVER_PORT} is listening"
    else
      log_error "❌ Server port ${SERVER_PORT} is not listening"
      health_check_passed=false
    fi
    
    if $port_check_cmd | grep -q ":80"; then
      log_success "✅ Nginx port 80 is listening"
    else
      log_error "❌ Nginx port 80 is not listening"
      health_check_passed=false
    fi
  fi
  
  return $($health_check_passed && echo 0 || echo 1)
}

# =============================================================================
# CORS LOGGING SUPPRESSION FIX
# =============================================================================
apply_cors_logging_fix() {
  log_info "🔇 Applying CORS logging suppression fix..."
  
  local env_file="$APP_DIR/.env"
  
  if [[ ! -f "$env_file" ]]; then
    log_warning "⚠️ .env file not found, skipping CORS logging fix"
    return 0
  fi
  
  # Create backup of .env file
  cp "$env_file" "$env_file.backup.cors.$(date +%Y%m%d_%H%M%S)"
  log_info "📄 Backed up .env file"
  
  # Add or update CORS logging settings to suppress verbose output
  if grep -q "DEV_ENABLE_CORS_LOGGING" "$env_file"; then
    # Update existing setting
    sed -i 's/DEV_ENABLE_CORS_LOGGING=.*/DEV_ENABLE_CORS_LOGGING=false/' "$env_file"
    log_info "✅ Updated existing CORS logging setting to false"
  else
    # Add new setting
    echo "" >> "$env_file"
    echo "# CORS Logging Configuration - Suppress verbose CORS messages" >> "$env_file"
    echo "DEV_ENABLE_CORS_LOGGING=false" >> "$env_file"
    log_info "✅ Added CORS logging suppression setting"
  fi
  
  # Add additional CORS logging control
  if grep -q "CORS_LOGGING_ENABLED" "$env_file"; then
    sed -i 's/CORS_LOGGING_ENABLED=.*/CORS_LOGGING_ENABLED=false/' "$env_file"
  else
    echo "CORS_LOGGING_ENABLED=false" >> "$env_file"
  fi
  
  # Set appropriate log level for production to reduce verbose output
  if grep -q "LOG_LEVEL" "$env_file"; then
    # Update to warn level to reduce verbose logging while keeping important messages
    sed -i 's/LOG_LEVEL=.*/LOG_LEVEL=warn/' "$env_file"
    log_info "✅ Updated log level to 'warn' for production"
  else
    echo "LOG_LEVEL=warn" >> "$env_file"
    log_info "✅ Set log level to 'warn' for production"
  fi
  
  # Add server-side CORS logging suppression flag
  if ! grep -q "SUPPRESS_CORS_CONFIG_MESSAGES" "$env_file"; then
    echo "SUPPRESS_CORS_CONFIG_MESSAGES=true" >> "$env_file"
    log_info "✅ Added server-side CORS message suppression"
  fi
  
  log_success "✅ CORS logging suppression fix applied successfully"
  log_info "📋 Changes made:"
  log_info "   • DEV_ENABLE_CORS_LOGGING=false (reduces CORS messages)"
  log_info "   • CORS_LOGGING_ENABLED=false (additional suppression)"
  log_info "   • LOG_LEVEL=warn (reduces verbose logging)"
  log_info "   • SUPPRESS_CORS_CONFIG_MESSAGES=true (server-side suppression)"
}

# =============================================================================
# PRODUCTION CONFIGURATION PATCHES
# =============================================================================
apply_production_patches() {
  log_info "Applying production configuration patches..."

  # Apply CORS logging suppression fix first
  apply_cors_logging_fix

  local server_file="$APP_DIR/server/server.js"
  if [[ -f "$server_file" ]]; then
    # Create backup
    cp "$server_file" "$server_file.backup"
    
    log_info "Applying comprehensive CORS fix for VPS IP access..."
    
    # Method 1: Replace any hardcoded domain with flexible CORS (but keep domain support)
    sed -i "s|https://hauling-qr.com|http://${DETECTED_VPS_IP}|g" "$server_file"
    # Don't replace truckhaul.top - we want to keep domain support
    
    # Method 2: Find and replace CORS origin patterns
    sed -i "s|'Access-Control-Allow-Origin', \`https://\${productionDomain}\`|'Access-Control-Allow-Origin', origin \|\| \`http://${DETECTED_VPS_IP}\`|g" "$server_file"
    
    # Method 3: Add a comprehensive CORS fix with logging suppression at the beginning of the file
    cat > /tmp/cors_fix.js << EOF
// CORS Fix for API Subdomain and Domain Access - Added by deployment script
app.use((req, res, next) => {
  const origin = req.headers.origin;
  const vpsIP = '${DETECTED_VPS_IP}';
  const domain = '${PRODUCTION_DOMAIN}';
  
  // Check if CORS logging is suppressed via environment variables
  const suppressCorsLogging = process.env.DEV_ENABLE_CORS_LOGGING === 'false' || 
                             process.env.CORS_LOGGING_ENABLED === 'false' ||
                             process.env.SUPPRESS_CORS_CONFIG_MESSAGES === 'true' ||
                             process.env.NODE_ENV === 'production';
  
  // Allow comprehensive origin access including API subdomain
  if (origin && (
    origin.includes(vpsIP) ||
    origin.includes(domain) ||
    origin.includes('truckhaul.top') ||
    origin.includes('api.truckhaul.top') ||
    origin.includes('localhost') ||
    origin.includes('127.0.0.1')
  )) {
    res.header('Access-Control-Allow-Origin', origin);
    res.header('Access-Control-Allow-Credentials', 'true');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS, PATCH, HEAD');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control, Pragma, X-CSRF-Token');
    res.header('Access-Control-Expose-Headers', 'Authorization, Content-Length, X-Requested-With');
    
    // Only log CORS messages if logging is not suppressed
    if (!suppressCorsLogging) {
      console.log(\`🔓 CORS: Allowing origin: \${origin}\`);
    }
  } else {
    // Fallback: allow main domain and API subdomain
    const allowedOrigin = origin && origin.includes('truckhaul.top') ? origin : 'https://truckhaul.top';
    res.header('Access-Control-Allow-Origin', allowedOrigin);
    res.header('Access-Control-Allow-Credentials', 'true');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS, PATCH, HEAD');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control, Pragma, X-CSRF-Token');
    
    // Only log CORS messages if logging is not suppressed
    if (!suppressCorsLogging) {
      console.log(\`🔓 CORS: Using fallback origin: \${allowedOrigin}\`);
    }
  }
  
  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
  } else {
    next();
  }
});

EOF
    
    # Insert the CORS fix after the app initialization
    sed -i '/const app = express();/r /tmp/cors_fix.js' "$server_file"
    rm /tmp/cors_fix.js
    
    log_success "Comprehensive CORS fix applied"
  else
    log_warning "server.js not found, CORS configuration not updated"
  fi

  # Fix 2: Disable rate limiting middleware
  log_info "Disabling rate limiting middleware..."
  if [[ -f "$server_file" ]]; then
    sed -i 's/^app\.use.*limiter.*$/\/\/ &/' "$server_file"
    sed -i 's/^app\.use.*authLimiter.*$/\/\/ &/' "$server_file"
    log_success "Rate limiting middleware disabled"
  fi

  # Fix 3: Fix requireRole import error
  log_info "Fixing requireRole import error..."
  local log_mgmt_file="$APP_DIR/server/routes/log-management.js"
  if [[ -f "$log_mgmt_file" ]]; then
    cp "$log_mgmt_file" "$log_mgmt_file.backup"
    sed -i "s/const { authenticateToken, requireRole } = require('..\/middleware\/auth');/const authenticateToken = require('..\/middleware\/auth');\nconst requireAdmin = require('..\/middleware\/admin');/" "$log_mgmt_file"
    sed -i "s/requireRole(\['admin'\])/requireAdmin/g" "$log_mgmt_file"
    log_success "requireRole import error fixed"
  fi
}

# =============================================================================
# POST-DEPLOYMENT FIXES AND VERIFICATION
# =============================================================================
post_deployment_fixes() {
  log_info "🔧 Running post-deployment fixes and verification..."
  
  cd "${APP_DIR}"
  
  # Final check and fix for pg module
  if [[ -d "database" ]]; then
    log_info "Final verification of database connectivity..."
    
    if ! node -e "require('pg'); console.log('pg module working')" 2>/dev/null; then
      log_warning "pg module still not working, applying final fix..."
      npm cache clean --force
      npm install pg --save --force
      npm rebuild pg 2>/dev/null || true
    fi
    
    # Try to run migrations one more time if they failed earlier
    if [[ -f "database/run-migration.js" ]] && [[ -f ".env" ]]; then
      DB_PASSWORD=$(grep "^DB_PASSWORD=" .env | cut -d'=' -f2)
      DB_USER=$(grep "^DB_USER=" .env | cut -d'=' -f2 || echo "hauling_qr_user")
      DB_NAME=$(grep "^DB_NAME=" .env | cut -d'=' -f2 || echo "hauling_qr_db")
      
      export DATABASE_URL="postgresql://${DB_USER}:${DB_PASSWORD}@localhost:5432/${DB_NAME}"
      
      if node database/run-migration.js 2>/dev/null; then
        log_success "✅ Database migrations completed successfully"
      else
        log_warning "⚠️ Database migrations still failing - may need manual intervention"
      fi
    fi
  fi
  
  # Ensure services are running
  log_info "Ensuring all services are running..."
  
  # Restart Nginx
  sudo systemctl restart nginx
  sudo systemctl enable nginx
  
  # Restart PM2 application
  if pm2 list | grep -q "hauling-qr-server"; then
    pm2 restart hauling-qr-server
  else
    pm2 start ecosystem.config.js
  fi
  
  # Wait a moment for services to start
  sleep 5
  
  log_success "✅ Post-deployment fixes completed"
}

# =============================================================================
# MAIN DEPLOYMENT FUNCTION
# =============================================================================
main() {
  log_info "🚀 Starting Hauling QR Trip System Enhanced Deployment v${VERSION}"
  
  # Check if running as root (like original auto-deploy.sh)
  if [[ ${EUID:-$(id -u)} -ne 0 ]]; then
    log_error "This script must be run as root (use sudo -E to preserve environment variables)"
    log_info "Usage: sudo -E ./auto-deploy-complete-fixed.sh"
    log_info "The -E flag preserves your GITHUB_PAT environment variable"
    exit 1
  fi
  
  # Debug: Show environment variables
  log_info "Debug: Environment variables:"
  log_info "  - GITHUB_PAT length: ${#GITHUB_PAT}"
  log_info "  - GITHUB_USERNAME: ${GITHUB_USERNAME:-'not set'}"
  
  if ! detect_vps_ip; then
    log_error "❌ IP detection failed"
    exit 1
  fi
  
  install_dependencies
  configure_timezone
  fetch_repository
  ensure_env_file
  verify_and_install_node_dependencies
  apply_production_patches
  setup_database
  build_application
  write_pm2_ecosystem
  ensure_app_dirs
  configure_nginx
  configure_pm2
  restart_services_after_patches
  fix_csp_for_webassembly
  
  if run_health_checks; then
    # Run CORS logging verification
    log_info "🔍 Running post-deployment CORS fix verification..."
    if [[ -f "maintenance/verify-deployment-cors-fix.sh" ]]; then
      cd "$APP_DIR"
      if ./maintenance/verify-deployment-cors-fix.sh; then
        log_success "✅ CORS logging suppression verified successfully"
      else
        log_warning "⚠️ CORS logging verification completed with warnings"
      fi
    else
      log_info "CORS verification script not found, skipping verification"
    fi
    
    log_success "🎉 Deployment completed successfully!"
    echo ""
    echo -e "${CYAN}╔══════════════════════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║                    HAULING QR TRIP SYSTEM DEPLOYMENT SUMMARY                ║${NC}"
    echo -e "${CYAN}╠══════════════════════════════════════════════════════════════════════════════╣${NC}"
    echo -e "${CYAN}║${NC} ${GREEN}✅ Deployment Status:${NC} ${YELLOW}SUCCESSFUL${NC}                                        ${CYAN}║${NC}"
    echo -e "${CYAN}║${NC} ${GREEN}🔍 Detected VPS IP:${NC} ${YELLOW}${DETECTED_VPS_IP}${NC}                                    ${CYAN}║${NC}"
    echo -e "${CYAN}║${NC} ${GREEN}🌐 Production Domain:${NC} ${YELLOW}${PRODUCTION_DOMAIN}${NC}                              ${CYAN}║${NC}"
    echo -e "${CYAN}║${NC} ${GREEN}🔇 CORS Logging:${NC} ${YELLOW}SUPPRESSED${NC} (reduces PM2 log spam)                    ${CYAN}║${NC}"
    echo -e "${CYAN}╠══════════════════════════════════════════════════════════════════════════════╣${NC}"
    echo -e "${CYAN}║${NC} ${GREEN}🔧 MANAGEMENT COMMANDS:${NC}                                                  ${CYAN}║${NC}"
    echo -e "${CYAN}║${NC}   • View logs: ${YELLOW}pm2 logs hauling-qr-server${NC}                                ${CYAN}║${NC}"
    echo -e "${CYAN}║${NC}   • Restart app: ${YELLOW}pm2 restart hauling-qr-server${NC}                           ${CYAN}║${NC}"
    echo -e "${CYAN}║${NC}   • Check status: ${YELLOW}pm2 status${NC}                                             ${CYAN}║${NC}"
    echo -e "${CYAN}╠══════════════════════════════════════════════════════════════════════════════╣${NC}"
    echo -e "${CYAN}║${NC} ${BLUE}📋 NEXT STEPS:${NC}                                                           ${CYAN}║${NC}"
    echo -e "${CYAN}║${NC}   1. Configure DNS: Point ${PRODUCTION_DOMAIN} to ${DETECTED_VPS_IP}                ${CYAN}║${NC}"
    echo -e "${CYAN}║${NC}   2. Test application: Visit http://${DETECTED_VPS_IP}                              ${CYAN}║${NC}"
    echo -e "${CYAN}║${NC}   3. Monitor logs: pm2 logs hauling-qr-server (should be cleaner now)      ${CYAN}║${NC}"
    echo -e "${CYAN}╚══════════════════════════════════════════════════════════════════════════════╝${NC}"
    echo ""
  else
    log_error "❌ Health checks failed"
    exit 1
  fi
}

# =============================================================================
# ENTRY POINT
# =============================================================================
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
  main "$@"
fi