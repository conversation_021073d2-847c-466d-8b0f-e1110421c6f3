/**
 * Dynamic Assignment Adaptation API Routes
 * 
 * This module provides API endpoints for dynamic assignment adaptation features.
 * It allows administrators to analyze truck patterns, create adaptive assignments,
 * and manage the dynamic assignment system.
 */

const express = require('express');
const router = express.Router();
const { getClient } = require('../config/database');
const auth = require('../middleware/auth');
const Joi = require('joi');
const { 
  dynamicAssignmentAdapter, 
  ADAPTATION_STRATEGIES, 
  CONFIDENCE_LEVELS 
} = require('../utils/dynamic-assignment-adapter');
const { logger } = require('../utils/logger');

// Validation schemas
const analyzePatternSchema = Joi.object({
  truck_number: Joi.string().required(),
  current_location_id: Joi.number().integer().required(),
  analysis_days: Joi.number().integer().min(1).max(90).default(30)
});

const createAdaptiveAssignmentSchema = Joi.object({
  truck_id: Joi.number().integer().required(),
  driver_id: Joi.number().integer().required(),
  loading_location_id: Joi.number().integer().required(),
  unloading_location_id: Joi.number().integer().required(),
  strategy: Joi.string().valid(...Object.values(ADAPTATION_STRATEGIES)).default(ADAPTATION_STRATEGIES.PATTERN_BASED),
  confidence: Joi.string().valid(...Object.values(CONFIDENCE_LEVELS)).default(CONFIDENCE_LEVELS.MEDIUM),
  metadata: Joi.object().default({})
});

/**
 * @route   POST /api/dynamic-assignments/analyze-patterns
 * @desc    Analyze truck movement patterns for adaptive assignment suggestions
 * @access  Private (Admin/Supervisor only)
 */
router.post('/analyze-patterns', auth, async (req, res) => {
  try {
    // Check permissions
    if (!['admin', 'supervisor'].includes(req.user.role)) {
      return res.status(403).json({
        error: 'Access Denied',
        message: 'Only administrators and supervisors can analyze movement patterns.'
      });
    }

    // Validate request
    const { error, value } = analyzePatternSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        error: 'Validation Error',
        message: error.details[0].message
      });
    }

    const { truck_number, current_location_id, analysis_days } = value;

    // Perform pattern analysis
    const analysisResult = await dynamicAssignmentAdapter.analyzeMovementPatterns(
      truck_number,
      current_location_id,
      { analysisDays: analysis_days }
    );

    logger.info('Pattern analysis completed', {
      user_id: req.user.id,
      truck_number,
      current_location_id,
      suggestions_count: analysisResult.suggestions.length
    });

    res.json({
      success: true,
      data: analysisResult
    });

  } catch (error) {
    logger.error('Pattern analysis failed', {
      user_id: req.user.id,
      error: error.message
    });
    
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to analyze movement patterns'
    });
  }
});

/**
 * @route   POST /api/dynamic-assignments/create-adaptive
 * @desc    Create an adaptive assignment based on analysis
 * @access  Private (Admin only)
 */
router.post('/create-adaptive', auth, async (req, res) => {
  try {
    // Check permissions
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        error: 'Access Denied',
        message: 'Only administrators can create adaptive assignments.'
      });
    }

    // Validate request
    const { error, value } = createAdaptiveAssignmentSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        error: 'Validation Error',
        message: error.details[0].message
      });
    }

    // Add user metadata
    value.metadata.created_by_user_id = req.user.id;
    value.metadata.created_by_username = req.user.username;
    value.metadata.created_at = new Date().toISOString();

    // Create adaptive assignment
    const assignmentResult = await dynamicAssignmentAdapter.createAdaptiveAssignment(value);

    logger.info('Adaptive assignment created', {
      user_id: req.user.id,
      assignment_id: assignmentResult.assignment.id,
      strategy: value.strategy,
      confidence: value.confidence
    });

    res.json({
      success: true,
      data: assignmentResult
    });

  } catch (error) {
    logger.error('Adaptive assignment creation failed', {
      user_id: req.user.id,
      error: error.message
    });
    
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to create adaptive assignment'
    });
  }
});

/**
 * @route   GET /api/dynamic-assignments/adaptive-assignments
 * @desc    Get list of adaptive assignments with their performance metrics
 * @access  Private (Admin/Supervisor only)
 */
router.get('/adaptive-assignments', auth, async (req, res) => {
  try {
    // Check permissions
    if (!['admin', 'supervisor'].includes(req.user.role)) {
      return res.status(403).json({
        error: 'Access Denied',
        message: 'Only administrators and supervisors can view adaptive assignments.'
      });
    }

    const { status, strategy, confidence, limit = 50, offset = 0 } = req.query;

    const client = await getClient();

    try {
      // Build query with filters
      let whereClause = 'WHERE a.is_adaptive = true';
      const params = [];
      let paramCount = 0;

      if (status) {
        whereClause += ` AND a.status = $${++paramCount}`;
        params.push(status);
      }

      if (strategy) {
        whereClause += ` AND a.adaptation_strategy = $${++paramCount}`;
        params.push(strategy);
      }

      if (confidence) {
        whereClause += ` AND a.adaptation_confidence = $${++paramCount}`;
        params.push(confidence);
      }

      // Add pagination
      whereClause += ` ORDER BY a.created_at DESC LIMIT $${++paramCount} OFFSET $${++paramCount}`;
      params.push(parseInt(limit), parseInt(offset));

      const adaptiveAssignmentsQuery = `
        SELECT 
          a.id,
          a.assignment_code,
          a.status,
          a.adaptation_strategy,
          a.adaptation_confidence,
          a.adaptation_metadata,
          a.created_at,
          dt.truck_number,
          d.full_name as driver_name,
          ll.name as loading_location,
          ul.name as unloading_location,
          COUNT(tl.id) as total_trips,
          COUNT(CASE WHEN tl.status = 'trip_completed' THEN 1 END) as completed_trips
        FROM assignments a
        JOIN dump_trucks dt ON a.truck_id = dt.id
        JOIN drivers d ON a.driver_id = d.id
        LEFT JOIN locations ll ON a.loading_location_id = ll.id
        LEFT JOIN locations ul ON a.unloading_location_id = ul.id
        LEFT JOIN trip_logs tl ON a.id = tl.assignment_id
        ${whereClause}
        GROUP BY a.id, dt.truck_number, d.full_name, ll.name, ul.name
      `;

      const result = await client.query(adaptiveAssignmentsQuery, params);

      // Get total count for pagination
      const countQuery = `
        SELECT COUNT(*) as total
        FROM assignments a
        ${whereClause.replace(/ORDER BY.*$/, '').replace(/LIMIT.*$/, '')}
      `;
      const countResult = await client.query(countQuery, params.slice(0, -2));

      res.json({
        success: true,
        data: result.rows,
        pagination: {
          total: parseInt(countResult.rows[0].total),
          limit: parseInt(limit),
          offset: parseInt(offset),
          hasMore: parseInt(offset) + parseInt(limit) < parseInt(countResult.rows[0].total)
        }
      });

    } finally {
      client.release();
    }

  } catch (error) {
    logger.error('Failed to fetch adaptive assignments', {
      user_id: req.user.id,
      error: error.message
    });
    
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to fetch adaptive assignments'
    });
  }
});

/**
 * @route   PUT /api/dynamic-assignments/:id/update-adaptive
 * @desc    Update an assignment with adaptive insights
 * @access  Private (Admin only)
 */
router.put('/:id/update-adaptive', auth, async (req, res) => {
  try {
    // Check permissions
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        error: 'Access Denied',
        message: 'Only administrators can update adaptive assignments.'
      });
    }

    const assignmentId = parseInt(req.params.id);
    const adaptations = req.body;

    // Add user metadata to adaptations
    adaptations.updated_by_user_id = req.user.id;
    adaptations.updated_by_username = req.user.username;
    adaptations.updated_at = new Date().toISOString();

    // Update assignment adaptively
    const updateResult = await dynamicAssignmentAdapter.updateAssignmentAdaptively(
      assignmentId,
      adaptations
    );

    logger.info('Assignment updated adaptively', {
      user_id: req.user.id,
      assignment_id: assignmentId,
      adaptations: Object.keys(updateResult.adaptations || {})
    });

    res.json({
      success: true,
      data: updateResult
    });

  } catch (error) {
    logger.error('Adaptive assignment update failed', {
      user_id: req.user.id,
      assignment_id: req.params.id,
      error: error.message
    });
    
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to update assignment adaptively'
    });
  }
});

/**
 * @route   GET /api/dynamic-assignments/adaptation-metrics
 * @desc    Get metrics and analytics for dynamic assignment adaptation
 * @access  Private (Admin/Supervisor only)
 */
router.get('/adaptation-metrics', auth, async (req, res) => {
  try {
    // Check permissions
    if (!['admin', 'supervisor'].includes(req.user.role)) {
      return res.status(403).json({
        error: 'Access Denied',
        message: 'Only administrators and supervisors can view adaptation metrics.'
      });
    }

    const client = await getClient();

    try {
      // Get adaptation metrics
      const metricsQuery = `
        SELECT 
          COUNT(*) as total_adaptive_assignments,
          COUNT(CASE WHEN status = 'assigned' THEN 1 END) as active_adaptive_assignments,
          COUNT(CASE WHEN adaptation_strategy = 'pattern_based' THEN 1 END) as pattern_based_count,
          COUNT(CASE WHEN adaptation_strategy = 'proximity_based' THEN 1 END) as proximity_based_count,
          COUNT(CASE WHEN adaptation_strategy = 'efficiency_based' THEN 1 END) as efficiency_based_count,
          COUNT(CASE WHEN adaptation_confidence = 'high' THEN 1 END) as high_confidence_count,
          COUNT(CASE WHEN adaptation_confidence = 'medium' THEN 1 END) as medium_confidence_count,
          COUNT(CASE WHEN adaptation_confidence = 'low' THEN 1 END) as low_confidence_count,
          AVG(CASE WHEN created_at >= CURRENT_DATE - INTERVAL '7 days' THEN 1 ELSE 0 END) as weekly_creation_rate
        FROM assignments 
        WHERE is_adaptive = true
      `;

      const metricsResult = await client.query(metricsQuery);
      const metrics = metricsResult.rows[0];

      // Get performance comparison
      const performanceQuery = `
        SELECT 
          a.is_adaptive,
          COUNT(tl.id) as total_trips,
          COUNT(CASE WHEN tl.status = 'trip_completed' THEN 1 END) as completed_trips,
          AVG(CASE WHEN tl.status = 'trip_completed' AND tl.total_duration_minutes IS NOT NULL 
              THEN tl.total_duration_minutes END) as avg_trip_duration
        FROM assignments a
        LEFT JOIN trip_logs tl ON a.id = tl.assignment_id
        WHERE a.created_at >= CURRENT_DATE - INTERVAL '30 days'
        GROUP BY a.is_adaptive
      `;

      const performanceResult = await client.query(performanceQuery);

      res.json({
        success: true,
        data: {
          metrics: {
            ...metrics,
            total_adaptive_assignments: parseInt(metrics.total_adaptive_assignments),
            active_adaptive_assignments: parseInt(metrics.active_adaptive_assignments),
            pattern_based_count: parseInt(metrics.pattern_based_count),
            proximity_based_count: parseInt(metrics.proximity_based_count),
            efficiency_based_count: parseInt(metrics.efficiency_based_count),
            high_confidence_count: parseInt(metrics.high_confidence_count),
            medium_confidence_count: parseInt(metrics.medium_confidence_count),
            low_confidence_count: parseInt(metrics.low_confidence_count)
          },
          performance: performanceResult.rows
        }
      });

    } finally {
      client.release();
    }

  } catch (error) {
    logger.error('Failed to fetch adaptation metrics', {
      user_id: req.user.id,
      error: error.message
    });
    
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to fetch adaptation metrics'
    });
  }
});

module.exports = router;
