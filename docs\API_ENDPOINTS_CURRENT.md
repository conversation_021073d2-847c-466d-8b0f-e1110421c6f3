# API Endpoints - Current System

## 📋 Overview

The Hauling QR Trip Management System provides a comprehensive REST API with WebSocket support for real-time updates. All endpoints are secured with JWT authentication unless otherwise specified.

**Base URL**: `http://localhost:5000/api` (development) or `https://api.truckhaul.top/api` (production)
**WebSocket**: `ws://localhost:5000` (development) or `wss://api.truckhaul.top/ws` (production)

## 🔐 Authentication

### JWT Authentication
All API endpoints (except public ones) require JWT authentication via the `Authorization` header:

```bash
Authorization: Bearer <jwt_token>
```

### Public Endpoints (No Authentication Required)
- `POST /api/auth/login` - User login
- `GET /api/driver-connect/*` - Driver QR check-in system (public access)

## 📊 Core API Endpoints

### Authentication & Users
```bash
# Authentication
POST   /api/auth/login              # User login
POST   /api/auth/logout             # User logout
GET    /api/auth/verify             # Verify JWT token
POST   /api/auth/refresh            # Refresh JWT token

# User Management
GET    /api/users                   # Get all users
POST   /api/users                   # Create new user
GET    /api/users/:id               # Get user by ID
PUT    /api/users/:id               # Update user
DELETE /api/users/:id               # Delete user
```

### Trip Management
```bash
# Trip Operations
GET    /api/trips                   # Get all trips with filtering
POST   /api/trips                   # Create new trip
GET    /api/trips/:id               # Get trip by ID
PUT    /api/trips/:id               # Update trip
DELETE /api/trips/:id               # Delete trip
POST   /api/trips/:id/complete      # Complete trip
POST   /api/trips/:id/verify        # Verify completed trip

# Trip Logs
GET    /api/trip-logs               # Get trip logs with filtering
POST   /api/trip-logs               # Create trip log entry
GET    /api/trip-logs/:id           # Get trip log by ID
PUT    /api/trip-logs/:id           # Update trip log
```

### Assignment Management
```bash
# Assignments
GET    /api/assignments             # Get all assignments
POST   /api/assignments             # Create new assignment
GET    /api/assignments/:id         # Get assignment by ID
PUT    /api/assignments/:id         # Update assignment
DELETE /api/assignments/:id         # Delete assignment
POST   /api/assignments/:id/activate # Activate assignment
```

### Driver & Shift Management
```bash
# Drivers
GET    /api/drivers                 # Get all drivers
POST   /api/drivers                 # Create new driver
GET    /api/drivers/:id             # Get driver by ID
PUT    /api/drivers/:id             # Update driver
DELETE /api/drivers/:id             # Delete driver

# Driver Shifts
GET    /api/driver-shifts           # Get driver shifts
POST   /api/driver-shifts           # Create driver shift
GET    /api/driver-shifts/:id       # Get shift by ID
PUT    /api/driver-shifts/:id       # Update shift
POST   /api/driver-shifts/:id/complete # Complete shift
POST   /api/driver-shifts/:id/cancel   # Cancel shift

# Manual Shift Management (Admin)
GET    /api/manual-shift-management/active     # Get active shifts
GET    /api/manual-shift-management/scheduled  # Get scheduled shifts
POST   /api/manual-shift-management/complete/:id # Complete shift manually
POST   /api/manual-shift-management/cancel/:id   # Cancel shift manually
GET    /api/manual-shift-management/summary     # Get shift summary
POST   /api/manual-shift-management/refresh     # Refresh shift statuses
```

### Driver QR Code System (Public Access)
```bash
# Driver Check-in/Check-out (No authentication required)
POST   /api/driver-connect/scan-driver    # Scan driver QR code
POST   /api/driver-connect/scan-truck     # Scan truck QR code for check-in/out
GET    /api/driver-connect/status/:driverId # Get driver current status

# Driver QR Management (Admin)
GET    /api/driver-qr                     # Get all driver QR codes
POST   /api/driver-qr/generate/:driverId  # Generate QR code for driver
GET    /api/driver-qr/:driverId           # Get driver QR code
DELETE /api/driver-qr/:driverId           # Delete driver QR code
```

### Truck & Location Management
```bash
# Trucks
GET    /api/trucks                  # Get all trucks
POST   /api/trucks                  # Create new truck
GET    /api/trucks/:id              # Get truck by ID
PUT    /api/trucks/:id              # Update truck
DELETE /api/trucks/:id              # Delete truck

# Locations
GET    /api/locations               # Get all locations
POST   /api/locations               # Create new location
GET    /api/locations/:id           # Get location by ID
PUT    /api/locations/:id           # Update location
DELETE /api/locations/:id           # Delete location
```

### QR Code Operations
```bash
# QR Code Generation
POST   /api/qr/generate/truck/:id   # Generate truck QR code
POST   /api/qr/generate/location/:id # Generate location QR code
POST   /api/qr/generate/driver/:id  # Generate driver QR code

# QR Code Scanning
POST   /api/qr/scan                 # Process QR code scan
POST   /api/qr/validate             # Validate QR code format
```

### Analytics & Reporting
```bash
# Dashboard Analytics
GET    /api/analytics/dashboard     # Get dashboard metrics
GET    /api/analytics/trips         # Get trip analytics
GET    /api/analytics/drivers       # Get driver performance
GET    /api/analytics/trucks        # Get truck utilization
GET    /api/analytics/locations     # Get location statistics

# Reports
GET    /api/reports/daily           # Daily activity report
GET    /api/reports/weekly          # Weekly summary report
GET    /api/reports/monthly         # Monthly performance report
POST   /api/reports/custom          # Generate custom report
```

### System Health & Monitoring
```bash
# System Health
GET    /api/health                  # System health check
GET    /api/health/database         # Database health check
GET    /api/health/services         # Service status check

# System Health Monitor (Admin)
GET    /api/system-health/status    # Get all module health status
POST   /api/system-health/fix-shifts # Fix shift management issues
POST   /api/system-health/fix-assignments # Fix assignment issues
POST   /api/system-health/fix-trips # Fix trip monitoring issues
POST   /api/system-health/fix-database # Fix database issues
```

### Fleet Resource Monitor
```bash
# Resource Summary
GET    /api/fleet-resources/summary           # Get driver and truck resource counts
GET    /api/fleet-resources/loading-locations # Get truck assignments by loading location
GET    /api/fleet-resources/utilization-history # Get historical utilization trends
GET    /api/fleet-resources/export            # Export resource utilization report

# Real-time Resource Updates (WebSocket)
# Events: resource-updated, truck-status-changed, driver-status-changed
```

### Role-Based Access Control (In Development)
```bash
# Role Management
GET    /api/roles                   # Get all user roles
POST   /api/roles                   # Create new role
DELETE /api/roles/:name             # Delete role (with safety checks)

# Permission Management
GET    /api/permissions             # Get all role-page permissions
POST   /api/permissions             # Bulk update permissions
GET    /api/permissions/:role       # Get permissions for specific role
```

## 🔌 WebSocket Events

### Real-time Updates
The system provides real-time updates via WebSocket connections:

```javascript
// Connection
const ws = new WebSocket('ws://localhost:5000');

// Event Types
ws.on('trip-updated', (data) => {
  // Trip status changed
});

ws.on('assignment-created', (data) => {
  // New assignment created
});

ws.on('driver-checked-in', (data) => {
  // Driver checked in via QR code
});

ws.on('shift-completed', (data) => {
  // Driver shift completed
});

ws.on('system-alert', (data) => {
  // System health alert
});
```

### WebSocket Authentication
WebSocket connections require JWT authentication:

```javascript
const ws = new WebSocket('ws://localhost:5000', {
  headers: {
    'Authorization': `Bearer ${jwt_token}`
  }
});
```

## 📝 Request/Response Examples

### Create Trip
```bash
POST /api/trips
Content-Type: application/json
Authorization: Bearer <jwt_token>

{
  "assignmentId": 123,
  "truckId": 456,
  "driverId": 789,
  "loadingLocationId": 1,
  "unloadingLocationId": 2,
  "scheduledStartTime": "2025-01-16T08:00:00Z",
  "notes": "Regular hauling trip"
}

# Response
{
  "success": true,
  "data": {
    "id": 1001,
    "status": "PENDING",
    "createdAt": "2025-01-16T07:30:00Z",
    "assignmentId": 123,
    "truckId": 456,
    "driverId": 789,
    "loadingLocationId": 1,
    "unloadingLocationId": 2,
    "scheduledStartTime": "2025-01-16T08:00:00Z",
    "notes": "Regular hauling trip"
  }
}
```

### Driver QR Check-in (Public)
```bash
POST /api/driver-connect/scan-driver
Content-Type: application/json

{
  "qrData": "driver_123_emp456_2025",
  "scanLocation": "yard_entrance"
}

# Response
{
  "success": true,
  "data": {
    "driverId": 123,
    "employeeId": "emp456",
    "driverName": "John Doe",
    "currentStatus": "available",
    "message": "Driver authenticated successfully"
  }
}
```

### System Health Check
```bash
GET /api/health
Authorization: Bearer <jwt_token>

# Response
{
  "success": true,
  "data": {
    "status": "healthy",
    "timestamp": "2025-01-16T10:30:00Z",
    "services": {
      "database": "operational",
      "websocket": "operational",
      "qr_scanner": "operational"
    },
    "metrics": {
      "activeTrips": 15,
      "activeShifts": 8,
      "systemUptime": "72h 15m"
    }
  }
}
```

## 🚨 Error Handling

### Standard Error Response
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": {
      "field": "truckId",
      "issue": "Truck ID is required"
    }
  },
  "timestamp": "2025-01-16T10:30:00Z"
}
```

### Common Error Codes
- `AUTHENTICATION_REQUIRED` - JWT token missing or invalid
- `AUTHORIZATION_FAILED` - Insufficient permissions
- `VALIDATION_ERROR` - Input validation failed
- `RESOURCE_NOT_FOUND` - Requested resource doesn't exist
- `CONFLICT_ERROR` - Resource conflict (e.g., duplicate entry)
- `SYSTEM_ERROR` - Internal server error
- `RATE_LIMIT_EXCEEDED` - Too many requests

## 🔧 Rate Limiting

### Current Limits
- **General API**: 10,000 requests per 15 minutes
- **Authentication**: 500 requests per 15 minutes
- **Production**: 20,000 requests per 15 minutes

### Rate Limit Headers
```bash
X-RateLimit-Limit: 10000
X-RateLimit-Remaining: 9995
X-RateLimit-Reset: 1642345200
```

## 📊 Filtering & Pagination

### Query Parameters
Most GET endpoints support filtering and pagination:

```bash
GET /api/trips?status=PENDING&page=1&limit=50&sortBy=createdAt&sortOrder=desc

# Parameters:
# - status: Filter by trip status
# - page: Page number (default: 1)
# - limit: Items per page (default: 50, max: 100)
# - sortBy: Sort field
# - sortOrder: asc or desc
# - dateFrom: Start date filter (ISO 8601)
# - dateTo: End date filter (ISO 8601)
```

### Response Format
```json
{
  "success": true,
  "data": {
    "items": [...],
    "pagination": {
      "page": 1,
      "limit": 50,
      "total": 150,
      "pages": 3,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

## 🔍 API Testing

### Using curl
```bash
# Login and get token
TOKEN=$(curl -s -X POST http://localhost:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}' \
  | jq -r '.data.token')

# Use token for authenticated requests
curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:5000/api/trips
```

### Using Postman
1. Import the API collection (if available)
2. Set up environment variables for base URL and token
3. Use the authentication endpoint to get JWT token
4. Add token to Authorization header for subsequent requests

## 📚 Additional Resources

- **OpenAPI/Swagger Documentation**: Available at `/api/docs` (if enabled)
- **WebSocket Testing**: Use tools like wscat or browser developer tools
- **Database Schema**: See `database/init.sql` for complete schema
- **Migration History**: Check `database/migrations/` for schema changes