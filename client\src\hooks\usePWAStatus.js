import { useState, useEffect, useCallback } from 'react';
import { useLocation } from 'react-router-dom';
import { backgroundSync } from '../services/backgroundSync';
// Removed tripScannerOffline import - TripScanner now operates online-only
import { driverConnectOffline } from '../services/driverConnectOffline';

/**
 * Custom hook for PWA status monitoring and sync management
 * Provides real-time status updates for offline functionality
 */
export const usePWAStatus = () => {
  const location = useLocation();
  
  // Network and sync states
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [syncStatus, setSyncStatus] = useState('synced'); // 'synced', 'pending', 'syncing', 'error'
  // Removed queuedScans - TripScanner now operates online-only
  const [queuedConnections, setQueuedConnections] = useState(0);
  
  // PWA detection
  const [isPWA, setIsPWA] = useState(false);
  const [lastSyncTime, setLastSyncTime] = useState(null);
  const [syncError, setSyncError] = useState(null);

  // PWA installation state
  const [installPrompt, setInstallPrompt] = useState(null);
  const [isInstalled, setIsInstalled] = useState(false);

  // Simplified PWA mode detection with debouncing to prevent infinite refresh loops
  let lastDetectionTime = 0;
  const DETECTION_COOLDOWN = 10000; // 10 seconds between detections

  const detectAndSendPWAMode = useCallback(() => {
    try {
      const now = Date.now();

      // Debounce detection to prevent excessive calls
      if (now - lastDetectionTime < DETECTION_COOLDOWN) {
        return isPWA; // Return cached value
      }

      lastDetectionTime = now;

      // Simplified PWA detection using only the most reliable method
      const standaloneMatch = window.matchMedia('(display-mode: standalone)').matches;
      const iOSStandalone = window.navigator.standalone === true;
      const isPWAMode = standaloneMatch || iOSStandalone;

      setIsPWA(isPWAMode);

      // Send PWA mode status to service worker only if it changed or on initial detection
      if (isPWAMode !== isPWA) {
        try {
          if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
            const message = {
              type: 'PWA_MODE_STATUS',
              isPWA: isPWAMode,
              timestamp: new Date().toISOString()
            };

            navigator.serviceWorker.controller.postMessage(message);
            console.log('[PWAStatus] PWA mode status updated:', isPWAMode);
          }
        } catch (communicationError) {
          console.error('[PWAStatus] Error communicating with service worker:', communicationError);
        }
      }

      return isPWAMode;
      
      return isPWAMode;
      
    } catch (error) {
      console.error('[PWAStatus] Critical error in detectAndSendPWAMode:', error);
      console.error('[PWAStatus] Error context:', {
        location: window.location.href,
        userAgent: navigator.userAgent.substring(0, 100) + '...',
        timestamp: new Date().toISOString()
      });
      
      // Fallback to basic detection in case of critical error
      try {
        const fallbackMode = window.matchMedia('(display-mode: standalone)').matches;
        setIsPWA(fallbackMode);
        return fallbackMode;
      } catch (fallbackError) {
        console.error('[PWAStatus] Even fallback detection failed:', fallbackError);
        setIsPWA(false);
        return false;
      }
    }
  }, []);

  // Simplified PWA mode detection - only run once on mount to prevent loops
  useEffect(() => {
    // Initial detection only
    detectAndSendPWAMode();

    // Only listen for display mode changes (most reliable indicator)
    const mediaQuery = window.matchMedia('(display-mode: standalone)');
    const handleDisplayModeChange = (e) => {
      console.log('[PWAStatus] Display mode changed:', e.matches ? 'standalone' : 'browser');
      detectAndSendPWAMode();
    };

    mediaQuery.addListener(handleDisplayModeChange);

    return () => {
      mediaQuery.removeListener(handleDisplayModeChange);
    };
  }, []); // Empty dependency array to run only once

  // Service worker message handler to respond to PWA mode requests
  useEffect(() => {
    const handleServiceWorkerMessage = (event) => {
      if (event.data?.type === 'REQUEST_PWA_MODE') {
        // Respond with current PWA mode status
        const currentPWAMode = detectAndSendPWAMode();

        if (navigator.serviceWorker.controller) {
          navigator.serviceWorker.controller.postMessage({
            type: 'PWA_MODE_RESPONSE',
            isPWA: currentPWAMode,
            timestamp: new Date().toISOString()
          });
        }
      }
    };

    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.addEventListener('message', handleServiceWorkerMessage);

      return () => {
        navigator.serviceWorker.removeEventListener('message', handleServiceWorkerMessage);
      };
    }
  }, [detectAndSendPWAMode]);



  // Enhanced sync error recovery function
  const recoverFromSyncError = useCallback(async () => {
    console.log('[PWA] Starting sync error recovery...');
    
    try {
      setSyncStatus('syncing');
      setSyncError(null);
      
      // Step 1: Try to recover failed connections
      const recoveryResults = await backgroundSync.recoverFailedConnections();
      console.log('[PWA] Recovery results:', recoveryResults);
      
      // Step 2: Clear any corrupted data
      const clearResults = await backgroundSync.clearCorruptedData();
      console.log('[PWA] Clear corrupted data results:', clearResults);
      
      // Step 3: Attempt sync again
      const syncResults = await backgroundSync.startSync();
      console.log('[PWA] Post-recovery sync results:', syncResults);
      
      // Check if recovery sync was successful using same logic as manual sync
      const driverConnectionsOk = syncResults && typeof syncResults.driverConnections === 'object';
      const referenceDataOk = syncResults && syncResults.referenceData &&
                              (syncResults.referenceData.summary?.successful >= 0 || syncResults.referenceData.summary?.skipped >= 0);

      if (driverConnectionsOk && referenceDataOk) {
        setSyncStatus('synced');
        setLastSyncTime(new Date().toISOString());

        // Update queue counts after successful recovery
        try {
          const connectionCount = await driverConnectOffline.getPendingCount();
          setQueuedConnections(connectionCount);
        } catch (countError) {
          console.error('Failed to update queue counts after recovery:', countError);
        }

        return {
          success: true,
          message: `Recovery successful. Synced ${syncResults.driverConnections?.synced || 0} items.`,
          recoveryResults,
          syncResults
        };
      } else {
        setSyncStatus('error');
        setSyncError('Recovery failed - sync still not working');
        
        return {
          success: false,
          message: 'Recovery failed - sync still not working',
          recoveryResults,
          syncResults
        };
      }
    } catch (error) {
      console.error('[PWA] Sync error recovery failed:', error);
      setSyncStatus('error');
      setSyncError(`Recovery failed: ${error.message}`);
      
      return {
        success: false,
        message: `Recovery failed: ${error.message}`,
        error
      };
    }
  }, []);

  // Trigger manual sync with enhanced error handling and PWA-specific support
  const triggerSync = useCallback(async () => {
    if (!navigator.onLine) {
      console.log('Cannot sync while offline');
      return { success: false, message: 'Cannot sync while offline' };
    }

    // Detect PWA mode for enhanced handling
    const isPWAMode = window.matchMedia('(display-mode: standalone)').matches ||
                      window.navigator.standalone === true ||
                      document.referrer.includes('android-app://');

    try {
      setSyncStatus('syncing');
      setSyncError(null);

      // Add PWA-specific delay for state stabilization
      if (isPWAMode) {
        console.log('[PWA] Adding PWA-specific delay for state stabilization');
        await new Promise(resolve => setTimeout(resolve, 300));
      }

      const results = await backgroundSync.startSync();

      // Check if sync was successful - consider it successful if:
      // 1. Driver connections sync completed (even if 0 items)
      // 2. Reference data was properly handled (success or skipped for Driver Connect PWA)
      const driverConnectionsOk = results && typeof results.driverConnections === 'object';
      const referenceDataOk = results && results.referenceData &&
                              (results.referenceData.summary?.successful >= 0 || results.referenceData.summary?.skipped >= 0);

      if (driverConnectionsOk && referenceDataOk) {
        // Add additional PWA-specific delay before setting success status
        if (isPWAMode) {
          console.log('[PWA] Adding PWA-specific delay before success status');
          await new Promise(resolve => setTimeout(resolve, 200));
        }

        setSyncStatus('synced');
        setLastSyncTime(new Date().toISOString());

        // Update queue counts directly after sync
        try {
          // Only check driver connections - TripScanner operates online-only
          const connectionCount = await driverConnectOffline.getPendingCount();
          setQueuedConnections(connectionCount);
        } catch (countError) {
          console.error('Failed to update queue counts after sync:', countError);
        }

        const totalSynced = (results.driverConnections?.synced || 0); // Only driver connections
        const referenceSkipped = results.referenceData?.summary?.skipped || 0;

        let message = `Synced ${totalSynced} items successfully`;
        if (referenceSkipped > 0) {
          message += ` (${referenceSkipped} reference data items skipped - normal for Driver Connect)`;
        }

        if (isPWAMode) {
          console.log('[PWA] Manual sync completed successfully in PWA mode:', { totalSynced, referenceSkipped });
        }

        return {
          success: true,
          message: message,
          results,
          pwaMode: isPWAMode
        };
      } else {
        // If sync failed, try recovery
        if (isPWAMode) {
          console.log('[PWA] Sync failed in PWA mode, attempting recovery...', { driverConnectionsOk, referenceDataOk, results });
        } else {
          console.log('[PWA] Sync failed, attempting recovery...', { driverConnectionsOk, referenceDataOk, results });
        }
        return await recoverFromSyncError();
      }
    } catch (error) {
      console.error('Manual sync failed:', error);

      // If sync threw an error, try recovery
      if (isPWAMode) {
        console.log('[PWA] Sync threw error in PWA mode, attempting recovery...', error);
      } else {
        console.log('[PWA] Sync threw error, attempting recovery...');
      }
      return await recoverFromSyncError();
    }
  }, [recoverFromSyncError]); // Add recoverFromSyncError dependency

  // Handle PWA installation
  const installPWA = useCallback(async () => {
    if (!installPrompt) {
      return { success: false, message: 'Installation not available' };
    }

    try {
      await installPrompt.prompt();
      const choiceResult = await installPrompt.userChoice;

      if (choiceResult.outcome === 'accepted') {
        setIsInstalled(true);
        setInstallPrompt(null);
        return { success: true, message: 'PWA installed successfully' };
      } else {
        return { success: false, message: 'Installation cancelled by user' };
      }
    } catch (error) {
      console.error('PWA installation failed:', error);
      return { success: false, message: error.message };
    }
  }, [installPrompt]);

  // Network status monitoring
  useEffect(() => {
    const handleOnline = async () => {
      setIsOnline(true);
      setSyncError(null);

      // AUTOMATIC SYNC DISABLED - Only manual sync via sync button is allowed
      // Check queue counts to update UI status when coming back online
      try {
        const connectionCount = await driverConnectOffline.getPendingCount();
        setQueuedConnections(connectionCount);

        if (connectionCount > 0) {
          // Set status to pending to indicate data is ready for manual sync
          setSyncStatus('pending');
          console.log(`[PWA] Back online with ${connectionCount} items ready for manual sync`);
        } else {
          setSyncStatus('synced');
        }
      } catch (error) {
        console.error('Failed to check queue counts on reconnect:', error);
      }
    };

    const handleOffline = () => {
      setIsOnline(false);
      setSyncStatus('pending');
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []); // Remove updateQueueCounts dependency to prevent infinite loop

  // PWA installation prompt handling
  useEffect(() => {
    const handleBeforeInstallPrompt = (e) => {
      e.preventDefault();
      setInstallPrompt(e);
    };

    const handleAppInstalled = () => {
      setIsInstalled(true);
      setInstallPrompt(null);
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    window.addEventListener('appinstalled', handleAppInstalled);

    // Check if already installed
    if (window.matchMedia && window.matchMedia('(display-mode: standalone)').matches) {
      setIsInstalled(true);
    }

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.removeEventListener('appinstalled', handleAppInstalled);
    };
  }, []);

  // Periodic queue count updates - check regularly to detect new offline data
  useEffect(() => {
    const updateCounts = async () => {
      try {
        // Only check driver connections - TripScanner operates online-only
        const connectionCount = await driverConnectOffline.getPendingCount();
        
        // Add debug logging to help troubleshoot sync button issues
        if (connectionCount > 0) {
          console.log(`[PWAStatus] Found ${connectionCount} queued connections, isOnline: ${isOnline}, isPWA: ${isPWA}`);
        }

        setQueuedConnections(connectionCount);

        // Update overall sync status based on queue counts and online status
        const totalQueued = connectionCount;
        setSyncStatus(prevStatus => {
          const newStatus = (() => {
            // If we have queued items and we're not already syncing
            if (totalQueued > 0 && prevStatus !== 'syncing') {
              return isOnline ? 'pending' : 'pending';
            } 
            // If no queued items and not syncing, we're synced
            else if (totalQueued === 0 && prevStatus !== 'syncing') {
              return 'synced';
            }
            // Keep current status if syncing
            return prevStatus;
          })();
          
          if (newStatus !== prevStatus) {
            console.log(`[PWAStatus] Sync status changed from ${prevStatus} to ${newStatus}, queued: ${totalQueued}`);
          }
          
          return newStatus;
        });
      } catch (error) {
        console.error('Failed to update queue counts:', error);
      }
    };

    // Initial update
    updateCounts();

    // Set up interval for periodic updates
    const interval = setInterval(updateCounts, 5000); // Update every 5 seconds

    return () => clearInterval(interval);
  }, [isOnline]); // Remove syncStatus dependency to prevent infinite loop

  // Enhanced service worker communication and PWA mode handling
  useEffect(() => {
    if ('serviceWorker' in navigator) {
      const handleServiceWorkerMessage = (event) => {
        console.log('[PWAStatus] Received message from service worker:', event.data);
        
        if (event.data && event.data.type === 'SYNC_STATUS_UPDATE') {
          const { status, queuedConnections: connections } = event.data;

          setSyncStatus(status);
          // Removed queuedScans - TripScanner operates online-only
          setQueuedConnections(connections || 0);

          if (status === 'synced') {
            setLastSyncTime(new Date().toISOString());
            setSyncError(null);
          } else if (status === 'error') {
            setSyncError(event.data.error || 'Sync failed');
          }
        } else if (event.data && event.data.type === 'TRIGGER_SYNC') {
          // AUTOMATIC SYNC DISABLED - Service worker sync requests are ignored
          console.log('[PWAStatus] Service worker requested sync but automatic sync is disabled:', event.data.syncType);
          console.log('[PWAStatus] Use manual sync button to transfer offline data');
        } else if (event.data && event.data.type === 'REQUEST_PWA_MODE') {
          // Enhanced response to service worker PWA mode requests
          console.log('[PWAStatus] Service worker requested PWA mode status');
          
          const currentPWAMode = detectAndSendPWAMode();
          console.log('[PWAStatus] Responded to service worker PWA mode request with enhanced detection:', currentPWAMode);
        } else if (event.data && event.data.type === 'PWA_MODE_CONFIRMATION') {
          // Service worker confirming receipt of PWA mode status
          console.log('[PWAStatus] Service worker confirmed PWA mode status:', event.data.confirmedMode);
        } else if (event.data && event.data.type === 'SERVICE_WORKER_READY') {
          // Service worker is ready - send current PWA mode status
          console.log('[PWAStatus] Service worker ready, sending current PWA mode status');
          detectAndSendPWAMode();
        }
      };

      // Enhanced service worker registration and communication setup
      const setupServiceWorkerCommunication = async () => {
        try {
          // Wait for service worker to be ready
          await navigator.serviceWorker.ready;
          
          // Send initial PWA mode status when service worker is ready
          console.log('[PWAStatus] Service worker ready, sending initial PWA mode status');
          detectAndSendPWAMode();
          
          // Set up message listener
          navigator.serviceWorker.addEventListener('message', handleServiceWorkerMessage);
          
          // Send ready notification to service worker
          if (navigator.serviceWorker.controller) {
            navigator.serviceWorker.controller.postMessage({
              type: 'CLIENT_READY',
              timestamp: new Date().toISOString(),
              source: 'pwa-status-hook'
            });
          }
        } catch (error) {
          console.error('[PWAStatus] Failed to setup service worker communication:', error);
        }
      };

      setupServiceWorkerCommunication();

      // Cleanup listener on unmount
      return () => {
        navigator.serviceWorker.removeEventListener('message', handleServiceWorkerMessage);
      };
    }
  }, [triggerSync, detectAndSendPWAMode]);

  // Export recovery function to global scope for debug tools
  useEffect(() => {
    if (typeof window !== 'undefined') {
      window.triggerSyncRecovery = recoverFromSyncError;
    }
    
    return () => {
      if (typeof window !== 'undefined') {
        delete window.triggerSyncRecovery;
      }
    };
  }, [recoverFromSyncError]);

  return {
    // Network status
    isOnline,
    
    // Sync status
    syncStatus,
    // queuedScans, // Removed - TripScanner operates online-only
    queuedConnections,
    lastSyncTime,
    syncError,
    
    // PWA status
    isPWA,
    installPrompt,
    isInstalled,
    
    // Actions
    triggerSync,
    installPWA,
    recoverFromSyncError, // Export recovery function
    
    // Computed values
    totalQueued: queuedConnections, // Only driver connections
    canSync: isOnline && queuedConnections > 0, // Allow sync in both PWA and browser modes
    canInstall: !!installPrompt && !isInstalled
  };
};

export default usePWAStatus;
