{"enabled": true, "name": "Test File Cleanup", "description": "Reviews and removes test, validation, check, and debug files from the codebase after verifying they are not used by production functions", "version": "1", "when": {"type": "userTriggered", "patterns": ["**/*test*.js", "**/*test*.md", "**/*validation*.js", "**/*validation*.md", "**/*check*.js", "**/*check*.md", "**/*debug*.js", "**/*debug*.md", "**/tests/**/*", "**/test-results/**/*"]}, "then": {"type": "askAgent", "prompt": "I need you to analyze the codebase and identify files containing 'test', 'validation', 'check', or 'debug' in their names or paths. Before removing any files, you must:\n\n1. First, scan all production files (server/, client/src/, scripts/, database/) to identify any imports, requires, or references to the test/validation/check/debug files\n2. Create a dependency map showing which production files (if any) depend on these files\n3. For each file marked for removal, verify it's not imported or referenced by production code\n4. Only remove files that are confirmed to be standalone test/validation/check/debug files with no production dependencies\n5. Provide a summary of what was removed and what was kept (with reasons)\n\nFocus on these file patterns:\n- Files with 'test', 'validation', 'check', 'debug' in filename\n- Files in /tests/ and /test-results/ directories\n- Files ending with .test.js, .spec.js, .check.js, .debug.js\n- Documentation files related to testing/validation\n\nBe extremely careful not to remove any files that are actually used by production functionality, even if they have test-related names."}}