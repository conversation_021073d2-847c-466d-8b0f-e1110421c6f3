import React, { useState, useMemo } from 'react';

const StatusFilter = ({ 
  statuses = [], 
  selectedStatus = '', 
  onStatusChange, 
  loading = false,
  showCounts = true,
  className = ""
}) => {
  const [viewMode, setViewMode] = useState('list'); // 'list', 'grid', 'compact'

  // Status icons and colors mapping
  const statusConfig = {
    'assigned': { icon: '📋', color: 'blue', bgColor: 'bg-blue-100', textColor: 'text-blue-800' },
    'loading_start': { icon: '⬆️', color: 'yellow', bgColor: 'bg-yellow-100', textColor: 'text-yellow-800' },
    'loading_end': { icon: '✅', color: 'green', bgColor: 'bg-green-100', textColor: 'text-green-800' },
    'unloading_start': { icon: '⬇️', color: 'orange', bgColor: 'bg-orange-100', textColor: 'text-orange-800' },
    'unloading_end': { icon: '✅', color: 'purple', bgColor: 'bg-purple-100', textColor: 'text-purple-800' },
    'trip_completed': { icon: '🏁', color: 'green', bgColor: 'bg-green-100', textColor: 'text-green-800' },
    'auto_assignment': { icon: '🤖', color: 'indigo', bgColor: 'bg-indigo-100', textColor: 'text-indigo-800' },
    'dynamic_route': { icon: '🔄', color: 'blue', bgColor: 'bg-blue-100', textColor: 'text-blue-800' },
    'cancelled': { icon: '❌', color: 'gray', bgColor: 'bg-gray-100', textColor: 'text-gray-800' }
  };

  // Group statuses by category
  const categorizedStatuses = useMemo(() => {
    const categories = {
      active: [],
      completed: [],
      issues: []
    };

    statuses.forEach(status => {
      if (['assigned', 'loading_start', 'loading_end', 'unloading_start', 'unloading_end', 'auto_assignment', 'dynamic_route'].includes(status.value)) {
        categories.active.push(status);
      } else if (['trip_completed'].includes(status.value)) {
        categories.completed.push(status);
      } else {
        categories.issues.push(status);
      }
    });

    return categories;
  }, [statuses]);

  const handleStatusSelect = (statusValue) => {
    onStatusChange(statusValue === selectedStatus ? '' : statusValue);
  };

  const getStatusConfig = (statusValue) => {
    return statusConfig[statusValue] || { 
      icon: '📊', 
      color: 'gray', 
      bgColor: 'bg-gray-100', 
      textColor: 'text-gray-800' 
    };
  };

  const clearStatus = () => {
    onStatusChange('');
  };

  const renderStatusOption = (status, isSelected) => {
    const config = getStatusConfig(status.value);
    
    return (
      <button
        key={status.value}
        onClick={() => handleStatusSelect(status.value)}
        className={`p-3 rounded-lg border transition-all duration-200 text-left w-full ${
          isSelected 
            ? `${config.bgColor} border-${config.color}-300 ring-2 ring-${config.color}-200` 
            : 'bg-white border-secondary-200 hover:border-secondary-300 hover:shadow-sm'
        }`}
        disabled={loading}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <span className="text-lg">{config.icon}</span>
            <div>
              <div className={`font-medium ${isSelected ? config.textColor : 'text-secondary-900'}`}>
                {status.label}
              </div>
              {status.description && (
                <div className={`text-xs ${isSelected ? config.textColor : 'text-secondary-500'} mt-0.5`}>
                  {status.description}
                </div>
              )}
            </div>
          </div>
          {showCounts && status.count !== undefined && (
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
              isSelected ? `bg-white ${config.textColor}` : 'bg-secondary-100 text-secondary-600'
            }`}>
              {status.count}
            </span>
          )}
        </div>
      </button>
    );
  };

  const renderCompactView = () => (
    <div className="space-y-2">
      <select
        value={selectedStatus}
        onChange={(e) => onStatusChange(e.target.value)}
        disabled={loading}
        className="input"
      >
        <option value="">All Status</option>
        {Object.entries(categorizedStatuses).map(([category, categoryStatuses]) => (
          categoryStatuses.length > 0 && (
            <optgroup key={category} label={category.charAt(0).toUpperCase() + category.slice(1)}>
              {categoryStatuses.map(status => (
                <option key={status.value} value={status.value}>
                  {getStatusConfig(status.value).icon} {status.label} 
                  {showCounts && status.count !== undefined ? ` (${status.count})` : ''}
                </option>
              ))}
            </optgroup>
          )
        ))}
      </select>
    </div>
  );

  const renderGridView = () => (
    <div className="grid grid-cols-2 lg:grid-cols-4 gap-2">
      {statuses.map(status => renderStatusOption(status, status.value === selectedStatus))}
    </div>
  );

  const renderListView = () => (
    <div className="space-y-4">
      {Object.entries(categorizedStatuses).map(([category, categoryStatuses]) => (
        categoryStatuses.length > 0 && (
          <div key={category}>
            <h4 className="text-sm font-medium text-secondary-700 mb-2 uppercase tracking-wider">
              {category === 'active' && '🔄 Active Status'}
              {category === 'completed' && '✅ Completed'}
              {category === 'issues' && '⚠️ Issues & Exceptions'}
            </h4>
            <div className="space-y-2">
              {categoryStatuses.map(status => renderStatusOption(status, status.value === selectedStatus))}
            </div>
          </div>
        )
      ))}
    </div>
  );

  return (
    <div className={`space-y-3 ${className}`}>
      {/* View Mode Selector */}
      <div className="flex items-center justify-between">
        <div className="text-sm font-medium text-secondary-700">Status Filter</div>
        <div className="flex space-x-1">
          {[
            { key: 'compact', label: 'Compact', icon: '☰' },
            { key: 'list', label: 'List', icon: '📋' },
            { key: 'grid', label: 'Grid', icon: '⊞' }
          ].map(mode => (
            <button
              key={mode.key}
              onClick={() => setViewMode(mode.key)}
              className={`px-2 py-1 text-xs rounded transition-colors ${
                viewMode === mode.key
                  ? 'bg-primary-100 text-primary-700'
                  : 'bg-secondary-100 text-secondary-600 hover:bg-secondary-200'
              }`}
              disabled={loading}
              title={`${mode.label} view`}
            >
              {mode.icon}
            </button>
          ))}
        </div>
      </div>

      {/* Status Options */}
      {loading ? (
        <div className="text-center py-4">
          <div className="inline-flex items-center text-sm text-secondary-600">
            <svg className="animate-spin -ml-1 mr-3 h-4 w-4" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Loading statuses...
          </div>
        </div>
      ) : (
        <>
          {viewMode === 'compact' && renderCompactView()}
          {viewMode === 'list' && renderListView()}
          {viewMode === 'grid' && renderGridView()}
        </>
      )}

      {/* Selected Status Info */}
      {selectedStatus && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <span className="text-lg">{getStatusConfig(selectedStatus).icon}</span>
              <div>
                <div className="font-medium text-blue-900">
                  {statuses.find(s => s.value === selectedStatus)?.label || selectedStatus}
                </div>
                {statuses.find(s => s.value === selectedStatus)?.description && (
                  <div className="text-sm text-blue-700">
                    {statuses.find(s => s.value === selectedStatus).description}
                  </div>
                )}
              </div>
            </div>
            <button
              onClick={clearStatus}
              className="text-blue-600 hover:text-blue-800 p-1"
              title="Clear status filter"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      )}

      {/* Status Summary */}
      {!loading && statuses.length > 0 && showCounts && (
        <div className="text-xs text-secondary-500 space-y-1">
          <div className="flex justify-between">
            <span>Total statuses: {statuses.length}</span>
            {selectedStatus && (
              <span className="text-primary-600">
                {statuses.find(s => s.value === selectedStatus)?.count || 0} trips
              </span>
            )}
          </div>
          {!selectedStatus && (
            <div className="text-secondary-400">
              Total trips: {statuses.reduce((sum, status) => sum + (status.count || 0), 0)}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default StatusFilter;