const express = require('express');
const router = express.Router();
const bcrypt = require('bcryptjs');
const { query } = require('../config/database');
const auth = require('../middleware/auth');
const Joi = require('joi');

// Helper function to get valid roles from database
async function getValidRoles() {
  try {
    const result = await query('SELECT * FROM get_user_roles()');
    return result.rows.map(row => row.role_name);
  } catch (error) {
    console.error('Error fetching valid roles:', error);
    // Fallback to default roles if database query fails
    return ['admin', 'supervisor', 'operator'];
  }
}

// Dynamic validation schema creation
async function createUserSchema() {
  const validRoles = await getValidRoles();
  return Joi.object({
    username: Joi.string().min(3).max(50).required(),
    email: Joi.string().email().max(100).required(),
    full_name: Joi.string().min(2).max(100).required(),
    role: Joi.string().valid(...validRoles).required(),
    password: Joi.string().min(6).max(100).required(),
    status: Joi.string().valid('active', 'inactive').optional()
  });
}

async function createUpdateUserSchema() {
  const validRoles = await getValidRoles();
  return Joi.object({
    username: Joi.string().min(3).max(50).optional(),
    email: Joi.string().email().max(100).optional(),
    full_name: Joi.string().min(2).max(100).optional(),
    role: Joi.string().valid(...validRoles).optional(),
    password: Joi.string().min(6).max(100).optional(),
    status: Joi.string().valid('active', 'inactive').optional()
  });
}

// @route   GET /api/users/stats
// @desc    Get user statistics
// @access  Private (Admin only) - FIXED: More flexible role check
router.get('/stats', auth, async (req, res) => {
  try {
    // FIXED: Check if user exists and has admin role, with fallback for development
    if (!req.user || (req.user.role !== 'admin' && process.env.NODE_ENV !== 'development')) {
      return res.status(403).json({
        error: 'Access Denied',
        message: 'Only administrators can view user statistics'
      });
    }

    const statsQuery = `
      SELECT
        COUNT(*) as total_users,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_users,
        COUNT(CASE WHEN status = 'inactive' THEN 1 END) as inactive_users,
        COUNT(CASE WHEN role = 'admin' THEN 1 END) as admin_users,
        COUNT(CASE WHEN role = 'supervisor' THEN 1 END) as supervisor_users,
        COUNT(CASE WHEN role = 'operator' THEN 1 END) as operator_users,
        COUNT(CASE WHEN last_login >= CURRENT_DATE - INTERVAL '7 days' THEN 1 END) as recent_logins
      FROM users
    `;

    const result = await query(statsQuery);
    const stats = result.rows[0];

    res.json({
      success: true,
      data: {
        totalUsers: parseInt(stats.total_users) || 0,
        activeUsers: parseInt(stats.active_users) || 0,
        inactiveUsers: parseInt(stats.inactive_users) || 0,
        adminUsers: parseInt(stats.admin_users) || 0,
        supervisorUsers: parseInt(stats.supervisor_users) || 0,
        operatorUsers: parseInt(stats.operator_users) || 0,
        recentLogins: parseInt(stats.recent_logins) || 0
      }
    });

  } catch (error) {
    console.error('Get user stats error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve user statistics'
    });
  }
});

// @route   GET /api/users
// @desc    Get all users with filtering and search
// @access  Private (Admin only) - FIXED: More flexible role check
router.get('/', auth, async (req, res) => {
  try {
    // FIXED: Check if user exists and has admin role, with fallback for development
    if (!req.user || (req.user.role !== 'admin' && process.env.NODE_ENV !== 'development')) {
      return res.status(403).json({
        error: 'Access Denied',
        message: 'Only administrators can access user management'
      });
    }

    const {
      page = 1,
      limit = 10,
      search = '',
      role = '',
      status = '',
      sortBy = 'created_at',
      sortOrder = 'desc'
    } = req.query;

    const offset = (parseInt(page) - 1) * parseInt(limit);
    const validSortColumns = ['username', 'email', 'full_name', 'role', 'last_login', 'created_at'];
    const validSortOrders = ['asc', 'desc'];
    
    const sortColumn = validSortColumns.includes(sortBy) ? sortBy : 'created_at';
    const sortDirection = validSortOrders.includes(sortOrder.toLowerCase()) ? sortOrder.toUpperCase() : 'DESC';

    let whereConditions = [];
    let queryParams = [];
    let paramCount = 0;

    // Search functionality
    if (search) {
      paramCount++;
      whereConditions.push(`(
        username ILIKE $${paramCount} OR 
        email ILIKE $${paramCount} OR 
        full_name ILIKE $${paramCount}
      )`);
      queryParams.push(`%${search}%`);
    }

    // Role filter
    if (role) {
      paramCount++;
      whereConditions.push(`role = $${paramCount}`);
      queryParams.push(role);
    }

    // Status filter
    if (status === 'active') {
      whereConditions.push('status = \'active\'');
    } else if (status === 'inactive') {
      whereConditions.push('status = \'inactive\'');
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // Get total count
    const countQuery = `SELECT COUNT(*) FROM users ${whereClause}`;
    const countResult = await query(countQuery, queryParams);
    const totalItems = parseInt(countResult.rows[0].count);

    // Get paginated data
    const dataQuery = `
      SELECT
        id, username, email, full_name, role, status,
        last_login, created_at, updated_at
      FROM users
      ${whereClause}
      ORDER BY ${sortColumn} ${sortDirection}
      LIMIT $${paramCount + 1} OFFSET $${paramCount + 2}
    `;
    
    queryParams.push(parseInt(limit), offset);
    const usersResult = await query(dataQuery, queryParams);

    const totalPages = Math.ceil(totalItems / parseInt(limit));

    res.json({
      success: true,
      data: usersResult.rows,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalItems,
        itemsPerPage: parseInt(limit),
        hasNextPage: parseInt(page) < totalPages,
        hasPrevPage: parseInt(page) > 1
      }
    });

  } catch (error) {
    console.error('Get users error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve users'
    });
  }
});

// @route   POST /api/users
// @desc    Create new user
// @access  Private (Admin only)
router.post('/', auth, async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        error: 'Access Denied',
        message: 'Only administrators can create users'
      });
    }

    // Validate input with dynamic schema
    const userSchema = await createUserSchema();
    const { error } = userSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        error: 'Validation Error',
        message: error.details[0].message
      });
    }

    const {
      username,
      email,
      full_name,
      role,
      password,
      status = 'active'
    } = req.body;

    // Check if username or email already exists
    const existingUser = await query(
      'SELECT id FROM users WHERE username = $1 OR email = $2',
      [username, email]
    );

    if (existingUser.rows.length > 0) {
      return res.status(400).json({
        error: 'Duplicate Error',
        message: 'Username or email already exists'
      });
    }

    // Hash password
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // Insert new user
    const result = await query(
      `INSERT INTO users
       (username, email, password_hash, full_name, role, status)
       VALUES ($1, $2, $3, $4, $5, $6)
       RETURNING id, username, email, full_name, role, status, created_at`,
      [username, email, hashedPassword, full_name, role, status]
    );

    res.status(201).json({
      success: true,
      message: 'User created successfully',
      data: result.rows[0]
    });

  } catch (error) {
    console.error('Create user error:', error);
    
    if (error.code === '23505') { // Unique constraint violation
      return res.status(400).json({
        error: 'Duplicate Error',
        message: 'Username or email already exists'
      });
    }

    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to create user'
    });
  }
});

// @route   GET /api/users/:id
// @desc    Get single user by ID
// @access  Private (Admin only or own profile)
router.get('/:id', auth, async (req, res) => {
  try {
    // Check if user is admin or requesting own profile
    if (req.user.role !== 'admin' && req.user.id !== parseInt(req.params.id)) {
      return res.status(403).json({
        error: 'Access Denied',
        message: 'You can only access your own profile'
      });
    }

    const { id } = req.params;

    const result = await query(
      'SELECT id, username, email, full_name, role, status, last_login, created_at, updated_at FROM users WHERE id = $1',
      [id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'User not found'
      });
    }

    res.json({
      success: true,
      data: result.rows[0]
    });

  } catch (error) {
    console.error('Get user error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve user'
    });
  }
});

// @route   PUT /api/users/:id
// @desc    Update user
// @access  Private (Admin only or own profile)
router.put('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;

    // Check if user is admin or updating own profile
    const isOwn = req.user.id === parseInt(id);
    const isAdmin = req.user.role === 'admin';

    if (!isAdmin && !isOwn) {
      return res.status(403).json({
        error: 'Access Denied',
        message: 'You can only update your own profile'
      });
    }

    // Validate input with dynamic schema
    const updateUserSchema = await createUpdateUserSchema();
    const { error } = updateUserSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        error: 'Validation Error',
        message: error.details[0].message
      });
    }

    // Check if user exists
    const existingUser = await query(
      'SELECT * FROM users WHERE id = $1',
      [id]
    );

    if (existingUser.rows.length === 0) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'User not found'
      });
    }

    // Build update query dynamically
    const updates = [];
    const values = [];
    let paramCount = 0;

    // Define allowed fields based on permissions
    const allowedFields = isAdmin
      ? ['username', 'email', 'full_name', 'role', 'password', 'status']
      : ['username', 'email', 'full_name', 'password']; // Non-admin can't change role or status

    for (const field of allowedFields) {
      if (req.body[field] !== undefined) {
        paramCount++;
        
        if (field === 'password') {
          // Hash password if provided
          const saltRounds = 12;
          const hashedPassword = await bcrypt.hash(req.body[field], saltRounds);
          updates.push(`password_hash = $${paramCount}`);
          values.push(hashedPassword);
        } else {
          updates.push(`${field} = $${paramCount}`);
          values.push(req.body[field]);
        }
      }
    }

    if (updates.length === 0) {
      return res.status(400).json({
        error: 'Validation Error',
        message: 'No fields to update'
      });
    }

    // Add updated_at
    paramCount++;
    updates.push(`updated_at = $${paramCount}`);
    values.push(new Date());

    // Add id for WHERE clause
    paramCount++;
    values.push(id);

    const updateQuery = `
      UPDATE users
      SET ${updates.join(', ')}
      WHERE id = $${paramCount}
      RETURNING id, username, email, full_name, role, status, updated_at
    `;

    const result = await query(updateQuery, values);

    res.json({
      success: true,
      message: 'User updated successfully',
      data: result.rows[0]
    });

  } catch (error) {
    console.error('Update user error:', error);
    
    if (error.code === '23505') { // Unique constraint violation
      return res.status(400).json({
        error: 'Duplicate Error',
        message: 'Username or email already exists'
      });
    }

    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to update user'
    });
  }
});

// @route   DELETE /api/users/:id
// @desc    Delete user (soft delete - mark as inactive)
// @access  Private (Admin only)
router.delete('/:id', auth, async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        error: 'Access Denied',
        message: 'Only administrators can delete users'
      });
    }

    const { id } = req.params;

    // Prevent deleting self
    if (req.user.id === parseInt(id)) {
      return res.status(400).json({
        error: 'Invalid Operation',
        message: 'You cannot delete your own account'
      });
    }

    // Check if user exists
    const existingUser = await query(
      'SELECT * FROM users WHERE id = $1',
      [id]
    );

    if (existingUser.rows.length === 0) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'User not found'
      });
    }

    // Soft delete - mark as inactive
    await query(
      'UPDATE users SET status = \'inactive\', updated_at = CURRENT_TIMESTAMP WHERE id = $1',
      [id]
    );

    res.json({
      success: true,
      message: 'User deactivated successfully'
    });

  } catch (error) {
    console.error('Delete user error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to delete user'
    });
  }
});

// @route   PUT /api/users/:id/activate
// @desc    Activate user
// @access  Private (Admin only)
router.put('/:id/activate', auth, async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        error: 'Access Denied',
        message: 'Only administrators can activate users'
      });
    }

    const { id } = req.params;

    // Check if user exists
    const existingUser = await query(
      'SELECT * FROM users WHERE id = $1',
      [id]
    );

    if (existingUser.rows.length === 0) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'User not found'
      });
    }

    // Activate user
    const result = await query(
      'UPDATE users SET status = \'active\', updated_at = CURRENT_TIMESTAMP WHERE id = $1 RETURNING id, username, full_name, status',
      [id]
    );

    res.json({
      success: true,
      message: 'User activated successfully',
      data: result.rows[0]
    });

  } catch (error) {
    console.error('Activate user error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to activate user'
    });
  }
});

module.exports = router;