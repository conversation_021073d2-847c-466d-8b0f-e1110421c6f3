import React, { useState, useEffect, useCallback, useRef } from 'react';
import { locationsAPI, scannerAPI } from '../services/api';
import toast from 'react-hot-toast';

const LocationDropdown = ({
  onLocationSelect,
  selectedLocation,
  isOnline = true,
  disabled = false,
  usePublicEndpoint = false
}) => {
  // State management
  const [locations, setLocations] = useState([]);
  const [filteredLocations, setFilteredLocations] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [error, setError] = useState(null);
  const [isMobile, setIsMobile] = useState(false);

  // Refs for managing focus and debouncing
  const searchInputRef = useRef(null);
  const dropdownRef = useRef(null);
  const debounceTimeoutRef = useRef(null);

  // Cache management
  const cacheRef = useRef({
    data: null,
    timestamp: null,
    expiry: 5 * 60 * 1000 // 5 minutes
  });

  // Mobile detection
  useEffect(() => {
    const checkMobile = () => {
      const userAgent = navigator.userAgent || navigator.vendor || window.opera;
      const isMobileDevice = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent.toLowerCase());
      setIsMobile(isMobileDevice);
    };
    checkMobile();
  }, []);

  // Check if cache is valid
  const isCacheValid = useCallback(() => {
    const cache = cacheRef.current;
    return cache.data && cache.timestamp && 
           (Date.now() - cache.timestamp) < cache.expiry;
  }, []);

  // Fetch locations with caching
  const fetchLocations = useCallback(async () => {
    // Check cache first
    if (isCacheValid()) {
      setLocations(cacheRef.current.data);
      setFilteredLocations(cacheRef.current.data);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Use public endpoint for Trip Scanner, regular endpoint for admin pages
      const response = usePublicEndpoint
        ? await scannerAPI.getPublicLocations()
        : await locationsAPI.getActive();

      // Handle different response structures
      // Both endpoints return { success: true, data: [...locations...] }
      // Axios wraps server response in response.data, so locations are in response.data.data
      const locationData = response.data?.data || [];

      // Ensure locationData is an array
      if (!Array.isArray(locationData)) {
        console.error('LocationDropdown: Expected array but got:', typeof locationData, locationData);
        throw new Error('Invalid location data format received from server');
      }

      // Validate location data structure
      const validLocations = locationData.filter(location => 
        location && 
        location.id && 
        location.name && 
        typeof location.name === 'string'
      );

      if (validLocations.length === 0) {
        throw new Error('No valid locations received from server');
      }

      // Update cache
      cacheRef.current = {
        data: validLocations,
        timestamp: Date.now(),
        expiry: 5 * 60 * 1000
      };

      setLocations(validLocations);
      setFilteredLocations(validLocations);
    } catch (error) {
      console.error('Failed to fetch locations:', error);
      setError(error.response?.data?.message || 'Failed to load locations');
      
      // Use cached data if available, even if expired
      if (cacheRef.current.data) {
        setLocations(cacheRef.current.data);
        setFilteredLocations(cacheRef.current.data);
        toast.error('Using cached locations - please refresh when online');
      } else {
        toast.error('Failed to load locations. Please check your connection.');
      }
    } finally {
      setIsLoading(false);
    }
  }, [isCacheValid, usePublicEndpoint]);

  // Debounced search function
  const debouncedSearch = useCallback((term) => {
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    debounceTimeoutRef.current = setTimeout(() => {
      if (!term.trim()) {
        setFilteredLocations(locations);
        return;
      }

      const filtered = locations.filter(location =>
        location.name.toLowerCase().includes(term.toLowerCase()) ||
        (location.code && location.code.toLowerCase().includes(term.toLowerCase())) ||
        (location.type && location.type.toLowerCase().includes(term.toLowerCase()))
      );

      setFilteredLocations(filtered);
    }, 300); // 300ms debounce delay
  }, [locations]);

  // Handle search input change
  const handleSearchChange = useCallback((e) => {
    const value = e.target.value;
    setSearchTerm(value);
    debouncedSearch(value);
  }, [debouncedSearch]);

  // Handle location selection
  const handleLocationSelect = useCallback((location) => {
    if (!location || disabled) return;

    // Create location data structure identical to QR scanning
    const locationData = {
      id: location.location_code || location.code || location.id,
      type: 'location',
      name: location.name,
      last_scan: new Date().toISOString()
    };

    onLocationSelect(locationData);
    setIsOpen(false);
    setSearchTerm('');
    setFilteredLocations(locations);
  }, [onLocationSelect, disabled, locations]);

  // Handle dropdown toggle
  const handleToggleDropdown = useCallback(() => {
    if (disabled || !isOnline) return;
    
    if (!isOpen && locations.length === 0) {
      fetchLocations();
    }
    setIsOpen(!isOpen);
  }, [disabled, isOnline, isOpen, locations.length, fetchLocations]);

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('touchstart', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('touchstart', handleClickOutside);
    };
  }, [isOpen]);

  // Cleanup debounce timeout
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  // Focus search input when dropdown opens
  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      // Small delay to ensure dropdown is rendered
      setTimeout(() => {
        searchInputRef.current?.focus();
      }, 100);
    }
  }, [isOpen]);

  // Get location type display
  const getLocationTypeDisplay = (location) => {
    if (!location.type) return '';
    return location.type.charAt(0).toUpperCase() + location.type.slice(1);
  };

  // Get location type color
  const getLocationTypeColor = (type) => {
    switch (type?.toLowerCase()) {
      case 'loading':
        return 'text-blue-600 bg-blue-100';
      case 'unloading':
        return 'text-green-600 bg-green-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <div className="relative w-full" ref={dropdownRef}>
      {/* Dropdown Button */}
      <button
        type="button"
        onClick={handleToggleDropdown}
        disabled={disabled || !isOnline}
        className={`
          w-full px-4 py-3 text-left bg-white border-2 rounded-lg shadow-sm
          flex items-center justify-between
          transition-all duration-200
          ${isMobile ? 'min-h-[44px]' : 'min-h-[40px]'}
          ${disabled || !isOnline 
            ? 'bg-gray-100 border-gray-300 text-gray-500 cursor-not-allowed' 
            : isOpen 
              ? 'border-primary-500 ring-2 ring-primary-200' 
              : 'border-secondary-300 hover:border-primary-400 focus:border-primary-500 focus:ring-2 focus:ring-primary-200'
          }
        `}
      >
        <div className="flex items-center space-x-3 flex-1 min-w-0">
          <span className="text-xl">📍</span>
          <div className="flex-1 min-w-0">
            {selectedLocation ? (
              <div>
                <div className="font-medium text-secondary-900 truncate">
                  {selectedLocation.name}
                </div>
                <div className="text-xs text-secondary-500">
                  Selected • {new Date(selectedLocation.last_scan).toLocaleTimeString()}
                </div>
              </div>
            ) : (
              <div className="text-secondary-600">
                {!isOnline ? 'Internet connection required' : 'Select a location...'}
              </div>
            )}
          </div>
        </div>
        
        {/* Dropdown Arrow */}
        <div className={`
          ml-2 transition-transform duration-200 
          ${isOpen ? 'transform rotate-180' : ''}
        `}>
          <svg className="w-5 h-5 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </div>
      </button>

      {/* Offline Message */}
      {!isOnline && (
        <div className="mt-2 p-3 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center space-x-2">
            <span className="text-red-600">🌐</span>
            <div className="text-sm text-red-700">
              <div className="font-medium">Internet Connection Required</div>
              <div className="text-xs mt-1">
                Location selection requires real-time validation for trip workflow integrity.
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Dropdown Menu */}
      {isOpen && isOnline && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-secondary-300 rounded-lg shadow-lg max-h-80 overflow-hidden">
          {/* Search Input */}
          <div className="p-3 border-b border-secondary-200">
            <div className="relative">
              <input
                ref={searchInputRef}
                type="text"
                value={searchTerm}
                onChange={handleSearchChange}
                placeholder="Search locations..."
                className={`
                  w-full px-3 py-2 border border-secondary-300 rounded-md
                  focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500
                  ${isMobile ? 'text-base' : 'text-sm'}
                `}
                style={isMobile ? { fontSize: '16px' } : {}} // Prevent zoom on iOS
              />
              <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                <svg className="w-4 h-4 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
            </div>
          </div>

          {/* Loading State */}
          {isLoading && (
            <div className="p-4 text-center">
              <div className="flex items-center justify-center space-x-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-600"></div>
                <span className="text-sm text-secondary-600">Loading locations...</span>
              </div>
            </div>
          )}

          {/* Error State */}
          {error && !isLoading && (
            <div className="p-4 text-center">
              <div className="text-sm text-red-600">
                <div className="font-medium">Failed to load locations</div>
                <div className="mt-1">{error}</div>
                <button
                  onClick={fetchLocations}
                  className="mt-2 text-xs text-primary-600 hover:text-primary-700 underline"
                >
                  Try again
                </button>
              </div>
            </div>
          )}

          {/* Location Options */}
          {!isLoading && !error && (
            <div className="max-h-60 overflow-y-auto">
              {filteredLocations.length === 0 ? (
                <div className="p-4 text-center text-sm text-secondary-600">
                  {searchTerm ? 'No locations found matching your search' : 'No locations available'}
                </div>
              ) : (
                filteredLocations.map((location) => (
                  <button
                    key={location.id}
                    onClick={() => handleLocationSelect(location)}
                    className={`
                      w-full px-4 py-3 text-left hover:bg-secondary-50 focus:bg-secondary-50
                      focus:outline-none border-b border-secondary-100 last:border-b-0
                      transition-colors duration-150
                      ${isMobile ? 'min-h-[44px]' : ''}
                    `}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="font-medium text-secondary-900 truncate">
                          {location.name}
                        </div>
                        {location.code && (
                          <div className="text-xs text-secondary-500 mt-1">
                            Code: {location.code}
                          </div>
                        )}
                      </div>
                      {location.type && (
                        <div className={`
                          ml-3 px-2 py-1 rounded-full text-xs font-medium
                          ${getLocationTypeColor(location.type)}
                        `}>
                          {getLocationTypeDisplay(location)}
                        </div>
                      )}
                    </div>
                  </button>
                ))
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default LocationDropdown;