const { query } = require('../config/database');
const { logError, logInfo } = require('../utils/logger');

/**
 * Driver Attendance Service
 * Handles attendance calculations and reporting for driver shifts
 * PERFORMANCE OPTIMIZED with efficient database queries and caching
 */
class DriverAttendanceService {

  /**
   * PERFORMANCE OPTIMIZATION: Get attendance records with optimized queries
   * Uses proper indexes and efficient JOIN operations
   * @param {Object} filters - Filter parameters
   * @returns {Promise<Object>} Attendance records with performance metrics
   */
  static async getOptimizedAttendanceRecords(filters = {}) {
    const startTime = Date.now();

    try {
      const {
        driver_id,
        date_from,
        date_to,
        truck_id,
        status = 'completed',
        limit = 50,
        offset = 0
      } = filters;

      // Build optimized query with proper index usage
      let whereClause = 'WHERE 1=1';
      let params = [];
      let paramCount = 0;

      if (driver_id) {
        paramCount++;
        whereClause += ` AND ds.driver_id = $${paramCount}`;
        params.push(driver_id);
      }

      // Enhanced date filtering with overlap detection for overnight shifts
      if (date_from || date_to) {
        if (date_from && date_to) {
          // Both dates provided - use overlap detection
          paramCount++;
          whereClause += ` AND (
            -- Shift starts within the date range
            (ds.start_date BETWEEN $${paramCount} AND $${paramCount + 1}) OR
            -- Shift ends within the date range
            (ds.end_date BETWEEN $${paramCount} AND $${paramCount + 1}) OR
            -- Shift spans the entire date range
            (ds.start_date <= $${paramCount} AND ds.end_date >= $${paramCount + 1})
          )`;
          params.push(date_from, date_to);
          paramCount++; // Increment for the second parameter
        } else if (date_from) {
          // Only start date provided
          paramCount++;
          whereClause += ` AND (ds.start_date >= $${paramCount} OR ds.end_date >= $${paramCount})`;
          params.push(date_from);
        } else if (date_to) {
          // Only end date provided
          paramCount++;
          whereClause += ` AND (ds.start_date <= $${paramCount} OR ds.end_date <= $${paramCount})`;
          params.push(date_to);
        }
      }

      if (truck_id) {
        paramCount++;
        whereClause += ` AND ds.truck_id = $${paramCount}`;
        params.push(truck_id);
      }

      if (status && status !== 'all') {
        paramCount++;
        whereClause += ` AND ds.status = $${paramCount}`;
        params.push(status);
      }

      // OPTIMIZED QUERY: Uses attendance_lookup index
      const attendanceQuery = `
        SELECT 
          ds.id as shift_id,
          ds.driver_id,
          d.employee_id,
          d.full_name as driver_name,
          ds.truck_id,
          dt.truck_number,
          ds.start_date,
          ds.start_time,
          ds.end_date,
          ds.end_time,
          ds.status,
          ds.shift_type,
          ds.auto_created,
          ds.created_at,
          -- PERFORMANCE: Calculate duration in database for efficiency
          CASE 
            WHEN ds.status = 'completed' AND ds.end_date IS NOT NULL AND ds.end_time IS NOT NULL
            THEN EXTRACT(EPOCH FROM (
              (ds.end_date + ds.end_time) - (ds.start_date + ds.start_time)
            )) / 3600
            ELSE NULL
          END as duration_hours
        FROM driver_shifts ds
        JOIN drivers d ON ds.driver_id = d.id
        JOIN dump_trucks dt ON ds.truck_id = dt.id
        ${whereClause}
        ORDER BY ds.start_date DESC, ds.start_time DESC
        LIMIT $${paramCount + 1} OFFSET $${paramCount + 2}
      `;

      params.push(limit, offset);

      const result = await query(attendanceQuery, params);
      const queryTime = Date.now() - startTime;

      // Log performance metrics for monitoring
      if (queryTime > 100) {
        logInfo('SLOW_ATTENDANCE_QUERY', `Attendance query took ${queryTime}ms`, {
          filters,
          query_time_ms: queryTime,
          result_count: result.rows.length
        });
      }

      return {
        success: true,
        records: result.rows,
        performance: {
          query_time_ms: queryTime,
          record_count: result.rows.length,
          optimized: queryTime < 100
        }
      };

    } catch (error) {
      const queryTime = Date.now() - startTime;
      logError('OPTIMIZED_ATTENDANCE_QUERY_ERROR', error, {
        filters,
        query_time_ms: queryTime
      });
      throw error;
    }
  }

  /**
   * Calculate simple shift duration (end_time - start_time)
   * @param {string} startDate - Start date (YYYY-MM-DD)
   * @param {string} startTime - Start time (HH:MM:SS)
   * @param {string} endDate - End date (YYYY-MM-DD)
   * @param {string} endTime - End time (HH:MM:SS)
   * @returns {Object} Duration information
   */
  static calculateShiftDuration(startDate, startTime, endDate, endTime) {
    try {
      if (!startDate || !startTime || !endDate || !endTime) {
        return {
          duration_ms: null,
          duration_hours: null,
          duration_minutes: null,
          duration_formatted: null,
          is_overnight: false
        };
      }

      const startDateTime = new Date(`${startDate}T${startTime}`);
      const endDateTime = new Date(`${endDate}T${endTime}`);

      if (isNaN(startDateTime.getTime()) || isNaN(endDateTime.getTime())) {
        throw new Error('Invalid date/time format');
      }

      const durationMs = endDateTime - startDateTime;

      if (durationMs < 0) {
        throw new Error('End time cannot be before start time');
      }

      const durationHours = Math.floor(durationMs / (1000 * 60 * 60));
      const durationMinutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60));
      const isOvernight = startDate !== endDate;

      return {
        duration_ms: durationMs,
        duration_hours: durationHours,
        duration_minutes: durationMinutes,
        duration_formatted: `${durationHours}h ${durationMinutes}m`,
        is_overnight: isOvernight,
        total_hours: parseFloat((durationMs / (1000 * 60 * 60)).toFixed(2))
      };

    } catch (error) {
      logError('DURATION_CALCULATION_ERROR', error, {
        start_date: startDate,
        start_time: startTime,
        end_date: endDate,
        end_time: endTime
      });

      return {
        duration_ms: null,
        duration_hours: null,
        duration_minutes: null,
        duration_formatted: 'Error calculating duration',
        is_overnight: false,
        error: error.message
      };
    }
  }

  /**
   * Get attendance records with filtering options
   * @param {Object} filters - Filter options
   * @returns {Promise<Object>} Attendance records with pagination
   */
  static async getAttendanceRecords(filters = {}) {
    try {
      const {
        driver_id,
        date_from,
        date_to,
        truck_id,
        status = 'all', // 'all', 'active', 'completed'
        limit = 50,
        offset = 0,
        sort_by = 'start_date',
        sort_order = 'desc'
      } = filters;

      // Build dynamic WHERE clause
      let whereClause = 'WHERE 1=1';
      let params = [];
      let paramCount = 0;

      if (driver_id) {
        paramCount++;
        whereClause += ` AND ds.driver_id = $${paramCount}`;
        params.push(driver_id);
      }

      // Enhanced date filtering with overlap detection for overnight shifts
      if (date_from || date_to) {
        if (date_from && date_to) {
          // Both dates provided - use overlap detection
          paramCount++;
          whereClause += ` AND (
            -- Shift starts within the date range
            (ds.start_date BETWEEN $${paramCount} AND $${paramCount + 1}) OR
            -- Shift ends within the date range
            (ds.end_date BETWEEN $${paramCount} AND $${paramCount + 1}) OR
            -- Shift spans the entire date range
            (ds.start_date <= $${paramCount} AND ds.end_date >= $${paramCount + 1})
          )`;
          params.push(date_from, date_to);
          paramCount++; // Increment for the second parameter
        } else if (date_from) {
          // Only start date provided
          paramCount++;
          whereClause += ` AND (ds.start_date >= $${paramCount} OR ds.end_date >= $${paramCount})`;
          params.push(date_from);
        } else if (date_to) {
          // Only end date provided
          paramCount++;
          whereClause += ` AND (ds.start_date <= $${paramCount} OR ds.end_date <= $${paramCount})`;
          params.push(date_to);
        }
      }

      if (truck_id) {
        paramCount++;
        whereClause += ` AND ds.truck_id = $${paramCount}`;
        params.push(truck_id);
      }

      if (status !== 'all') {
        paramCount++;
        whereClause += ` AND ds.status = $${paramCount}`;
        params.push(status);
      }

      // Validate sort parameters
      const validSortColumns = ['start_date', 'start_time', 'end_date', 'end_time', 'driver_name', 'truck_number'];
      const validSortOrders = ['asc', 'desc'];

      const sortColumn = validSortColumns.includes(sort_by) ? sort_by : 'start_date';
      const sortOrder = validSortOrders.includes(sort_order.toLowerCase()) ? sort_order.toLowerCase() : 'desc';

      // Main query
      const attendanceQuery = `
        SELECT 
          ds.id as shift_id,
          ds.driver_id,
          d.employee_id,
          d.full_name as driver_name,
          ds.truck_id,
          dt.truck_number,
          ds.start_date,
          ds.start_time,
          ds.end_date,
          ds.end_time,
          ds.status,
          ds.shift_type,
          ds.auto_created,
          ds.handover_notes,
          ds.completion_notes,
          ds.created_at,
          ds.updated_at,
          -- Calculate duration in the query for better performance
          CASE 
            WHEN ds.status = 'completed' AND ds.end_date IS NOT NULL AND ds.end_time IS NOT NULL
            THEN EXTRACT(EPOCH FROM (
              (ds.end_date + ds.end_time) - (ds.start_date + ds.start_time)
            ))
            ELSE NULL
          END as duration_seconds
        FROM driver_shifts ds
        JOIN drivers d ON ds.driver_id = d.id
        JOIN dump_trucks dt ON ds.truck_id = dt.id
        ${whereClause}
        ORDER BY ${sortColumn === 'driver_name' ? 'd.full_name' :
          sortColumn === 'truck_number' ? 'dt.truck_number' :
            'ds.' + sortColumn} ${sortOrder}
        LIMIT $${paramCount + 1} OFFSET $${paramCount + 2}
      `;

      params.push(limit, offset);

      const result = await query(attendanceQuery, params);

      // Get total count for pagination
      const countQuery = `
        SELECT COUNT(*) as total
        FROM driver_shifts ds
        JOIN drivers d ON ds.driver_id = d.id
        JOIN dump_trucks dt ON ds.truck_id = dt.id
        ${whereClause}
      `;

      const countResult = await query(countQuery, params.slice(0, paramCount));
      const totalRecords = parseInt(countResult.rows[0].total);

      // Process records and calculate durations
      const processedRecords = result.rows.map(record => {
        let durationInfo = { duration_formatted: null, total_hours: null };

        if (record.duration_seconds !== null) {
          const hours = Math.floor(record.duration_seconds / 3600);
          const minutes = Math.floor((record.duration_seconds % 3600) / 60);
          durationInfo = {
            duration_formatted: `${hours}h ${minutes}m`,
            total_hours: parseFloat((record.duration_seconds / 3600).toFixed(2))
          };
        }

        return {
          ...record,
          ...durationInfo,
          is_active: record.status === 'active',
          is_overnight: record.start_date !== record.end_date
        };
      });

      return {
        success: true,
        data: {
          records: processedRecords,
          pagination: {
            total: totalRecords,
            limit,
            offset,
            has_more: offset + limit < totalRecords,
            current_page: Math.floor(offset / limit) + 1,
            total_pages: Math.ceil(totalRecords / limit)
          },
          filters: {
            driver_id,
            date_from,
            date_to,
            truck_id,
            status,
            sort_by: sortColumn,
            sort_order: sortOrder
          }
        }
      };

    } catch (error) {
      logError('GET_ATTENDANCE_RECORDS_ERROR', error, { filters });
      throw error;
    }
  }

  /**
   * Generate attendance summary for a specific period
   * @param {string} period - 'daily', 'weekly', 'monthly'
   * @param {number} driverId - Optional driver ID filter
   * @param {string} startDate - Start date for the period
   * @param {string} endDate - End date for the period
   * @returns {Promise<Object>} Attendance summary
   */
  static async generateAttendanceSummary(period, driverId = null, startDate = null, endDate = null) {
    try {
      console.log('ATTENDANCE_SUMMARY_DEBUG: Input params:', { period, driverId, startDate, endDate });

      // Set default date range if not provided
      const now = new Date();
      let defaultStartDate, defaultEndDate;

      switch (period) {
        case 'daily':
          defaultStartDate = now.toISOString().split('T')[0];
          defaultEndDate = defaultStartDate;
          break;
        case 'weekly':
          const weekStart = new Date(now);
          weekStart.setDate(now.getDate() - now.getDay()); // Start of week (Sunday)
          defaultStartDate = weekStart.toISOString().split('T')[0];

          const weekEnd = new Date(weekStart);
          weekEnd.setDate(weekStart.getDate() + 6); // End of week (Saturday)
          defaultEndDate = weekEnd.toISOString().split('T')[0];
          break;
        case 'monthly':
          defaultStartDate = new Date(now.getFullYear(), now.getMonth(), 1).toISOString().split('T')[0];
          defaultEndDate = new Date(now.getFullYear(), now.getMonth() + 1, 0).toISOString().split('T')[0];
          break;
        default:
          throw new Error('Invalid period. Must be daily, weekly, or monthly');
      }

      const finalStartDate = startDate || defaultStartDate;
      const finalEndDate = endDate || defaultEndDate;

      console.log('ATTENDANCE_SUMMARY_DEBUG: Date range:', { finalStartDate, finalEndDate });

      // Build query with optional driver filter - Include all shifts, not just completed
      // Enhanced date filtering with overlap detection for overnight shifts
      let whereClause = `WHERE (
        -- Shift starts within the date range
        (ds.start_date BETWEEN $1 AND $2) OR
        -- Shift ends within the date range
        (ds.end_date BETWEEN $1 AND $2) OR
        -- Shift spans the entire date range
        (ds.start_date <= $1 AND ds.end_date >= $2)
      )`;
      let params = [finalStartDate, finalEndDate];

      if (driverId) {
        whereClause += ' AND ds.driver_id = $3';
        params.push(driverId);
      }

      const summaryQuery = `
        SELECT 
          d.id as driver_id,
          d.employee_id,
          d.full_name as driver_name,
          COUNT(ds.id) as total_shifts,
          SUM(EXTRACT(EPOCH FROM (
            (ds.end_date + ds.end_time) - (ds.start_date + ds.start_time)
          )) / 3600) as total_hours,
          AVG(EXTRACT(EPOCH FROM (
            (ds.end_date + ds.end_time) - (ds.start_date + ds.start_time)
          )) / 3600) as average_hours_per_shift,
          MIN(ds.start_date) as first_shift_date,
          MAX(ds.end_date) as last_shift_date,
          COUNT(DISTINCT ds.truck_id) as trucks_worked,
          COUNT(CASE WHEN ds.auto_created = true THEN 1 END) as auto_created_shifts,
          COUNT(CASE WHEN ds.start_date != ds.end_date THEN 1 END) as overnight_shifts
        FROM driver_shifts ds
        JOIN drivers d ON ds.driver_id = d.id
        ${whereClause}
        GROUP BY d.id, d.employee_id, d.full_name
        ORDER BY total_hours DESC
      `;

      console.log('ATTENDANCE_SUMMARY_DEBUG: Executing query with params:', params);
      const summaryResult = await query(summaryQuery, params);
      console.log('ATTENDANCE_SUMMARY_DEBUG: Summary result count:', summaryResult.rows.length);

      // Get overall statistics
      const overallStatsQuery = `
        SELECT 
          COUNT(ds.id) as total_shifts,
          COUNT(DISTINCT ds.driver_id) as active_drivers,
          COUNT(DISTINCT ds.truck_id) as trucks_used,
          SUM(EXTRACT(EPOCH FROM (
            (ds.end_date + ds.end_time) - (ds.start_date + ds.start_time)
          )) / 3600) as total_hours,
          AVG(EXTRACT(EPOCH FROM (
            (ds.end_date + ds.end_time) - (ds.start_date + ds.start_time)
          )) / 3600) as average_shift_duration
        FROM driver_shifts ds
        ${whereClause}
      `;

      const overallResult = await query(overallStatsQuery, params);
      const overallStats = overallResult.rows[0];

      // Format the results
      const driverSummaries = summaryResult.rows.map(row => {
        const totalHours = parseFloat(row.total_hours) || 0;
        const avgHours = parseFloat(row.average_hours_per_shift) || 0;

        return {
          driver_id: row.driver_id,
          employee_id: row.employee_id,
          driver_name: row.driver_name,
          total_shifts: parseInt(row.total_shifts),
          total_hours: parseFloat(totalHours.toFixed(2)),
          total_hours_formatted: totalHours > 0 ? `${Math.floor(totalHours)}h ${Math.floor((totalHours % 1) * 60)}m` : '0h 0m',
          average_hours_per_shift: parseFloat(avgHours.toFixed(2)),
          first_shift_date: row.first_shift_date,
          last_shift_date: row.last_shift_date,
          trucks_worked: parseInt(row.trucks_worked),
          auto_created_shifts: parseInt(row.auto_created_shifts),
          overnight_shifts: parseInt(row.overnight_shifts)
        };
      });

      return {
        success: true,
        data: {
          period,
          date_range: {
            start_date: finalStartDate,
            end_date: finalEndDate
          },
          overall_stats: {
            total_shifts: parseInt(overallStats.total_shifts || 0),
            active_drivers: parseInt(overallStats.active_drivers || 0),
            trucks_used: parseInt(overallStats.trucks_used || 0),
            total_hours: parseFloat((parseFloat(overallStats.total_hours) || 0).toFixed(2)),
            total_hours_formatted: overallStats.total_hours ?
              `${Math.floor(parseFloat(overallStats.total_hours))}h ${Math.floor((parseFloat(overallStats.total_hours) % 1) * 60)}m` : '0h 0m',
            average_shift_duration: parseFloat((parseFloat(overallStats.average_shift_duration) || 0).toFixed(2))
          },
          driver_summaries: driverSummaries,
          generated_at: new Date().toISOString()
        }
      };

    } catch (error) {
      logError('GENERATE_ATTENDANCE_SUMMARY_ERROR', error, {
        period,
        driver_id: driverId,
        start_date: startDate,
        end_date: endDate
      });
      throw error;
    }
  }

  /**
   * Get driver attendance for multiple shifts per day tracking
   * @param {number} driverId - Driver ID
   * @param {string} date - Date to check (YYYY-MM-DD)
   * @returns {Promise<Object>} Daily attendance details
   */
  static async getDailyAttendance(driverId, date) {
    try {
      const shiftsQuery = `
        SELECT 
          ds.id as shift_id,
          ds.truck_id,
          dt.truck_number,
          ds.start_time,
          ds.end_time,
          ds.status,
          ds.shift_type,
          ds.auto_created,
          ds.handover_notes,
          ds.completion_notes,
          CASE 
            WHEN ds.status = 'completed' AND ds.end_time IS NOT NULL
            THEN EXTRACT(EPOCH FROM (ds.end_time - ds.start_time)) / 3600
            ELSE NULL
          END as duration_hours
        FROM driver_shifts ds
        JOIN dump_trucks dt ON ds.truck_id = dt.id
        WHERE ds.driver_id = $1 AND ds.start_date = $2
        ORDER BY ds.start_time ASC
      `;

      const shiftsResult = await query(shiftsQuery, [driverId, date]);

      // Calculate daily totals
      let totalHours = 0;
      let completedShifts = 0;
      let activeShifts = 0;

      const shifts = shiftsResult.rows.map(shift => {
        if (shift.duration_hours) {
          totalHours += shift.duration_hours;
          completedShifts++;
        }

        if (shift.status === 'active') {
          activeShifts++;
        }

        return {
          ...shift,
          duration_formatted: shift.duration_hours ?
            `${Math.floor(shift.duration_hours)}h ${Math.floor((shift.duration_hours % 1) * 60)}m` :
            (shift.status === 'active' ? 'In Progress' : 'Not Completed')
        };
      });

      return {
        success: true,
        data: {
          driver_id: driverId,
          date,
          shifts,
          daily_summary: {
            total_shifts: shifts.length,
            completed_shifts: completedShifts,
            active_shifts: activeShifts,
            total_hours: parseFloat(totalHours.toFixed(2)),
            total_hours_formatted: `${Math.floor(totalHours)}h ${Math.floor((totalHours % 1) * 60)}m`,
            trucks_worked: [...new Set(shifts.map(s => s.truck_id))].length
          }
        }
      };

    } catch (error) {
      logError('GET_DAILY_ATTENDANCE_ERROR', error, {
        driver_id: driverId,
        date
      });
      throw error;
    }
  }

  /**
   * Export attendance data for payroll processing
   * @param {Object} filters - Export filters
   * @returns {Promise<Object>} Formatted data for export
   */
  static async exportAttendanceForPayroll(filters = {}) {
    try {
      // Use all provided filters, don't override status unless not specified
      const exportFilters = {
        ...filters,
        limit: 10000, // Large limit for export
        offset: 0     // Start from beginning for export
      };

      // If no status filter is provided, default to 'all' to respect user's choice
      if (!exportFilters.status) {
        exportFilters.status = 'all';
      }

      const attendanceData = await this.getAttendanceRecords(exportFilters);

      // Check if we got data in the expected format
      const records = attendanceData?.data?.records || attendanceData?.records || [];
      const filtersUsed = attendanceData?.data?.filters || attendanceData?.filters || exportFilters;

      // Format for export with comprehensive data
      const exportData = records.map(record => ({
        employee_id: record.employee_id,
        driver_name: record.driver_name,
        date: record.start_date,
        end_date: record.end_date,
        start_time: record.start_time,
        end_time: record.end_time,
        status: record.status,
        total_hours: record.total_hours || 0,
        duration_formatted: record.duration_formatted || 'N/A',
        truck_number: record.truck_number,
        shift_type: record.shift_type,
        is_overnight: record.is_overnight ? 'Yes' : 'No',
        auto_created: record.auto_created ? 'Yes' : 'No',
        handover_notes: record.handover_notes || '',
        completion_notes: record.completion_notes || '',
        created_at: record.created_at,
        updated_at: record.updated_at
      }));

      return {
        success: true,
        data: {
          export_date: new Date().toISOString(),
          filters: filtersUsed,
          total_records: exportData.length,
          payroll_records: exportData
        }
      };

    } catch (error) {
      logError('EXPORT_ATTENDANCE_ERROR', error, { filters });
      throw error;
    }
  }
}

module.exports = DriverAttendanceService;