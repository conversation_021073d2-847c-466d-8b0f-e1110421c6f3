/**
 * Data Flow Validation Routes
 * 
 * Provides API endpoints for validating the complete data flow chain:
 * Shift Management → Assignment Management → Trip Monitoring
 */

const express = require('express');
const router = express.Router();
const auth = require('../middleware/auth');
const DataFlowValidationService = require('../services/DataFlowValidationService');
const StatusSynchronizationService = require('../services/StatusSynchronizationService');
const statusSyncMonitor = require('../services/StatusSynchronizationMonitor');
const EndToEndDataFlowTest = require('../utils/EndToEndDataFlowTest');
const { logInfo, logError } = require('../utils/logger');

/**
 * @route   GET /api/data-flow-validation/validate
 * @desc    Validate complete data flow chain
 * @access  Private (Admin/Supervisor)
 */
router.get('/validate', auth, async (req, res) => {
  try {
    // Check user permissions
    if (!['admin', 'supervisor'].includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        error: 'Insufficient permissions',
        message: 'Only administrators and supervisors can access data flow validation'
      });
    }

    logInfo('DATA_FLOW_VALIDATION_REQUEST', 'Data flow validation requested', {
      user_id: req.user.id,
      username: req.user.username,
      ip_address: req.ip
    });

    const validationResults = await DataFlowValidationService.validateCompleteDataFlow();

    res.json({
      success: true,
      message: 'Data flow validation completed',
      data: validationResults
    });

  } catch (error) {
    logError('DATA_FLOW_VALIDATION_ERROR', error, {
      user_id: req.user?.id,
      ip_address: req.ip
    });

    res.status(500).json({
      success: false,
      error: 'Validation Error',
      message: 'Failed to validate data flow chain'
    });
  }
});

/**
 * @route   GET /api/data-flow-validation/sync-status
 * @desc    Monitor status synchronization across all systems
 * @access  Private (Admin/Supervisor)
 */
router.get('/sync-status', auth, async (req, res) => {
  try {
    // Check user permissions
    if (!['admin', 'supervisor'].includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        error: 'Insufficient permissions',
        message: 'Only administrators and supervisors can access synchronization monitoring'
      });
    }

    logInfo('SYNC_STATUS_REQUEST', 'Status synchronization monitoring requested', {
      user_id: req.user.id,
      username: req.user.username,
      ip_address: req.ip
    });

    const monitoringResults = await StatusSynchronizationService.monitorStatusSynchronization();

    res.json({
      success: true,
      message: 'Status synchronization monitoring completed',
      data: monitoringResults
    });

  } catch (error) {
    logError('SYNC_STATUS_ERROR', error, {
      user_id: req.user?.id,
      ip_address: req.ip
    });

    res.status(500).json({
      success: false,
      error: 'Monitoring Error',
      message: 'Failed to monitor status synchronization'
    });
  }
});

/**
 * @route   GET /api/data-flow-validation/monitor-status
 * @desc    Get status synchronization monitor status and metrics
 * @access  Private (Admin/Supervisor)
 */
router.get('/monitor-status', auth, async (req, res) => {
  try {
    // Check user permissions
    if (!['admin', 'supervisor'].includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        error: 'Insufficient permissions',
        message: 'Only administrators and supervisors can access monitoring status'
      });
    }

    const monitorStatus = statusSyncMonitor.getStatus();

    res.json({
      success: true,
      message: 'Status synchronization monitor status retrieved',
      data: monitorStatus
    });

  } catch (error) {
    logError('MONITOR_STATUS_ERROR', error, {
      user_id: req.user?.id,
      ip_address: req.ip
    });

    res.status(500).json({
      success: false,
      error: 'Monitor Status Error',
      message: 'Failed to get monitor status'
    });
  }
});

/**
 * @route   POST /api/data-flow-validation/force-monitor-check
 * @desc    Force a manual status synchronization monitoring check
 * @access  Private (Admin/Supervisor)
 */
router.post('/force-monitor-check', auth, async (req, res) => {
  try {
    // Check user permissions
    if (!['admin', 'supervisor'].includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        error: 'Insufficient permissions',
        message: 'Only administrators and supervisors can force monitoring checks'
      });
    }

    logInfo('FORCE_MONITOR_CHECK', 'Manual status synchronization check requested', {
      user_id: req.user.id,
      username: req.user.username,
      ip_address: req.ip
    });

    const checkResults = await statusSyncMonitor.forceCheck();

    res.json({
      success: true,
      message: 'Manual status synchronization check completed',
      data: checkResults
    });

  } catch (error) {
    logError('FORCE_MONITOR_CHECK_ERROR', error, {
      user_id: req.user?.id,
      ip_address: req.ip
    });

    res.status(500).json({
      success: false,
      error: 'Monitor Check Error',
      message: 'Failed to perform manual monitoring check'
    });
  }
});

/**
 * @route   GET /api/data-flow-validation/alerts
 * @desc    Get status synchronization alerts
 * @access  Private (Admin/Supervisor)
 */
router.get('/alerts', auth, async (req, res) => {
  try {
    // Check user permissions
    if (!['admin', 'supervisor'].includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        error: 'Insufficient permissions',
        message: 'Only administrators and supervisors can access alerts'
      });
    }

    const { alert_id } = req.query;
    const alerts = statusSyncMonitor.getAlerts(alert_id);

    res.json({
      success: true,
      message: alert_id ? 'Alert details retrieved' : 'Alerts retrieved',
      data: alerts
    });

  } catch (error) {
    logError('ALERTS_ERROR', error, {
      user_id: req.user?.id,
      ip_address: req.ip
    });

    res.status(500).json({
      success: false,
      error: 'Alerts Error',
      message: 'Failed to retrieve alerts'
    });
  }
});

/**
 * @route   POST /api/data-flow-validation/start-monitor
 * @desc    Start the status synchronization monitor
 * @access  Private (Admin only)
 */
router.post('/start-monitor', auth, async (req, res) => {
  try {
    // Check user permissions
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        error: 'Insufficient permissions',
        message: 'Only administrators can start/stop monitoring services'
      });
    }

    const { interval } = req.body;
    const checkInterval = interval && interval >= 30000 ? interval : 60000; // Minimum 30 seconds

    statusSyncMonitor.start(checkInterval);

    logInfo('START_MONITOR', 'Status synchronization monitor started', {
      user_id: req.user.id,
      username: req.user.username,
      check_interval_ms: checkInterval,
      ip_address: req.ip
    });

    res.json({
      success: true,
      message: 'Status synchronization monitor started',
      data: {
        check_interval_ms: checkInterval,
        check_interval_minutes: checkInterval / 60000
      }
    });

  } catch (error) {
    logError('START_MONITOR_ERROR', error, {
      user_id: req.user?.id,
      ip_address: req.ip
    });

    res.status(500).json({
      success: false,
      error: 'Start Monitor Error',
      message: 'Failed to start monitoring service'
    });
  }
});

/**
 * @route   POST /api/data-flow-validation/stop-monitor
 * @desc    Stop the status synchronization monitor
 * @access  Private (Admin only)
 */
router.post('/stop-monitor', auth, async (req, res) => {
  try {
    // Check user permissions
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        error: 'Insufficient permissions',
        message: 'Only administrators can start/stop monitoring services'
      });
    }

    statusSyncMonitor.stop();

    logInfo('STOP_MONITOR', 'Status synchronization monitor stopped', {
      user_id: req.user.id,
      username: req.user.username,
      ip_address: req.ip
    });

    res.json({
      success: true,
      message: 'Status synchronization monitor stopped'
    });

  } catch (error) {
    logError('STOP_MONITOR_ERROR', error, {
      user_id: req.user?.id,
      ip_address: req.ip
    });

    res.status(500).json({
      success: false,
      error: 'Stop Monitor Error',
      message: 'Failed to stop monitoring service'
    });
  }
});

/**
 * @route   POST /api/data-flow-validation/end-to-end-test
 * @desc    Run end-to-end data flow test
 * @access  Private (Admin only)
 */
router.post('/end-to-end-test', auth, async (req, res) => {
  try {
    // Check user permissions - only admins can run tests
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        error: 'Insufficient permissions',
        message: 'Only administrators can run end-to-end tests'
      });
    }

    const { scenario, scenarios } = req.body;

    logInfo('END_TO_END_TEST_REQUEST', 'End-to-end test requested', {
      user_id: req.user.id,
      username: req.user.username,
      scenario: scenario,
      scenarios: scenarios,
      ip_address: req.ip
    });

    let testResults;

    if (scenarios && Array.isArray(scenarios)) {
      // Run multiple scenarios
      testResults = await EndToEndDataFlowTest.runMultipleScenarios(scenarios);
    } else {
      // Run single scenario
      const testConfig = { scenario: scenario || 'standard_test' };
      testResults = await EndToEndDataFlowTest.runEndToEndTest(testConfig);
    }

    res.json({
      success: true,
      message: 'End-to-end test completed',
      data: testResults
    });

  } catch (error) {
    logError('END_TO_END_TEST_ERROR', error, {
      user_id: req.user?.id,
      ip_address: req.ip,
      scenario: req.body?.scenario,
      scenarios: req.body?.scenarios
    });

    res.status(500).json({
      success: false,
      error: 'Test Error',
      message: 'Failed to run end-to-end test'
    });
  }
});

/**
 * @route   GET /api/data-flow-validation/queries
 * @desc    Get validation queries for manual verification
 * @access  Private (Admin/Supervisor)
 */
router.get('/queries', auth, async (req, res) => {
  try {
    // Check user permissions
    if (!['admin', 'supervisor'].includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        error: 'Insufficient permissions',
        message: 'Only administrators and supervisors can access validation queries'
      });
    }

    const queries = DataFlowValidationService.getValidationQueries();

    res.json({
      success: true,
      message: 'Validation queries retrieved',
      data: {
        queries: queries,
        description: 'SQL queries for manual data flow validation',
        usage: 'Run these queries directly against the database to verify data consistency'
      }
    });

  } catch (error) {
    logError('VALIDATION_QUERIES_ERROR', error, {
      user_id: req.user?.id,
      ip_address: req.ip
    });

    res.status(500).json({
      success: false,
      error: 'Query Error',
      message: 'Failed to retrieve validation queries'
    });
  }
});

/**
 * @route   GET /api/data-flow-validation/sync-report
 * @desc    Generate comprehensive synchronization report
 * @access  Private (Admin/Supervisor)
 */
router.get('/sync-report', auth, async (req, res) => {
  try {
    // Check user permissions
    if (!['admin', 'supervisor'].includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        error: 'Insufficient permissions',
        message: 'Only administrators and supervisors can access synchronization reports'
      });
    }

    logInfo('SYNC_REPORT_REQUEST', 'Synchronization report requested', {
      user_id: req.user.id,
      username: req.user.username,
      ip_address: req.ip
    });

    // Get monitoring results
    const monitoringResults = await StatusSynchronizationService.monitorStatusSynchronization();
    
    // Generate comprehensive report
    const report = StatusSynchronizationService.generateSyncReport(monitoringResults);
    
    // Create alerts
    const alerts = await StatusSynchronizationService.createSyncAlerts(monitoringResults);

    res.json({
      success: true,
      message: 'Synchronization report generated',
      data: {
        report: report,
        alerts: alerts,
        raw_monitoring_data: monitoringResults
      }
    });

  } catch (error) {
    logError('SYNC_REPORT_ERROR', error, {
      user_id: req.user?.id,
      ip_address: req.ip
    });

    res.status(500).json({
      success: false,
      error: 'Report Error',
      message: 'Failed to generate synchronization report'
    });
  }
});

/**
 * @route   GET /api/data-flow-validation/health
 * @desc    Health check for data flow validation system
 * @access  Public
 */
router.get('/health', (req, res) => {
  res.json({
    success: true,
    service: 'Data Flow Validation API',
    status: 'healthy',
    timestamp: new Date().toISOString(),
    available_endpoints: [
      'GET /validate - Validate complete data flow chain',
      'GET /sync-status - Monitor status synchronization',
      'GET /monitor-status - Get monitoring service status and metrics',
      'POST /force-monitor-check - Force manual monitoring check',
      'GET /alerts - Get status synchronization alerts',
      'POST /start-monitor - Start monitoring service (Admin only)',
      'POST /stop-monitor - Stop monitoring service (Admin only)',
      'POST /end-to-end-test - Run end-to-end tests',
      'GET /queries - Get validation queries',
      'GET /sync-report - Generate synchronization report'
    ]
  });
});

module.exports = router;