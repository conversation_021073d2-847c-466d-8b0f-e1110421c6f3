#!/usr/bin/env node

/**
 * Trip Logs Data Quality Validation Script
 * 
 * This script validates the completeness and quality of trip_logs field population
 * and can be run manually or scheduled as a cron job for continuous monitoring.
 * 
 * Usage:
 *   node server/scripts/validate-trip-logs-data-quality.js [options]
 * 
 * Options:
 *   --hours <number>     Time range in hours to analyze (default: 24)
 *   --alerts             Generate alerts for quality issues (default: false)
 *   --output <format>    Output format: json, table, summary (default: summary)
 *   --thresholds <file>  JSON file with custom alert thresholds
 *   --help               Show help information
 */

const path = require('path');
const fs = require('fs');

// Add the server directory to the module path
process.env.NODE_PATH = path.join(__dirname, '..');
require('module')._initPaths();

const TripLogsValidationService = require('../services/TripLogsValidationService');
const { logger } = require('../utils/logger');

// Command line argument parsing
function parseArguments() {
  const args = process.argv.slice(2);
  const options = {
    hours: 24,
    alerts: false,
    output: 'summary',
    thresholds: null,
    help: false
  };

  for (let i = 0; i < args.length; i++) {
    switch (args[i]) {
      case '--hours':
        options.hours = parseInt(args[++i]) || 24;
        break;
      case '--alerts':
        options.alerts = true;
        break;
      case '--output':
        options.output = args[++i] || 'summary';
        break;
      case '--thresholds':
        options.thresholds = args[++i];
        break;
      case '--help':
        options.help = true;
        break;
    }
  }

  return options;
}

// Display help information
function showHelp() {
  console.log(`
Trip Logs Data Quality Validation Script

Usage: node server/scripts/validate-trip-logs-data-quality.js [options]

Options:
  --hours <number>     Time range in hours to analyze (default: 24)
  --alerts             Generate alerts for quality issues (default: false)
  --output <format>    Output format: json, table, summary (default: summary)
  --thresholds <file>  JSON file with custom alert thresholds
  --help               Show this help information

Examples:
  # Basic validation for last 24 hours
  node server/scripts/validate-trip-logs-data-quality.js

  # Validation with alerts for last 6 hours
  node server/scripts/validate-trip-logs-data-quality.js --hours 6 --alerts

  # JSON output for integration with monitoring systems
  node server/scripts/validate-trip-logs-data-quality.js --output json

  # Custom thresholds from file
  node server/scripts/validate-trip-logs-data-quality.js --alerts --thresholds ./custom-thresholds.json

Alert Thresholds (default values):
  - Driver info success rate: 95%
  - Notes quality success rate: 90%
  - Location sequence success rate: 95%
  - Overall completeness rate: 98%
`);
}

// Load custom thresholds from file
function loadThresholds(thresholdsFile) {
  try {
    if (!fs.existsSync(thresholdsFile)) {
      console.error(`Thresholds file not found: ${thresholdsFile}`);
      process.exit(1);
    }

    const thresholds = JSON.parse(fs.readFileSync(thresholdsFile, 'utf8'));
    console.log(`Loaded custom thresholds from: ${thresholdsFile}`);
    return thresholds;
  } catch (error) {
    console.error(`Error loading thresholds file: ${error.message}`);
    process.exit(1);
  }
}

// Format output based on requested format
function formatOutput(report, format) {
  switch (format.toLowerCase()) {
    case 'json':
      return JSON.stringify(report, null, 2);
    
    case 'table':
      return formatTableOutput(report);
    
    case 'summary':
    default:
      return formatSummaryOutput(report);
  }
}

// Format summary output for human readability
function formatSummaryOutput(report) {
  const summary = report.overall_summary;
  
  let output = `
=== Trip Logs Data Quality Validation Report ===
Validation Time: ${report.validation_timestamp}
Time Range: Last ${report.time_range_hours} hours
Total Trips Analyzed: ${summary.total_trips_analyzed}
Execution Time: ${report.execution_time_ms}ms

=== Success Rates ===
Driver Information: ${summary.driver_info_success_rate}
Notes Quality: ${summary.notes_quality_success_rate}
Location Sequence: ${summary.location_sequence_success_rate}
Overall Completeness: ${summary.overall_completeness_rate}

=== Detailed Results ===
`;

  // Driver validation details
  const driverValidation = report.detailed_results.driver_validation;
  output += `
Driver Information Validation:
  - Complete: ${driverValidation.complete_driver_info}/${driverValidation.total_trips}
  - Missing: ${driverValidation.missing_driver_info}
`;
  
  if (Object.keys(driverValidation.missing_breakdown).length > 0) {
    output += `  - Missing breakdown:\n`;
    Object.entries(driverValidation.missing_breakdown).forEach(([field, count]) => {
      output += `    * ${field}: ${count}\n`;
    });
  }

  // Notes validation details
  const notesValidation = report.detailed_results.notes_validation;
  output += `
Notes Quality Validation:
  - Valid: ${notesValidation.valid_notes}/${notesValidation.total_trips}
  - Invalid: ${notesValidation.invalid_notes}
  - Average length: ${notesValidation.average_notes_length} characters
`;

  if (Object.keys(notesValidation.notes_breakdown).length > 0) {
    output += `  - Issues breakdown:\n`;
    Object.entries(notesValidation.notes_breakdown).forEach(([issue, count]) => {
      output += `    * ${issue}: ${count}\n`;
    });
  }

  // Sequence validation details
  const sequenceValidation = report.detailed_results.sequence_validation;
  output += `
Location Sequence Validation:
  - Valid: ${sequenceValidation.valid_sequences}/${sequenceValidation.total_trips}
  - Invalid: ${sequenceValidation.invalid_sequences}
`;

  if (Object.keys(sequenceValidation.workflow_breakdown).length > 0) {
    output += `  - Workflow types:\n`;
    Object.entries(sequenceValidation.workflow_breakdown).forEach(([workflow, count]) => {
      output += `    * ${workflow}: ${count}\n`;
    });
  }

  // Missing fields details
  const missingFieldsValidation = report.detailed_results.missing_fields_validation;
  output += `
Missing Fields Analysis:
  - Complete: ${missingFieldsValidation.complete_trips}/${missingFieldsValidation.total_trips}
  - Incomplete: ${missingFieldsValidation.incomplete_trips}
`;

  if (Object.keys(missingFieldsValidation.field_missing_counts).length > 0) {
    output += `  - Field missing counts:\n`;
    Object.entries(missingFieldsValidation.field_missing_counts).forEach(([field, count]) => {
      output += `    * ${field}: ${count}\n`;
    });
  }

  return output;
}

// Format table output for structured display
function formatTableOutput(report) {
  const summary = report.overall_summary;
  
  let output = `
Trip Logs Data Quality Validation Report
========================================
Validation Time: ${report.validation_timestamp}
Time Range: ${report.time_range_hours} hours
Total Trips: ${summary.total_trips_analyzed}
Execution Time: ${report.execution_time_ms}ms

Metric                    | Success Rate | Details
--------------------------|--------------|------------------
Driver Information        | ${summary.driver_info_success_rate.padEnd(12)} | ${report.detailed_results.driver_validation.complete_driver_info}/${report.detailed_results.driver_validation.total_trips} complete
Notes Quality             | ${summary.notes_quality_success_rate.padEnd(12)} | ${report.detailed_results.notes_validation.valid_notes}/${report.detailed_results.notes_validation.total_trips} valid
Location Sequence         | ${summary.location_sequence_success_rate.padEnd(12)} | ${report.detailed_results.sequence_validation.valid_sequences}/${report.detailed_results.sequence_validation.total_trips} valid
Overall Completeness      | ${summary.overall_completeness_rate.padEnd(12)} | ${report.detailed_results.missing_fields_validation.complete_trips}/${report.detailed_results.missing_fields_validation.total_trips} complete
`;

  return output;
}

// Main execution function
async function main() {
  const options = parseArguments();

  if (options.help) {
    showHelp();
    process.exit(0);
  }

  console.log('Starting trip_logs data quality validation...');
  console.log(`Time range: ${options.hours} hours`);
  console.log(`Output format: ${options.output}`);
  console.log(`Alerts enabled: ${options.alerts}`);

  try {
    // Load custom thresholds if specified
    let thresholds = {};
    if (options.thresholds) {
      thresholds = loadThresholds(options.thresholds);
    }

    // Run comprehensive validation
    const report = await TripLogsValidationService.runComprehensiveValidation(options.hours);

    // Generate alerts if requested
    let alertsReport = null;
    if (options.alerts) {
      console.log('Generating data quality alerts...');
      alertsReport = await TripLogsValidationService.createDataQualityAlerts(thresholds);
      
      if (alertsReport.alerts_generated > 0) {
        console.log(`⚠️  ${alertsReport.alerts_generated} data quality alerts generated!`);
      } else {
        console.log('✅ No data quality alerts - all metrics within thresholds');
      }
    }

    // Output results
    const output = formatOutput(report, options.output);
    console.log(output);

    // Output alerts if generated
    if (alertsReport && options.output === 'json') {
      console.log('\n=== ALERTS ===');
      console.log(JSON.stringify(alertsReport, null, 2));
    } else if (alertsReport && alertsReport.alerts_generated > 0) {
      console.log('\n=== ALERTS ===');
      alertsReport.alerts.forEach((alert, index) => {
        console.log(`${index + 1}. [${alert.severity}] ${alert.type}`);
        console.log(`   ${alert.message}`);
        if (alert.details) {
          console.log(`   Details: ${JSON.stringify(alert.details, null, 2)}`);
        }
        console.log('');
      });
    }

    // Exit with appropriate code
    const exitCode = (alertsReport && alertsReport.alerts_generated > 0) ? 1 : 0;
    console.log(`Validation completed. Exit code: ${exitCode}`);
    process.exit(exitCode);

  } catch (error) {
    console.error('Error during validation:', error.message);
    if (process.env.NODE_ENV === 'development') {
      console.error('Stack trace:', error.stack);
    }
    process.exit(2);
  }
}

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught exception:', error.message);
  process.exit(3);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled rejection at:', promise, 'reason:', reason);
  process.exit(3);
});

// Run the script
if (require.main === module) {
  main();
}

module.exports = { main, parseArguments, formatOutput };