const { query, getClient } = require('../config/database');
const DriverQRCodeGenerator = require('../utils/DriverQRCodeGenerator');
const { logError, logInfo } = require('../utils/logger');
const crypto = require('crypto');

/**
 * Driver QR Service
 * Handles core business logic for driver QR code operations
 */
class DriverQRService {

  /**
   * Authenticate driver using QR code data with enhanced security
   * @param {string|Object} qrData - Driver QR code data
   * @param {Object} securityContext - Security context (IP, user agent, etc.)
   * @returns {Promise<Object>} Authentication result with driver info
   */
  static async authenticateDriver(qrData, securityContext = {}) {
    const startTime = Date.now();
    const { ip_address, user_agent, timestamp } = securityContext;
    
    try {
      // Enhanced tamper detection - check for suspicious patterns
      const tamperCheck = this._detectQRTamper(qrData);
      if (!tamperCheck.valid) {
        logError('DRIVER_QR_TAMPER_DETECTED', tamperCheck.reason, {
          qr_data_hash: this._hashSensitiveData(qrData),
          ip_address,
          user_agent,
          timestamp: timestamp || new Date().toISOString(),
          tamper_indicators: tamperCheck.indicators
        });
        
        return {
          success: false,
          error: 'AUTHENTICATION_FAILED',
          message: 'Invalid QR code detected',
          driver: null
        };
      }

      const validation = await DriverQRCodeGenerator.validateDriverQR(qrData);
      
      // Enhanced audit logging for authentication attempts
      const auditData = {
        qr_data_hash: this._hashSensitiveData(qrData),
        ip_address,
        user_agent,
        timestamp: timestamp || new Date().toISOString(),
        processing_time_ms: Date.now() - startTime,
        validation_success: validation.success,
        driver_id: validation.driver?.id,
        employee_id: validation.driver?.employee_id
      };

      if (!validation.success || !validation.valid) {
        logError('DRIVER_AUTHENTICATION_FAILED', validation.error || 'Authentication failed', auditData);
        
        return {
          success: false,
          error: 'AUTHENTICATION_FAILED',
          message: validation.error || 'Driver authentication failed',
          driver: null
        };
      }

      // Log successful authentication with security context
      logInfo('DRIVER_AUTHENTICATION_SUCCESS', `Driver ${validation.driver.employee_id} authenticated successfully`, auditData);

      return {
        success: true,
        driver: validation.driver,
        qr_data: validation.qr_data
      };

    } catch (error) {
      const auditData = {
        qr_data_hash: this._hashSensitiveData(qrData),
        ip_address,
        user_agent,
        timestamp: timestamp || new Date().toISOString(),
        processing_time_ms: Date.now() - startTime,
        error_type: error.name,
        error_message: error.message
      };
      
      logError('DRIVER_AUTHENTICATION_ERROR', error, auditData);
      
      return {
        success: false,
        error: 'AUTHENTICATION_ERROR',
        message: 'An error occurred during driver authentication',
        driver: null
      };
    }
  }

  /**
   * Validate truck QR code and check if truck is active with enhanced security
   * @param {string|Object} qrData - Truck QR code data
   * @param {Object} securityContext - Security context (IP, user agent, etc.)
   * @returns {Promise<Object>} Validation result with truck info
   */
  static async validateTruck(qrData, securityContext = {}) {
    try {
      // Parse QR data if it's a string
      let parsedData;
      if (typeof qrData === 'string') {
        try {
          parsedData = JSON.parse(qrData);
        } catch (parseError) {
          return {
            success: false,
            error: 'INVALID_QR_FORMAT',
            message: 'Truck QR code is not valid JSON format',
            truck: null
          };
        }
      } else {
        parsedData = qrData;
      }

      // Validate QR structure
      if (!parsedData || !parsedData.id || !parsedData.type || parsedData.type !== 'truck') {
        return {
          success: false,
          error: 'INVALID_QR_STRUCTURE',
          message: 'Invalid truck QR code structure',
          truck: null
        };
      }

      // Check if truck exists and is active
      const truckResult = await query(
        `SELECT id, truck_number, license_plate, qr_code_data, status
         FROM dump_trucks 
         WHERE truck_number = $1`,
        [parsedData.id]
      );

      if (truckResult.rows.length === 0) {
        return {
          success: false,
          error: 'TRUCK_NOT_FOUND',
          message: 'Truck not found',
          truck: null
        };
      }

      const truck = truckResult.rows[0];

      if (truck.status !== 'active') {
        return {
          success: false,
          error: 'TRUCK_INACTIVE',
          message: 'Truck is not available for assignment. Please contact maintenance or supervisor.',
          truck: null
        };
      }

      // Verify QR code data matches database
      const storedQrData = truck.qr_code_data;
      if (!storedQrData || storedQrData.id !== parsedData.id) {
        return {
          success: false,
          error: 'QR_DATA_MISMATCH',
          message: 'Truck QR code data mismatch with database',
          truck: null
        };
      }

      return {
        success: true,
        truck: {
          id: truck.id,
          truck_number: truck.truck_number,
          license_plate: truck.license_plate,
          status: truck.status
        },
        qr_data: parsedData
      };

    } catch (error) {
      const { ip_address, user_agent, timestamp } = securityContext;
      
      logError('TRUCK_VALIDATION_ERROR', error, {
        qr_data_hash: this._hashSensitiveData(qrData),
        ip_address,
        user_agent,
        timestamp: timestamp || new Date().toISOString(),
        error_type: error.name,
        error_message: error.message
      });
      
      return {
        success: false,
        error: 'VALIDATION_ERROR',
        message: 'An error occurred during truck validation',
        truck: null
      };
    }
  }

  /**
   * Get driver's current active shift (OPTIMIZED)
   * @param {number} driverId - Driver ID
   * @returns {Promise<Object>} Current shift info or null
   */
  static async getCurrentDriverShift(driverId) {
    try {
      // PERFORMANCE OPTIMIZATION: Use partial index for active shifts
      const result = await query(
        `SELECT ds.id, ds.truck_id, ds.start_date, ds.start_time, 
                dt.truck_number, dt.license_plate
         FROM driver_shifts ds
         JOIN dump_trucks dt ON ds.truck_id = dt.id
         WHERE ds.driver_id = $1 AND ds.status = 'active'
         ORDER BY ds.created_at DESC
         LIMIT 1`,
        [driverId]
      );

      if (result.rows.length === 0) {
        return null;
      }

      return result.rows[0];

    } catch (error) {
      logError('GET_CURRENT_SHIFT_ERROR', error, { driver_id: driverId });
      throw error;
    }
  }

  /**
   * Process driver-truck connection with database transactions and row-level locking
   * @param {number} driverId - Driver ID
   * @param {number} truckId - Truck ID
   * @param {string} action - 'check_in' or 'check_out'
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} Connection result
   */
  static async processDriverTruckConnection(driverId, truckId, action, options = {}) {
    const client = await getClient();
    
    try {
      await client.query('BEGIN');

      // Get current shift with row-level locking
      const currentShiftResult = await client.query(
        `SELECT ds.id, ds.truck_id, ds.start_date, ds.start_time, dt.truck_number
         FROM driver_shifts ds
         JOIN dump_trucks dt ON ds.truck_id = dt.id
         WHERE ds.driver_id = $1 AND ds.status = 'active'
         FOR UPDATE`,
        [driverId]
      );

      const hasActiveShift = currentShiftResult.rows.length > 0;
      const currentShift = hasActiveShift ? currentShiftResult.rows[0] : null;

      let result = {};

      if (action === 'check_in') {
        if (hasActiveShift && currentShift.truck_id !== truckId) {
          // Handover scenario: end current shift and start new one
          result = await this._handleShiftHandover(client, driverId, truckId, currentShift, options);
        } else if (!hasActiveShift) {
          // Regular check-in: create new shift
          result = await this._createNewShift(client, driverId, truckId, options);
        } else {
          // Already checked in to the same truck
          await client.query('ROLLBACK');
          return {
            success: false,
            error: 'ALREADY_CHECKED_IN',
            message: `Already checked in to ${currentShift.truck_number}`,
            current_shift: currentShift
          };
        }

      } else if (action === 'check_out') {
        if (!hasActiveShift) {
          await client.query('ROLLBACK');
          return {
            success: false,
            error: 'NO_ACTIVE_SHIFT',
            message: 'No active shift found to check out from'
          };
        }

        if (currentShift.truck_id !== truckId) {
          await client.query('ROLLBACK');
          return {
            success: false,
            error: 'TRUCK_MISMATCH',
            message: `You are currently assigned to ${currentShift.truck_number}. Please scan that truck's QR code to check out.`
          };
        }

        // Regular check-out: end current shift
        result = await this._endCurrentShift(client, currentShift, options);
      }

      await client.query('COMMIT');

      // Log successful operation
      logInfo('DRIVER_TRUCK_CONNECTION_SUCCESS', `Driver ${driverId} ${action} truck ${truckId}`, {
        driver_id: driverId,
        truck_id: truckId,
        action: action,
        shift_id: result.shift_id,
        is_handover: result.action === 'handover'
      });

      return {
        success: true,
        ...result
      };

    } catch (error) {
      await client.query('ROLLBACK');
      logError('DRIVER_TRUCK_CONNECTION_ERROR', error, {
        driver_id: driverId,
        truck_id: truckId,
        action: action
      });
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Handle automatic shift handover between drivers
   * @param {Object} client - Database client
   * @param {number} newDriverId - New driver ID
   * @param {number} truckId - Truck ID
   * @param {Object} currentShift - Current active shift
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} Handover result
   * @private
   */
  static async _handleShiftHandover(client, newDriverId, truckId, currentShift, options = {}) {
    const currentTimestamp = new Date();
    const currentTime = currentTimestamp.toTimeString().split(' ')[0];

    // FIXED: Calculate end_date correctly for overnight shifts during handover
    // Use the actual date when handover occurs, not the start_date
    const phDateFormatter = new Intl.DateTimeFormat('en-CA', { timeZone: 'Asia/Manila' });
    const endDate = phDateFormatter.format(currentTimestamp);
    const startDate = phDateFormatter.format(currentTimestamp);

    // End current shift
    await client.query(
      `UPDATE driver_shifts
       SET status = 'completed', end_date = $1, end_time = $2,
           handover_notes = $3, handover_completed_at = $4, updated_at = $5
       WHERE id = $6`,
      [
        endDate,
        currentTime,
        options.handover_notes || `Automatic handover from ${currentShift.truck_number}`,
        currentTimestamp,
        currentTimestamp,
        currentShift.id
      ]
    );

    // Create new shift for new driver
    const newShiftResult = await client.query(
      `INSERT INTO driver_shifts (
        truck_id, driver_id, shift_type, start_date, end_date,
        start_time, end_time, status, previous_shift_id, auto_created,
        created_at, updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
      RETURNING id`,
      [
        truckId, newDriverId, 'custom', startDate, null,
        currentTime, null, 'active', currentShift.id, true,
        currentTimestamp, currentTimestamp
      ]
    );

    const newShiftId = newShiftResult.rows[0].id;

    // Get truck info for response
    const truckResult = await client.query(
      'SELECT truck_number FROM dump_trucks WHERE id = $1',
      [truckId]
    );
    const newTruckNumber = truckResult.rows[0].truck_number;

    return {
      action: 'handover',
      message: `Taking over from previous assignment on ${currentShift.truck_number}`,
      previous_truck: currentShift.truck_number,
      new_truck: newTruckNumber,
      shift_id: newShiftId,
      previous_shift_id: currentShift.id,
      check_in_time: currentTimestamp.toISOString()
    };
  }

  /**
   * Create new shift for driver
   * @param {Object} client - Database client
   * @param {number} driverId - Driver ID
   * @param {number} truckId - Truck ID
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} New shift result
   * @private
   */
  static async _createNewShift(client, driverId, truckId, options = {}) {
    const currentTimestamp = new Date();
    const currentDate = new Intl.DateTimeFormat('en-CA', { timeZone: 'Asia/Manila' }).format(currentTimestamp);
    const currentTime = currentTimestamp.toTimeString().split(' ')[0];

    // Create new shift with status 'active' immediately
    const newShiftResult = await client.query(
      `INSERT INTO driver_shifts (
        truck_id, driver_id, shift_type, start_date, end_date, 
        start_time, end_time, status, auto_created, created_at, updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
      RETURNING id`,
      [
        truckId, driverId, 'custom', currentDate, null,
        currentTime, null, 'active', true, currentTimestamp, currentTimestamp
      ]
    );

    const newShiftId = newShiftResult.rows[0].id;

    // Get truck info for response
    const truckResult = await client.query(
      'SELECT truck_number FROM dump_trucks WHERE id = $1',
      [truckId]
    );
    const truckNumber = truckResult.rows[0].truck_number;

    return {
      action: 'check_in',
      message: `Checked in to ${truckNumber}`,
      truck: truckNumber,
      shift_id: newShiftId,
      check_in_time: currentTimestamp.toISOString()
    };
  }

  /**
   * End current active shift
   * @param {Object} client - Database client
   * @param {Object} currentShift - Current active shift
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} End shift result
   * @private
   */
  static async _endCurrentShift(client, currentShift, options = {}) {
    const currentTimestamp = new Date();
    const currentTime = currentTimestamp.toTimeString().split(' ')[0];

    // FIXED: Calculate end_date correctly for overnight shifts
    // Use the actual date when check-out occurs, not the start_date
    const endDate = new Intl.DateTimeFormat('en-CA', { timeZone: 'Asia/Manila' }).format(currentTimestamp);

    // End current shift
    await client.query(
      `UPDATE driver_shifts
       SET status = 'completed', end_date = $1, end_time = $2,
           completion_notes = $3, updated_at = $4
       WHERE id = $5`,
      [
        endDate,
        currentTime,
        options.completion_notes || 'Checked out via QR system',
        currentTimestamp,
        currentShift.id
      ]
    );

    // Calculate duration
    const startDateTime = new Date(`${currentShift.start_date}T${currentShift.start_time}`);
    const endDateTime = currentTimestamp;
    const durationMs = endDateTime - startDateTime;
    const durationHours = Math.floor(durationMs / (1000 * 60 * 60));
    const durationMinutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60));

    return {
      action: 'check_out',
      message: `Checked out from ${currentShift.truck_number}`,
      truck: currentShift.truck_number,
      shift_id: currentShift.id,
      check_in_time: startDateTime.toISOString(),
      check_out_time: currentTimestamp.toISOString(),
      duration: `${durationHours}h ${durationMinutes}m`,
      duration_ms: durationMs
    };
  }

  /**
   * Get driver shift history
   * @param {number} driverId - Driver ID
   * @param {Object} options - Query options (limit, offset, date_from, date_to)
   * @returns {Promise<Object>} Shift history
   */
  static async getDriverShiftHistory(driverId, options = {}) {
    try {
      const {
        limit = 50,
        offset = 0,
        date_from,
        date_to
      } = options;

      let whereClause = 'WHERE ds.driver_id = $1';
      let params = [driverId];
      let paramCount = 1;

      if (date_from) {
        paramCount++;
        whereClause += ` AND ds.start_date >= $${paramCount}`;
        params.push(date_from);
      }

      if (date_to) {
        paramCount++;
        whereClause += ` AND ds.start_date <= $${paramCount}`;
        params.push(date_to);
      }

      const result = await query(
        `SELECT ds.id, ds.truck_id, ds.shift_type, ds.start_date, ds.end_date,
                ds.start_time, ds.end_time, ds.status, ds.auto_created,
                ds.handover_notes, ds.completion_notes, ds.created_at,
                dt.truck_number, dt.license_plate,
                d.employee_id, d.full_name
         FROM driver_shifts ds
         JOIN dump_trucks dt ON ds.truck_id = dt.id
         JOIN drivers d ON ds.driver_id = d.id
         ${whereClause}
         ORDER BY ds.start_date DESC, ds.start_time DESC
         LIMIT $${paramCount + 1} OFFSET $${paramCount + 2}`,
        [...params, limit, offset]
      );

      // Calculate durations for completed shifts
      const shifts = result.rows.map(shift => {
        let duration = null;
        let duration_ms = null;

        if (shift.status === 'completed' && shift.end_date && shift.end_time) {
          const startDateTime = new Date(`${shift.start_date}T${shift.start_time}`);
          const endDateTime = new Date(`${shift.end_date}T${shift.end_time}`);
          duration_ms = endDateTime - startDateTime;
          
          const hours = Math.floor(duration_ms / (1000 * 60 * 60));
          const minutes = Math.floor((duration_ms % (1000 * 60 * 60)) / (1000 * 60));
          duration = `${hours}h ${minutes}m`;
        }

        return {
          ...shift,
          duration,
          duration_ms
        };
      });

      return {
        success: true,
        shifts,
        pagination: {
          limit,
          offset,
          total: shifts.length
        }
      };

    } catch (error) {
      logError('GET_DRIVER_SHIFT_HISTORY_ERROR', error, { driver_id: driverId, options });
      throw error;
    }
  }

  /**
   * Get active drivers for a specific truck
   * @param {number} truckId - Truck ID
   * @returns {Promise<Object>} Active driver info
   */
  static async getActiveDriverForTruck(truckId) {
    try {
      const result = await query(
        `SELECT ds.id as shift_id, ds.start_date, ds.start_time,
                d.id as driver_id, d.employee_id, d.full_name,
                dt.truck_number
         FROM driver_shifts ds
         JOIN drivers d ON ds.driver_id = d.id
         JOIN dump_trucks dt ON ds.truck_id = dt.id
         WHERE ds.truck_id = $1 AND ds.status = 'active'
         ORDER BY ds.created_at DESC
         LIMIT 1`,
        [truckId]
      );

      if (result.rows.length === 0) {
        return {
          success: true,
          has_active_driver: false,
          driver: null
        };
      }

      return {
        success: true,
        has_active_driver: true,
        driver: result.rows[0]
      };

    } catch (error) {
      logError('GET_ACTIVE_DRIVER_FOR_TRUCK_ERROR', error, { truck_id: truckId });
      throw error;
    }
  }

  /**
   * Validate if driver can perform action on truck
   * @param {number} driverId - Driver ID
   * @param {number} truckId - Truck ID
   * @param {string} action - Intended action
   * @returns {Promise<Object>} Validation result
   */
  static async validateDriverTruckAction(driverId, truckId, action) {
    try {
      const currentShift = await this.getCurrentDriverShift(driverId);
      const truckDriver = await this.getActiveDriverForTruck(truckId);

      let canPerform = true;
      let message = '';
      let suggestedAction = action;

      if (action === 'check_in') {
        if (currentShift) {
          if (currentShift.truck_id === truckId) {
            canPerform = false;
            message = `Already checked in to ${currentShift.truck_number}`;
          } else {
            // Handover scenario
            suggestedAction = 'handover';
            message = `Will handover from ${currentShift.truck_number} to truck`;
          }
        }

        if (truckDriver.has_active_driver && truckDriver.driver.driver_id !== driverId) {
          message += ` (${truckDriver.driver.full_name} will be checked out automatically)`;
        }

      } else if (action === 'check_out') {
        if (!currentShift) {
          canPerform = false;
          message = 'No active shift to check out from';
        } else if (currentShift.truck_id !== truckId) {
          canPerform = false;
          message = `Currently assigned to ${currentShift.truck_number}, not this truck`;
        }
      }

      return {
        success: true,
        can_perform: canPerform,
        message,
        suggested_action: suggestedAction,
        current_shift: currentShift,
        truck_driver: truckDriver.driver
      };

    } catch (error) {
      logError('VALIDATE_DRIVER_TRUCK_ACTION_ERROR', error, {
        driver_id: driverId,
        truck_id: truckId,
        action
      });
      throw error;
    }
  }

  /**
   * Detect potential QR code tampering
   * @param {string|Object} qrData - QR code data to check
   * @returns {Object} Tamper detection result
   * @private
   */
  static _detectQRTamper(qrData) {
    const indicators = [];
    
    try {
      // Parse QR data if string
      let parsedData;
      if (typeof qrData === 'string') {
        parsedData = JSON.parse(qrData);
      } else {
        parsedData = qrData;
      }

      // Check for suspicious field modifications
      if (parsedData.driver_id && typeof parsedData.driver_id === 'string' && parsedData.driver_id.includes('..')) {
        indicators.push('SUSPICIOUS_DRIVER_ID_PATTERN');
      }

      // Check for SQL injection patterns
      const sqlPatterns = ['union', 'select', 'drop', 'delete', 'insert', 'update', '--', ';'];
      const dataString = JSON.stringify(parsedData).toLowerCase();
      
      for (const pattern of sqlPatterns) {
        if (dataString.includes(pattern)) {
          indicators.push(`POTENTIAL_SQL_INJECTION_${pattern.toUpperCase()}`);
        }
      }

      // Check for excessive data size (potential DoS)
      if (dataString.length > 1000) {
        indicators.push('EXCESSIVE_DATA_SIZE');
      }

      // Check for unusual field count
      const fieldCount = Object.keys(parsedData).length;
      if (fieldCount > 10) {
        indicators.push('EXCESSIVE_FIELD_COUNT');
      }

      // Check for timestamp manipulation (future dates)
      if (parsedData.generated_date) {
        const generatedDate = new Date(parsedData.generated_date);
        const now = new Date();
        const oneYearFromNow = new Date(now.getTime() + (365 * 24 * 60 * 60 * 1000));
        
        if (generatedDate > oneYearFromNow) {
          indicators.push('FUTURE_TIMESTAMP_DETECTED');
        }
      }

      return {
        valid: indicators.length === 0,
        indicators,
        reason: indicators.length > 0 ? `Tamper indicators detected: ${indicators.join(', ')}` : null
      };

    } catch (error) {
      return {
        valid: false,
        indicators: ['PARSE_ERROR'],
        reason: 'Failed to parse QR data for tamper detection'
      };
    }
  }

  /**
   * Hash sensitive data for logging (to avoid exposing actual QR content)
   * @param {string|Object} data - Data to hash
   * @returns {string} SHA256 hash
   * @private
   */
  static _hashSensitiveData(data) {
    try {
      const dataString = typeof data === 'string' ? data : JSON.stringify(data);
      return crypto.createHash('sha256').update(dataString).digest('hex').substring(0, 16);
    } catch (error) {
      return 'HASH_ERROR';
    }
  }

  /**
   * Enhanced audit logging for driver operations
   * @param {string} operation - Operation type
   * @param {Object} context - Operation context
   * @param {Object} securityContext - Security context
   * @private
   */
  static _auditLog(operation, context, securityContext = {}) {
    const auditData = {
      operation,
      timestamp: new Date().toISOString(),
      ip_address: securityContext.ip_address,
      user_agent: securityContext.user_agent,
      session_id: securityContext.session_id,
      ...context
    };

    logInfo('DRIVER_OPERATION_AUDIT', `${operation} performed`, auditData);
  }
}

module.exports = DriverQRService;