const { query } = require('../config/database');
const { logError } = require('../utils/logger');

/**
 * Truck Status Validation Middleware
 * Validates truck status for PWA Driver Connect operations
 * Prevents inactive, maintenance, or retired trucks from performing shift operations
 * Mirrors the driver status validation pattern for consistency
 */

// Status-specific error messages with actionable guidance
const TRUCK_STATUS_MESSAGES = {
  inactive: "This truck is inactive. Contact your supervisor or maintenance team.",
  maintenance: "This truck is currently under maintenance. Please use a different truck or contact maintenance.",
  retired: "This truck has been retired from service. Please use a different truck."
};

// Display names for truck statuses
const TRUCK_STATUS_DISPLAY_NAMES = {
  active: "Active",
  inactive: "Inactive", 
  maintenance: "Under Maintenance",
  retired: "Retired"
};

/**
 * Log blocked truck operation attempt for security monitoring
 * @param {Object} truck - Truck object with status info
 * @param {string} operation - Operation being attempted
 */
async function logBlockedTruckOperation(truck, operation) {
  try {
    // Try to log to security_audit_log table if it exists
    await query(
      `INSERT INTO security_audit_log
       (event_type, entity_type, entity_id, details, ip_address, user_agent, created_at)
       VALUES ($1, $2, $3, $4, $5, $6, NOW())`,
      [
        'TRUCK_STATUS_BLOCKED',
        'truck',
        truck.id,
        JSON.stringify({
          truck_number: truck.truck_number,
          status: truck.status,
          operation: operation,
          blocked_at: new Date().toISOString()
        }),
        null, // IP will be added by calling middleware if available
        null  // User agent will be added by calling middleware if available
      ]
    );
  } catch (error) {
    // If security_audit_log table doesn't exist, just log the error but don't fail
    if (error.message && error.message.includes('does not exist')) {
      console.log(`[TruckStatusValidation] Security audit log table not found, skipping audit log for truck ${truck.truck_number}`);
    } else {
      logError('TRUCK_STATUS_AUDIT_LOG_ERROR', error, { truck_id: truck.id, operation });
    }
  }
}

/**
 * Validate truck status and return structured response
 * @param {string} truckNumber - Truck number (e.g., "DT-100")
 * @param {string} operation - Operation being attempted (e.g., 'check_in', 'check_out', 'qr_scan')
 * @returns {Promise<Object>} Validation result with status and error details
 */
async function validateTruckStatus(truckNumber, operation = 'shift_operation') {
  try {
    // Get truck info including current status
    const truckResult = await query(
      `SELECT id, truck_number, license_plate, status, make, model, updated_at
       FROM dump_trucks 
       WHERE truck_number = $1`,
      [truckNumber]
    );

    if (truckResult.rows.length === 0) {
      return {
        valid: false,
        error: 'TRUCK_NOT_FOUND',
        message: 'Truck not found in system.',
        status: null,
        truck: null
      };
    }

    const truck = truckResult.rows[0];

    // Check if truck status allows operations
    if (truck.status !== 'active') {
      // Log blocked operation attempt for security monitoring
      await logBlockedTruckOperation(truck, operation);

      return {
        valid: false,
        error: 'TRUCK_STATUS_BLOCKED',
        message: TRUCK_STATUS_MESSAGES[truck.status] || 'Truck status prevents operations.',
        status: truck.status,
        statusDisplayName: TRUCK_STATUS_DISPLAY_NAMES[truck.status] || truck.status,
        truck: {
          id: truck.id,
          truck_number: truck.truck_number,
          license_plate: truck.license_plate,
          status: truck.status,
          make: truck.make,
          model: truck.model,
          updated_at: truck.updated_at
        }
      };
    }

    // Truck is active - allow operation
    return {
      valid: true,
      truck: {
        id: truck.id,
        truck_number: truck.truck_number,
        license_plate: truck.license_plate,
        status: truck.status,
        make: truck.make,
        model: truck.model,
        updated_at: truck.updated_at
      }
    };

  } catch (error) {
    logError('TRUCK_STATUS_VALIDATION_ERROR', error, { truck_number: truckNumber, operation });
    
    return {
      valid: false,
      error: 'VALIDATION_ERROR',
      message: 'Unable to validate truck status. Please try again.',
      status: null,
      truck: null
    };
  }
}

/**
 * Express middleware for truck status validation
 * Validates truck status before allowing operations
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object  
 * @param {Function} next - Express next function
 */
async function validateTruckStatusMiddleware(req, res, next) {
  try {
    // Extract truck number from request (could be in body, params, or query)
    const truckNumber = req.body?.truck_qr_data?.id || 
                       req.params?.truck_number || 
                       req.query?.truck_number;

    if (!truckNumber) {
      return res.status(400).json({
        success: false,
        error: 'MISSING_TRUCK_NUMBER',
        message: 'Truck number is required for validation'
      });
    }

    // Determine operation type from request
    const operation = req.body?.action || req.method.toLowerCase() || 'truck_operation';

    // Validate truck status
    const validation = await validateTruckStatus(truckNumber, operation);

    if (!validation.valid) {
      return res.status(403).json({
        success: false,
        error: validation.error,
        message: validation.message,
        truck_status: validation.status,
        status_display_name: validation.statusDisplayName,
        truck: validation.truck,
        statusBlocked: true // Flag for client-side handling
      });
    }

    // Add validated truck info to request for downstream use
    req.validatedTruck = validation.truck;
    next();

  } catch (error) {
    logError('TRUCK_STATUS_MIDDLEWARE_ERROR', error, { 
      url: req.url, 
      method: req.method,
      body: req.body 
    });
    
    return res.status(500).json({
      success: false,
      error: 'VALIDATION_ERROR',
      message: 'Unable to validate truck status. Please try again.'
    });
  }
}

module.exports = {
  validateTruckStatus,
  validateTruckStatusMiddleware,
  TRUCK_STATUS_MESSAGES,
  TRUCK_STATUS_DISPLAY_NAMES
};
