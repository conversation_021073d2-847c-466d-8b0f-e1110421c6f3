-- Migration 017: Role-Based Access Control System
-- Creates role_permissions table and functions for managing user_role enum

-- Create role_permissions table
CREATE TABLE IF NOT EXISTS role_permissions (
    id SERIAL PRIMARY KEY,
    role_name user_role NOT NULL,
    page_key VARCHAR(100) NOT NULL,
    has_access BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(role_name, page_key)
);

-- Create index for performance
CREATE INDEX IF NOT EXISTS idx_role_permissions_role_page ON role_permissions(role_name, page_key);
CREATE INDEX IF NOT EXISTS idx_role_permissions_role ON role_permissions(role_name);

-- Function to safely add new role to user_role enum
CREATE OR REPLACE FUNCTION add_user_role_enum(new_role TEXT) 
RETURNS VOID AS $$
BEGIN
    -- Check if role already exists
    IF NOT EXISTS (
        SELECT 1 FROM pg_enum 
        WHERE enumlabel = new_role 
        AND enumtypid = 'user_role'::regtype
    ) THEN
        EXECUTE format('ALTER TYPE user_role ADD VALUE %L', new_role);
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Function to get all user_role enum values
CREATE OR REPLACE FUNCTION get_user_roles() 
RETURNS TABLE(role_name TEXT) AS $$
BEGIN
    RETURN QUERY
    SELECT enumlabel::TEXT 
    FROM pg_enum 
    WHERE enumtypid = 'user_role'::regtype
    ORDER BY enumlabel;
END;
$$ LANGUAGE plpgsql;

-- Function to check if role can be safely deleted (no users assigned)
CREATE OR REPLACE FUNCTION can_delete_user_role(role_to_check TEXT)
RETURNS BOOLEAN AS $$
DECLARE
    user_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO user_count
    FROM users 
    WHERE role = role_to_check::user_role;
    
    RETURN user_count = 0;
END;
$$ LANGUAGE plpgsql;

-- Insert default permissions for existing roles
-- Admin gets access to all pages
INSERT INTO role_permissions (role_name, page_key, has_access) VALUES
('admin', 'dashboard', true),
('admin', 'users', true),
('admin', 'trucks', true),
('admin', 'drivers', true),
('admin', 'locations', true),
('admin', 'assignments', true),
('admin', 'shifts', true),
('admin', 'trips', true),
('admin', 'scanner', true),
('admin', 'assignment_monitoring', true),
('admin', 'truck_trip_summary', true),
('admin', 'analytics', true),
('admin', 'settings', true)
ON CONFLICT (role_name, page_key) DO NOTHING;

-- Supervisor gets access to most pages except user management and settings
INSERT INTO role_permissions (role_name, page_key, has_access) VALUES
('supervisor', 'dashboard', true),
('supervisor', 'users', false),
('supervisor', 'trucks', true),
('supervisor', 'drivers', true),
('supervisor', 'locations', true),
('supervisor', 'assignments', true),
('supervisor', 'shifts', true),
('supervisor', 'trips', true),
('supervisor', 'scanner', true),
('supervisor', 'assignment_monitoring', true),
('supervisor', 'truck_trip_summary', true),
('supervisor', 'analytics', true),
('supervisor', 'settings', false)
ON CONFLICT (role_name, page_key) DO NOTHING;

-- Operator gets access to basic operational pages
INSERT INTO role_permissions (role_name, page_key, has_access) VALUES
('operator', 'dashboard', true),
('operator', 'users', false),
('operator', 'trucks', false),
('operator', 'drivers', false),
('operator', 'locations', false),
('operator', 'assignments', false),
('operator', 'shifts', false),
('operator', 'trips', true),
('operator', 'scanner', true),
('operator', 'assignment_monitoring', false),
('operator', 'truck_trip_summary', false),
('operator', 'analytics', false),
('operator', 'settings', false)
ON CONFLICT (role_name, page_key) DO NOTHING;

-- Add trigger to update updated_at timestamp
CREATE TRIGGER update_role_permissions_updated_at
    BEFORE UPDATE ON role_permissions
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Add comments for documentation
COMMENT ON TABLE role_permissions IS 'Stores page access permissions for each user role';
COMMENT ON COLUMN role_permissions.role_name IS 'User role from user_role enum';
COMMENT ON COLUMN role_permissions.page_key IS 'Unique identifier for application page/route';
COMMENT ON COLUMN role_permissions.has_access IS 'Whether the role has access to this page';
COMMENT ON FUNCTION add_user_role_enum(TEXT) IS 'Safely adds a new value to the user_role enum if it does not already exist';
COMMENT ON FUNCTION get_user_roles() IS 'Returns all values from the user_role enum';
COMMENT ON FUNCTION can_delete_user_role(TEXT) IS 'Checks if a role can be safely deleted (no users assigned to it)';