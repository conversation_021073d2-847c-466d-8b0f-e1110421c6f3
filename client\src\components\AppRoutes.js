import React from 'react';
import { Routes, Route } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import LoadingSpinner from './common/LoadingSpinner';

// Pages - will be created in subsequent steps
const LoginPage = React.lazy(() => import('../pages/auth/LoginPage'));
const DashboardLayout = React.lazy(() => import('./layout/DashboardLayout'));
const DriverConnect = React.lazy(() => import('../pages/drivers/DriverConnect'));
const TripScanner = React.lazy(() => import('../pages/trip-scanner/TripScanner'));

function AppRoutes() {
  const { isAuthenticated, loading } = useAuth();

  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <React.Suspense fallback={<LoadingSpinner />}>
      <Routes>
        {/* Public routes - accessible without authentication */}
        <Route path="/driver-connect" element={<DriverConnect />} />
        <Route path="/trip-scanner" element={<TripScanner />} />
        
        {!isAuthenticated ? (
          // Public routes for unauthenticated users
          <>
            <Route path="/login" element={<LoginPage />} />
            <Route path="*" element={<LoginPage />} />
          </>
        ) : (
          // Protected routes for authenticated users
          <>
            <Route path="/*" element={<DashboardLayout />} />
          </>
        )}
      </Routes>
    </React.Suspense>
  );
}

export default AppRoutes;