/**
 * Appearance Service
 * Handles loading and applying custom appearance settings
 */

class AppearanceService {
  constructor() {
    this.defaultSettings = {
      logo: {
        src: '',
        alt: 'Hauling QR System',
        width: 40,
        height: 40
      },
      fonts: {
        header: {
          family: 'Inter, system-ui, sans-serif',
          size: '24px',
          weight: '600'
        },
        content: {
          family: 'Inter, system-ui, sans-serif',
          size: '16px',
          weight: '400'
        },
        footer: {
          family: 'Inter, system-ui, sans-serif',
          size: '14px',
          weight: '400'
        }
      }
    };
  }

  /**
   * Load appearance settings from localStorage
   */
  loadSettings() {
    try {
      const savedSettings = localStorage.getItem('hauling_appearance_settings');
      if (savedSettings) {
        const parsed = JSON.parse(savedSettings);
        return { ...this.defaultSettings, ...parsed };
      }
    } catch (error) {
      console.error('Error loading appearance settings:', error);
    }
    return this.defaultSettings;
  }

  /**
   * Save appearance settings to localStorage
   */
  saveSettings(settings) {
    try {
      localStorage.setItem('hauling_appearance_settings', JSON.stringify(settings));
      return true;
    } catch (error) {
      console.error('Error saving appearance settings:', error);
      return false;
    }
  }

  /**
   * Apply settings to the document
   */
  applySettings(settings = null) {
    const settingsToApply = settings || this.loadSettings();
    const root = document.documentElement;
    
    try {
      // Apply font settings
      root.style.setProperty('--font-header-family', settingsToApply.fonts.header.family);
      root.style.setProperty('--font-header-size', settingsToApply.fonts.header.size);
      root.style.setProperty('--font-header-weight', settingsToApply.fonts.header.weight);
      
      root.style.setProperty('--font-content-family', settingsToApply.fonts.content.family);
      root.style.setProperty('--font-content-size', settingsToApply.fonts.content.size);
      root.style.setProperty('--font-content-weight', settingsToApply.fonts.content.weight);
      
      root.style.setProperty('--font-footer-family', settingsToApply.fonts.footer.family);
      root.style.setProperty('--font-footer-size', settingsToApply.fonts.footer.size);
      root.style.setProperty('--font-footer-weight', settingsToApply.fonts.footer.weight);

      // Apply logo settings
      root.style.setProperty('--logo-width', `${settingsToApply.logo.width}px`);
      root.style.setProperty('--logo-height', `${settingsToApply.logo.height}px`);

      return true;
    } catch (error) {
      console.error('Error applying appearance settings:', error);
      return false;
    }
  }

  /**
   * Reset settings to defaults
   */
  resetSettings() {
    try {
      localStorage.removeItem('hauling_appearance_settings');
      this.applySettings(this.defaultSettings);
      return true;
    } catch (error) {
      console.error('Error resetting appearance settings:', error);
      return false;
    }
  }

  /**
   * Get current logo settings
   */
  getLogoSettings() {
    const settings = this.loadSettings();
    return settings.logo;
  }

  /**
   * Get current font settings
   */
  getFontSettings() {
    const settings = this.loadSettings();
    return settings.fonts;
  }

  /**
   * Initialize appearance settings on app start
   */
  initialize() {
    // Apply saved settings on app initialization
    this.applySettings();
    
    // Listen for storage changes (if settings are changed in another tab)
    window.addEventListener('storage', (e) => {
      if (e.key === 'hauling_appearance_settings') {
        this.applySettings();
      }
    });
  }
}

// Create and export a singleton instance
const appearanceService = new AppearanceService();
export default appearanceService;