# Project Structure & Organization

## Root Level Structure
```
hauling-qr-trip-system/
├── client/                 # React frontend application
├── server/                 # Express.js backend API
├── database/              # Database schema and migrations
├── scripts/               # System startup and utility scripts
├── docs/                  # Project documentation
├── utils/                 # Shared utilities
├── tests/                 # Integration tests
├── .env                   # Unified environment configuration
└── package.json           # Root package with system-level scripts
```

## Client Structure (`/client`)
```
client/
├── src/                   # React source code
├── public/                # Static assets and index.html
├── build/                 # Production build output (generated)
├── node_modules/          # Frontend dependencies
├── package.json           # Frontend dependencies and scripts
├── tailwind.config.js     # Tailwind CSS configuration
├── postcss.config.js      # PostCSS configuration
└── start-clean.js         # Clean startup script
```

## Server Structure (`/server`)
```
server/
├── routes/                # Express route handlers
├── services/              # Business logic services
├── middleware/            # Custom Express middleware
├── config/                # Server configuration files
├── utils/                 # Server-side utilities
├── ssl/                   # SSL certificates (dev/production)
├── logs/                  # Application logs
├── scripts/               # Server-specific scripts
├── server.js              # Main server entry point
├── websocket.js           # WebSocket server implementation
└── package.json           # Backend dependencies and scripts
```

## Database Structure (`/database`)
```
database/
├── migrations/            # Database migration files
├── init.sql              # Initial database schema
├── run-migration.js      # Migration runner script
├── check-migration-table.js # Migration table checker
└── .env                  # Database-specific environment variables
```

## Key Architectural Patterns

### Configuration Management
- **Unified Config**: Single `.env` file at root level with automatic client/server variable distribution
- **Environment Detection**: Automatic development/production mode switching
- **IP Auto-detection**: Dynamic network configuration for local development

### Database Architecture
- **Migration-based Schema**: Versioned database changes in `/database/migrations`
- **Advanced PostgreSQL Features**: JSONB columns, GIN indexes, materialized views
- **Performance Optimization**: Connection pooling, optimized queries, cached analytics

### API Structure
- **RESTful Routes**: Organized by resource type in `/server/routes`
- **Service Layer**: Business logic separated in `/server/services`
- **Middleware Chain**: Authentication, validation, rate limiting, CORS
- **WebSocket Integration**: Real-time updates for dashboard and trip monitoring

### Frontend Architecture
- **Component-based**: React components organized by feature/page
- **State Management**: React hooks and context for state management
- **Routing**: React Router DOM for SPA navigation
- **Responsive Design**: Mobile-first approach with Tailwind CSS

## File Naming Conventions

### Backend Files
- **Routes**: `{resource}-routes.js` (e.g., `trip-routes.js`)
- **Services**: `{Resource}Service.js` (e.g., `TripService.js`)
- **Middleware**: `{purpose}-middleware.js` (e.g., `auth-middleware.js`)
- **Utils**: `{purpose}-utils.js` or descriptive names

### Frontend Files
- **Components**: PascalCase (e.g., `TripDashboard.jsx`)
- **Pages**: PascalCase with "Page" suffix (e.g., `DashboardPage.jsx`)
- **Hooks**: camelCase with "use" prefix (e.g., `useTrips.js`)
- **Utils**: camelCase descriptive names

### Database Files
- **Migrations**: `{number}_{description}.sql` (e.g., `016_multi_location_workflow.sql`)
- **Schema**: Descriptive names (e.g., `init.sql`)

## Development Workflow Structure

### Environment Files
- **Root `.env`**: Master configuration for entire system
- **Client `.env`**: Client-specific overrides (rare)
- **Database `.env`**: Database connection settings

### Script Organization
- **Root `/scripts`**: System-wide utilities (startup, testing, configuration)
- **Server `/server/scripts`**: Backend-specific scripts
- **Client scripts**: Defined in `client/package.json`

### Documentation Structure
- **`/docs`**: Project documentation and guides
- **`/docs/adr`**: Architecture Decision Records
- **README files**: Component-specific documentation at each level

## Key Integration Points

### Client-Server Communication
- **API Base URL**: Auto-configured via `REACT_APP_API_URL`
- **WebSocket**: Real-time updates via `REACT_APP_WS_URL`
- **Authentication**: JWT tokens in HTTP headers and WebSocket connections

### Database Integration
- **Connection Pooling**: Configured in server config files
- **Migration System**: Automated via npm scripts
- **Performance Monitoring**: Built-in query performance tracking