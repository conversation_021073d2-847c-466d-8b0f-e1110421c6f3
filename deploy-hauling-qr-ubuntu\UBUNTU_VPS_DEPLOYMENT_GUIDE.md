# Ubuntu VPS Deployment Guide - Hauling QR Trip System

## 🎯 Overview

This guide provides comprehensive instructions for deploying the Hauling QR Trip System on any Ubuntu VPS with automatic IP detection and development-style configuration in production mode.

### Key Features
- ✅ **Automatic VPS IP Detection** - Works on any VPS provider
- ✅ **Development Configuration in Production** - Preserves all dev flexibility
- ✅ **Single Command Deployment** - `sudo -E ./auto-deploy-enhanced.sh`
- ✅ **VPS Provider Agnostic** - DigitalOcean, AWS, Linode, Vultr, etc.
- ✅ **Zero Hardcoded IPs** - Dynamic configuration everywhere

## ⚡ Quick Start

### Single Command Deployment
```bash
# Clone repository
git clone https://github.com/mightybadz18/hauling-qr-trip-management.git
cd hauling-qr-trip-management/deploy-hauling-qr-ubuntu

# Make executable and run
chmod +x auto-deploy.sh
sudo -E ./auto-deploy.sh
```

### With Custom Configuration
```bash
# Set environment variables
export GITHUB_PAT="*********************************************************************************************"
export PRODUCTION_DOMAIN="truckhaul.top"

# Run deployment
sudo -E ./auto-deploy.sh
```

## 📁 Directory Contents

### Core Deployment Files
- **`auto-deploy.sh`** - Enhanced deployment script with automatic IP detection
- **`deployment-config.conf`** - Configuration templates
- **`fix-permissions-ubuntu-user.sh`** - Permission fix script for ubuntu user issues
- **`post-reboot-check.sh`** - Health check script for post-reboot diagnostics

### Documentation
- **`UBUNTU_VPS_DEPLOYMENT_GUIDE.md`** - This comprehensive deployment guide
- **`CLOUDFLARE_DNS_SETUP_GUIDE.md`** - DNS configuration with Cloudflare
- **`TROUBLESHOOTING_GUIDE.md`** - Comprehensive troubleshooting solutions
- **`README-PERMISSION-SCRIPTS.md`** - Permission fix scripts documentation

---

## 📋 Prerequisites

### VPS Requirements
- **OS**: Ubuntu 24.04 LTS (recommended) or 22.04 LTS
- **RAM**: Minimum 2GB (4GB recommended)
- **Storage**: Minimum 20GB SSD
- **Network**: Public IP address with ports 22, 80, 443 accessible

### Local Requirements
- SSH access to your VPS
- GitHub Personal Access Token (for private repositories)
- Domain name (optional but recommended)

---

## 🌐 Step 1: VPS Server Setup

### 1.1 Initial Server Access
```bash
# Connect to your VPS (replace with your actual IP)
ssh root@YOUR_VPS_IP

# Or if using ubuntu user:
ssh ubuntu@YOUR_VPS_IP
```

### 1.2 Basic System Update
```bash
# Update package repositories
apt update && apt upgrade -y

# Install essential tools
apt install -y curl git wget unzip
```

### 1.3 Create Ubuntu User (if not exists)
```bash
# If you're logged in as root, create ubuntu user
adduser ubuntu
usermod -aG sudo ubuntu

# Switch to ubuntu user
su - ubuntu
```

---

## 🔧 Step 2: Download and Prepare Deployment Script

### 2.1 Clone Repository
```bash
# Clone the repository
git clone https://github.com/mightybadz18/hauling-qr-trip-management.git
cd hauling-qr-trip-management/deploy-hauling-qr-ubuntu

# Make script executable
chmod +x auto-deploy.sh
```

### 2.2 Set Environment Variables (Optional)
```bash
# For private repositories
export GITHUB_PAT="*********************************************************************************************"

# For custom domain (optional)
export PRODUCTION_DOMAIN="truckhaul.top"

# For manual IP override (only if auto-detection fails)
export MANUAL_IP="your.vps.ip.here"
```

---

## 🚀 Step 3: Single Command Deployment

### 3.1 Run Enhanced Deployment Script
```bash
# Basic deployment (auto-detects everything)
sudo -E ./auto-deploy.sh

# With custom domain
sudo -E PRODUCTION_DOMAIN=truckhaul.top ./auto-deploy.sh

# With GitHub PAT for private repo
sudo -E GITHUB_PAT=your_token ./auto-deploy.sh
```

### 3.2 What the Script Does Automatically
1. **🔍 Detects VPS IP** using multiple methods (ipinfo.io, ipify.org, OpenDNS, etc.)
2. **📦 Installs Dependencies** (Node.js, PostgreSQL, Nginx, PM2)
3. **🔥 Configures Firewall** (UFW with ports 22, 80, 443, 5000)
4. **📁 Clones Repository** with GitHub PAT authentication
5. **⚙️ Creates Production .env** with development-style configuration
6. **🗄️ Sets Up Database** (PostgreSQL with migrations)
7. **🏗️ Builds Application** (React frontend + Node.js backend)
8. **🌐 Configures Nginx** with detected IP and domain
9. **🚀 Starts PM2 Service** with process management
10. **🏥 Runs Health Checks** to verify deployment

---

## 🌍 Step 4: DNS Configuration

### 4.1 Standard DNS Setup
Point your domain to the detected VPS IP:
```
A Record: @ → YOUR_DETECTED_VPS_IP
A Record: www → YOUR_DETECTED_VPS_IP
```

### 4.2 Cloudflare DNS Setup (Recommended)
1. **Add Domain to Cloudflare**
   - Sign up at cloudflare.com
   - Add your domain (e.g., truckhaul.top)
   - Update nameservers at your domain registrar

2. **Configure DNS Records**
   ```
   Type: A
   Name: @
   Content: YOUR_DETECTED_VPS_IP
   Proxy: ✅ (Orange Cloud)
   
   Type: A  
   Name: www
   Content: YOUR_DETECTED_VPS_IP
   Proxy: ✅ (Orange Cloud)
   ```

3. **SSL/TLS Settings**
   - Go to SSL/TLS → Overview
   - Set to "Full" (not "Full Strict")
   - Enable "Always Use HTTPS"

4. **Page Rules for React SPA**
   ```
   URL: yourdomain.com/*
   Settings: 
   - Cache Level: Standard
   - Browser Cache TTL: 4 hours
   ```

---

## ✅ Step 5: Verification Checklist

### 5.1 Automatic Verification
The deployment script automatically verifies:
- ✅ PM2 process is running
- ✅ Backend health endpoint responds
- ✅ Nginx serves frontend
- ✅ CORS configuration works for IP and domain
- ✅ Database connectivity

### 5.2 Manual Verification
```bash
# Check PM2 status
pm2 status

# Check Nginx status  
systemctl status nginx

# Check PostgreSQL status
systemctl status postgresql

# Test backend directly
curl http://localhost:5000/health

# Test frontend
curl http://localhost/

# Check logs
pm2 logs hauling-qr-server
tail -f /var/log/hauling-deployment/auto-deploy-*.log
```

### 5.3 Browser Testing
1. **IP Access**: `http://YOUR_DETECTED_VPS_IP`
2. **Domain Access**: `https://yourdomain.com`
3. **API Testing**: `https://yourdomain.com/api/health`

---

## 🔧 Step 6: Post-Deployment Management

### 6.1 PM2 Commands
```bash
# View application status
pm2 status

# View logs
pm2 logs hauling-qr-server

# Restart application
pm2 restart hauling-qr-server

# Stop application
pm2 stop hauling-qr-server

# View detailed info
pm2 show hauling-qr-server
```

### 6.2 Nginx Commands
```bash
# Check Nginx status
systemctl status nginx

# Restart Nginx
systemctl restart nginx

# Test Nginx configuration
nginx -t

# View Nginx logs
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log
```

### 6.3 Database Commands
```bash
# Connect to database
sudo -u postgres psql -d hauling_qr_system

# Check database status
systemctl status postgresql

# View database logs
tail -f /var/log/postgresql/postgresql-*.log
```

---

## 🔄 Step 7: VPS Migration Guide

### 7.1 Migrating to New VPS
1. **Prepare New VPS** with Ubuntu 24.04
2. **Run Deployment Script** - IP will be auto-detected
   ```bash
   sudo -E ./auto-deploy-enhanced.sh
   ```
3. **Update DNS Records** to point to new IP
4. **Test New Deployment** before switching traffic

### 7.2 IP Address Changes
If your VPS IP changes:
```bash
# Re-run deployment to update IP configuration
sudo -E ./auto-deploy-enhanced.sh

# Or manually update environment
export MANUAL_IP="new.ip.address"
sudo -E ./auto-deploy-enhanced.sh
```

---

## 🛠️ Troubleshooting Guide

### Common Issues and Solutions

#### 1. IP Detection Fails
```bash
# Check internet connectivity
curl -I https://google.com

# Manual IP override
export MANUAL_IP="your.actual.ip"
sudo -E ./auto-deploy-enhanced.sh
```

#### 2. GitHub Clone Fails
```bash
# Set GitHub PAT
export GITHUB_PAT="*********************************************************************************************"
sudo -E ./auto-deploy-enhanced.sh

# Or use public repository URL
sudo -E ./auto-deploy-enhanced.sh --repo-url https://github.com/user/repo.git
```

#### 3. Database Connection Issues
```bash
# Check PostgreSQL status
systemctl status postgresql

# Restart PostgreSQL
systemctl restart postgresql

# Check database logs
tail -f /var/log/postgresql/postgresql-*.log
```

#### 4. PM2 Process Not Starting
```bash
# Check PM2 logs
pm2 logs hauling-qr-server

# Restart PM2
pm2 restart hauling-qr-server

# Check application logs
tail -f /var/www/hauling-qr-system/server/logs/combined.log
```

#### 5. Nginx Configuration Issues
```bash
# Test Nginx configuration
nginx -t

# Check Nginx logs
tail -f /var/log/nginx/error.log

# Restart Nginx
systemctl restart nginx
```

#### 6. CORS Issues
The deployment uses development-style CORS configuration that should work with:
- Domain access: `https://yourdomain.com`
- IP access: `http://your.vps.ip`
- Localhost: `http://localhost:3000`

If CORS issues persist:
```bash
# Check server logs for CORS messages
pm2 logs hauling-qr-server | grep -i cors

# Verify environment variables
cat /var/www/hauling-qr-system/.env | grep -E "(DETECTED_VPS_IP|PRODUCTION_DOMAIN|DEV_ENABLE_CORS_ALL)"
```

---

## 📊 Configuration Details

### Environment Configuration
The deployment creates a production environment that mirrors development settings:

```bash
# Key configuration preserved from development:
NODE_ENV=production                    # Only change from development
AUTO_DETECT_IP=true                   # Preserved
DEV_ENABLE_CORS_ALL=true             # Preserved  
DEV_DISABLE_RATE_LIMITING=true       # Preserved
DETECTED_VPS_IP=auto-detected-ip     # Auto-populated
PRODUCTION_DOMAIN=yourdomain.com     # Configurable
```

### Service Ports
- **Frontend**: Port 3000 (served by Nginx on port 80/443)
- **Backend**: Port 5000 (proxied by Nginx)
- **Database**: Port 5432 (PostgreSQL, localhost only)
- **Nginx**: Port 80 (HTTP) and 443 (HTTPS via Cloudflare)

### File Locations
- **Application**: `/var/www/hauling-qr-system/`
- **Logs**: `/var/log/hauling-deployment/`
- **Nginx Config**: `/etc/nginx/sites-available/hauling-qr-system`
- **PM2 Config**: `/var/www/hauling-qr-system/ecosystem.config.js`

---

## 🎯 Success Indicators

After successful deployment, you should see:

1. **✅ Deployment Summary** with detected IP and URLs
2. **✅ All Services Running** (PostgreSQL, PM2, Nginx)
3. **✅ Health Checks Passing** (Backend, Frontend, CORS)
4. **✅ Application Accessible** via both IP and domain
5. **✅ Development-Style Flexibility** preserved in production

The system will behave identically to development mode for CORS, networking, and user experience while maintaining proper production logging and security contexts.

---

## 📞 Support

For deployment issues:
1. Check the deployment log: `/var/log/hauling-deployment/auto-deploy-*.log`
2. Review PM2 logs: `pm2 logs hauling-qr-server`
3. Verify Nginx configuration: `nginx -t`
4. Test health endpoints manually
5. Ensure DNS is properly configured

The enhanced deployment script provides comprehensive error handling and detailed logging to help diagnose any issues that may occur during deployment.