/**
 * Business-Grade Trip Monitoring System
 * 
 * This module provides enhanced trip monitoring capabilities for perfect business system operation.
 * It includes automated trip progression monitoring, real-time operational dashboards,
 * comprehensive audit trails, and business intelligence reporting.
 */

const { getClient } = require('../config/database');

/**
 * Business Status Mapping for Trip Monitoring
 */
const BUSINESS_STATUS = {
  ASSIGNED: 'ASSIGNED',
  LOADING_IN_PROGRESS: 'LOADING_IN_PROGRESS',
  READY_FOR_UNLOADING: 'READY_FOR_UNLOADING',
  UNLOADING_IN_PROGRESS: 'UNLOADING_IN_PROGRESS',
  READY_FOR_COMPLETION: 'READY_FOR_COMPLETION',
  COMPLETED: 'COMPLETED',
  EXCEPTION_DETECTED: 'EXCEPTION_DETECTED',
  AWAITING_APPROVAL: 'AWAITING_APPROVAL',
  CANCELLED: 'CANCELLED'
};

/**
 * Operational Status for Real-Time Dashboard
 */
const OPERATIONAL_STATUS = {
  ACTIVE: 'ACTIVE',
  IDLE: 'IDLE',
  EXCEPTION_PENDING: 'EXCEPTION_PENDING',
  NO_TRIPS: 'NO_TRIPS',
  MAINTENANCE: 'MAINTENANCE'
};

/**
 * Get real-time operational dashboard data for all trucks
 * @returns {Promise<Array>} Array of truck operational status
 */
async function getOperationalDashboard() {
  const client = await getClient();
  
  try {
    const result = await client.query(`
      SELECT 
        dt.id as truck_id,
        dt.truck_number,
        dt.status as truck_status,
        COUNT(tl.id) as total_trips_today,
        COUNT(CASE WHEN tl.status = 'trip_completed' THEN 1 END) as completed_trips_today,
        COUNT(CASE WHEN tl.status IN ('loading_start', 'loading_end', 'unloading_start', 'unloading_end') THEN 1 END) as active_trips,
        COUNT(CASE WHEN tl.is_exception = true AND tl.status IN ('exception_triggered', 'exception_pending') THEN 1 END) as pending_exceptions,
        MAX(tl.created_at) as last_trip_time,
        MAX(tl.updated_at) as last_activity_time,
        CASE 
          WHEN dt.status = 'maintenance' THEN 'MAINTENANCE'
          WHEN COUNT(CASE WHEN tl.status IN ('loading_start', 'loading_end', 'unloading_start', 'unloading_end') THEN 1 END) > 0 THEN 'ACTIVE'
          WHEN COUNT(CASE WHEN tl.status IN ('exception_triggered', 'exception_pending') THEN 1 END) > 0 THEN 'EXCEPTION_PENDING'
          WHEN COUNT(tl.id) > 0 THEN 'IDLE'
          ELSE 'NO_TRIPS'
        END as operational_status,
        ROUND(
          CASE 
            WHEN COUNT(tl.id) > 0 THEN 
              COUNT(CASE WHEN tl.status = 'trip_completed' THEN 1 END)::FLOAT / COUNT(tl.id) * 100
            ELSE 0 
          END, 1
        ) as completion_rate_today
      FROM dump_trucks dt
      LEFT JOIN assignments a ON dt.id = a.truck_id AND a.status IN ('assigned', 'in_progress')
      LEFT JOIN trip_logs tl ON a.id = tl.assignment_id AND DATE(tl.created_at) = CURRENT_DATE
      GROUP BY dt.id, dt.truck_number, dt.status
      ORDER BY dt.truck_number
    `);
    
    return result.rows.map(row => ({
      truck_id: row.truck_id,
      truck_number: row.truck_number,
      truck_status: row.truck_status,
      operational_status: row.operational_status,
      total_trips_today: parseInt(row.total_trips_today),
      completed_trips_today: parseInt(row.completed_trips_today),
      active_trips: parseInt(row.active_trips),
      pending_exceptions: parseInt(row.pending_exceptions),
      completion_rate_today: parseFloat(row.completion_rate_today),
      last_trip_time: row.last_trip_time,
      last_activity_time: row.last_activity_time
    }));
    
  } finally {
    client.release();
  }
}

/**
 * Get comprehensive audit trail for trips with business status mapping
 * @param {Object} options - Query options
 * @param {number} options.limit - Number of records to return (default: 50)
 * @param {string} options.truck_number - Filter by specific truck
 * @param {Date} options.start_date - Filter from date
 * @param {Date} options.end_date - Filter to date
 * @returns {Promise<Array>} Array of trip audit records
 */
async function getComprehensiveAuditTrail(options = {}) {
  const { limit = 50, truck_number, start_date, end_date } = options;
  const client = await getClient();
  
  try {
    let whereClause = '1=1';
    const params = [];
    let paramIndex = 1;
    
    if (truck_number) {
      whereClause += ` AND dt.truck_number = $${paramIndex}`;
      params.push(truck_number);
      paramIndex++;
    }
    
    if (start_date) {
      whereClause += ` AND tl.created_at >= $${paramIndex}`;
      params.push(start_date);
      paramIndex++;
    }
    
    if (end_date) {
      whereClause += ` AND tl.created_at <= $${paramIndex}`;
      params.push(end_date);
      paramIndex++;
    }
    
    params.push(limit);
    
    const result = await client.query(`
      SELECT
        tl.id, tl.trip_number, tl.status, tl.created_at, tl.updated_at,
        dt.truck_number, a.assignment_code,
        tl.loading_start_time, tl.loading_end_time,
        tl.unloading_start_time, tl.unloading_end_time, tl.trip_completed_time,
        tl.is_exception, tl.exception_reason, tl.exception_approved_at,
        tl.total_duration_minutes, tl.loading_duration_minutes,
        tl.travel_duration_minutes, tl.unloading_duration_minutes,
        tl.notes,

        -- Historical driver information (who actually performed the trip)
        tl.performed_by_driver_id,
        tl.performed_by_driver_name,
        tl.performed_by_employee_id,
        tl.performed_by_shift_id,
        tl.performed_by_shift_type,

        -- Assignment driver (fallback)
        d.id as assignment_driver_id,
        d.full_name as assignment_driver_name,
        d.employee_id as assignment_employee_id,

        -- Current shift driver information
        ds.driver_id as current_shift_driver_id,
        sd.full_name as current_shift_driver_name,
        sd.employee_id as current_shift_employee_id,
        ds.shift_type as current_shift_type,

        ll.name as loading_location, ul.name as unloading_location,
        COALESCE(all_loc.name, ll.name) as actual_loading_location,
        COALESCE(aul.name, ul.name) as actual_unloading_location,
        ap.status as approval_status, ap.reviewed_by, ap.reviewed_at,
        CASE
          WHEN tl.trip_completed_time IS NOT NULL THEN 'COMPLETED'
          WHEN tl.status = 'exception_pending' THEN 'AWAITING_APPROVAL'
          WHEN tl.status = 'exception_triggered' THEN 'EXCEPTION_DETECTED'
          WHEN tl.status = 'cancelled' THEN 'CANCELLED'
          WHEN tl.unloading_end_time IS NOT NULL THEN 'READY_FOR_COMPLETION'
          WHEN tl.unloading_start_time IS NOT NULL THEN 'UNLOADING_IN_PROGRESS'
          WHEN tl.loading_end_time IS NOT NULL THEN 'READY_FOR_UNLOADING'
          WHEN tl.loading_start_time IS NOT NULL THEN 'LOADING_IN_PROGRESS'
          ELSE 'ASSIGNED'
        END as business_status
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      JOIN dump_trucks dt ON a.truck_id = dt.id
      LEFT JOIN drivers d ON a.driver_id = d.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      LEFT JOIN locations all_loc ON tl.actual_loading_location_id = all_loc.id
      LEFT JOIN locations aul ON tl.actual_unloading_location_id = aul.id
      LEFT JOIN approvals ap ON tl.id = ap.trip_log_id
      LEFT JOIN driver_shifts ds ON (
        ds.truck_id = a.truck_id
        AND ds.status = 'active'
        AND CURRENT_DATE BETWEEN ds.start_date AND ds.end_date
        AND CURRENT_TIME BETWEEN ds.start_time AND
            CASE
                WHEN ds.end_time < ds.start_time
                THEN ds.end_time + interval '24 hours'
                ELSE ds.end_time
            END
      )
      LEFT JOIN drivers sd ON ds.driver_id = sd.id
      WHERE ${whereClause}
      ORDER BY tl.created_at DESC
      LIMIT $${paramIndex}
    `, params);
    
    return result.rows.map(row => ({
      id: row.id,
      trip_number: row.trip_number,
      truck_number: row.truck_number,
      assignment_code: row.assignment_code,
      system_status: row.status,
      business_status: row.business_status,
      created_at: row.created_at,
      updated_at: row.updated_at,
      timeline: {
        loading_start: row.loading_start_time,
        loading_end: row.loading_end_time,
        unloading_start: row.unloading_start_time,
        unloading_end: row.unloading_end_time,
        trip_completed: row.trip_completed_time
      },
      locations: {
        assigned_loading: row.loading_location,
        assigned_unloading: row.unloading_location,
        actual_loading: row.actual_loading_location,
        actual_unloading: row.actual_unloading_location
      },
      duration_metrics: {
        total_minutes: row.total_duration_minutes,
        loading_minutes: row.loading_duration_minutes,
        travel_minutes: row.travel_duration_minutes,
        unloading_minutes: row.unloading_duration_minutes
      },
      exception_info: {
        is_exception: row.is_exception,
        exception_reason: row.exception_reason,
        exception_approved_at: row.exception_approved_at,
        approval_status: row.approval_status,
        reviewed_by: row.reviewed_by,
        reviewed_at: row.reviewed_at
      },
      notes: row.notes ? JSON.parse(row.notes) : {}
    }));
    
  } finally {
    client.release();
  }
}

/**
 * Get business intelligence metrics for reporting and analytics
 * @param {Object} options - Query options
 * @param {Date} options.start_date - Start date for metrics
 * @param {Date} options.end_date - End date for metrics
 * @returns {Promise<Object>} Business intelligence metrics
 */
async function getBusinessIntelligenceMetrics(options = {}) {
  const { start_date, end_date } = options;
  const client = await getClient();
  
  try {
    let dateFilter = '';
    const params = [];
    
    if (start_date && end_date) {
      dateFilter = 'WHERE tl.created_at BETWEEN $1 AND $2';
      params.push(start_date, end_date);
    } else if (start_date) {
      dateFilter = 'WHERE tl.created_at >= $1';
      params.push(start_date);
    } else {
      // Default to last 30 days
      dateFilter = 'WHERE tl.created_at >= CURRENT_DATE - INTERVAL \'30 days\'';
    }
    
    const [overallMetrics, dailyMetrics, truckMetrics, exceptionMetrics] = await Promise.all([
      // Overall metrics
      client.query(`
        SELECT 
          COUNT(tl.id) as total_trips,
          COUNT(CASE WHEN tl.status = 'trip_completed' THEN 1 END) as completed_trips,
          COUNT(CASE WHEN tl.is_exception = true THEN 1 END) as exception_trips,
          AVG(tl.total_duration_minutes) as avg_duration_minutes,
          AVG(CASE WHEN tl.status = 'trip_completed' THEN tl.total_duration_minutes END) as avg_completed_duration,
          ROUND(
            COUNT(CASE WHEN tl.status = 'trip_completed' THEN 1 END)::FLOAT / 
            NULLIF(COUNT(tl.id), 0) * 100, 2
          ) as completion_rate,
          ROUND(
            COUNT(CASE WHEN tl.is_exception = true THEN 1 END)::FLOAT / 
            NULLIF(COUNT(tl.id), 0) * 100, 2
          ) as exception_rate
        FROM trip_logs tl ${dateFilter}
      `, params),
      
      // Daily metrics
      client.query(`
        SELECT 
          DATE(tl.created_at) as trip_date,
          COUNT(tl.id) as total_trips,
          COUNT(CASE WHEN tl.status = 'trip_completed' THEN 1 END) as completed_trips,
          COUNT(CASE WHEN tl.is_exception = true THEN 1 END) as exception_trips,
          AVG(tl.total_duration_minutes) as avg_duration
        FROM trip_logs tl ${dateFilter}
        GROUP BY DATE(tl.created_at)
        ORDER BY trip_date DESC
        LIMIT 30
      `, params),
      
      // Truck performance metrics
      client.query(`
        SELECT 
          dt.truck_number,
          COUNT(tl.id) as total_trips,
          COUNT(CASE WHEN tl.status = 'trip_completed' THEN 1 END) as completed_trips,
          COUNT(CASE WHEN tl.is_exception = true THEN 1 END) as exception_trips,
          AVG(CASE WHEN tl.status = 'trip_completed' THEN tl.total_duration_minutes END) as avg_duration,
          ROUND(
            COUNT(CASE WHEN tl.status = 'trip_completed' THEN 1 END)::FLOAT / 
            NULLIF(COUNT(tl.id), 0) * 100, 2
          ) as completion_rate
        FROM trip_logs tl
        JOIN assignments a ON tl.assignment_id = a.id
        JOIN dump_trucks dt ON a.truck_id = dt.id
        ${dateFilter}
        GROUP BY dt.truck_number
        ORDER BY total_trips DESC
      `, params),
      
      // Exception analysis
      client.query(`
        SELECT 
          tl.exception_reason,
          COUNT(*) as occurrence_count,
          COUNT(CASE WHEN tl.exception_approved_at IS NOT NULL THEN 1 END) as approved_count,
          AVG(EXTRACT(EPOCH FROM (tl.exception_approved_at - tl.created_at))/3600) as avg_resolution_hours
        FROM trip_logs tl
        ${dateFilter} AND tl.is_exception = true
        GROUP BY tl.exception_reason
        ORDER BY occurrence_count DESC
      `, params)
    ]);
    
    return {
      overall: overallMetrics.rows[0],
      daily_trends: dailyMetrics.rows,
      truck_performance: truckMetrics.rows,
      exception_analysis: exceptionMetrics.rows,
      generated_at: new Date().toISOString(),
      period: {
        start_date: start_date || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
        end_date: end_date || new Date()
      }
    };
    
  } finally {
    client.release();
  }
}

/**
 * Detect and fix incomplete trips automatically
 * @returns {Promise<Array>} Array of fixed trips
 */
async function detectAndFixIncompleteTrips() {
  const client = await getClient();
  
  try {
    // Find trips that are stuck in intermediate states
    const incompleteTrips = await client.query(`
      SELECT 
        tl.id, tl.trip_number, tl.status, tl.created_at,
        dt.truck_number, a.assignment_code,
        CASE 
          WHEN tl.loading_start_time IS NULL THEN 'missing_loading_start'
          WHEN tl.loading_end_time IS NULL AND tl.loading_start_time IS NOT NULL THEN 'stuck_at_loading'
          WHEN tl.unloading_start_time IS NULL AND tl.loading_end_time IS NOT NULL THEN 'missing_unloading_start'
          WHEN tl.unloading_end_time IS NULL AND tl.unloading_start_time IS NOT NULL THEN 'stuck_at_unloading'
          WHEN tl.trip_completed_time IS NULL AND tl.unloading_end_time IS NOT NULL THEN 'missing_completion'
          ELSE 'complete_progression'
        END as gap_type,
        EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - tl.created_at))/3600 as hours_since_creation
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      JOIN dump_trucks dt ON a.truck_id = dt.id
      WHERE tl.status NOT IN ('trip_completed', 'cancelled', 'exception_triggered', 'exception_pending')
        AND tl.is_exception = false
        AND EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - tl.created_at))/3600 > 1 -- Older than 1 hour
      ORDER BY tl.created_at ASC
    `);
    
    const fixedTrips = [];
    const now = new Date();
    
    for (const trip of incompleteTrips.rows) {
      let updateQuery = '';
      let updateParams = [];
      let newStatus = '';
      
      switch (trip.gap_type) {
        case 'missing_unloading_start':
          updateQuery = `
            UPDATE trip_logs 
            SET unloading_start_time = $1,
                status = 'unloading_start',
                notes = COALESCE(notes::jsonb, '{}'::jsonb) || $2::jsonb,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = $3
          `;
          newStatus = 'unloading_start';
          break;
          
        case 'missing_completion':
          updateQuery = `
            UPDATE trip_logs 
            SET trip_completed_time = $1,
                status = 'trip_completed',
                notes = COALESCE(notes::jsonb, '{}'::jsonb) || $2::jsonb,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = $3
          `;
          newStatus = 'trip_completed';
          break;
      }
      
      if (updateQuery) {
        updateParams = [
          now,
          JSON.stringify({
            auto_progression: true,
            auto_progression_reason: 'Business logic enhancement - automated trip progression',
            auto_progression_timestamp: now.toISOString(),
            previous_status: trip.status,
            gap_type: trip.gap_type,
            hours_stuck: Math.round(trip.hours_since_creation)
          }),
          trip.id
        ];
        
        await client.query(updateQuery, updateParams);
        
        fixedTrips.push({
          trip_id: trip.id,
          trip_number: trip.trip_number,
          truck_number: trip.truck_number,
          assignment_code: trip.assignment_code,
          previous_status: trip.status,
          new_status: newStatus,
          gap_type: trip.gap_type,
          hours_stuck: Math.round(trip.hours_since_creation)
        });
      }
    }
    
    return fixedTrips;
    
  } finally {
    client.release();
  }
}

module.exports = {
  BUSINESS_STATUS,
  OPERATIONAL_STATUS,
  getOperationalDashboard,
  getComprehensiveAuditTrail,
  getBusinessIntelligenceMetrics,
  detectAndFixIncompleteTrips
};
