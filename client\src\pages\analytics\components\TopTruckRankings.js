import React from 'react';

const TopTruckRankings = ({ data, loading, dateRange }) => {
  if (loading) {
    return (
      <div className="animate-pulse space-y-4">
        {[...Array(8)].map((_, i) => (
          <div key={i} className="bg-secondary-200 h-16 rounded-lg"></div>
        ))}
      </div>
    );
  }

  if (!data || !data.rankings || data.rankings.length === 0) {
    return (
      <div className="text-center py-12">
        <svg className="w-12 h-12 text-secondary-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
        <h3 className="text-lg font-medium text-secondary-900 mb-2">No Truck Performance Data Available</h3>
        <p className="text-secondary-500">Performance rankings will appear here once there are trucks with assigned drivers and completed trips.</p>
      </div>
    );
  }

  const formatDateRange = () => {
    if (!dateRange) return '';
    const start = new Date(dateRange.start).toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
    const end = new Date(dateRange.end).toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
    return `${start} - ${end}`;
  };

  const formatDuration = (minutes) => {
    if (!minutes || minutes === 0) return '0m';
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;
  };

  const getPerformanceColor = (score) => {
    if (score >= 80) return 'text-green-600 bg-green-50';
    if (score >= 60) return 'text-yellow-600 bg-yellow-50';
    if (score >= 40) return 'text-orange-600 bg-orange-50';
    return 'text-red-600 bg-red-50';
  };

  const getPerformanceLabel = (score) => {
    if (score >= 80) return 'Excellent';
    if (score >= 60) return 'Good';
    if (score >= 40) return 'Average';
    return 'Below Average';
  };

  const getRankIcon = (rank) => {
    switch(rank) {
      case 1: return '🥇';
      case 2: return '🥈';
      case 3: return '🥉';
      default: return `#${rank}`;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h4 className="text-lg font-medium text-secondary-900">🏆 Top Dump Trucks Performance Rankings</h4>
          <p className="text-sm text-secondary-500 mt-1">
            Ranked by performance score based on trip completion, efficiency, and exception rates
          </p>
        </div>
        <div className="text-right">
          <div className="text-sm text-secondary-500">Period: {formatDateRange()}</div>
          <div className="text-sm text-secondary-500">Total Trucks: {data.totalTrucks}</div>
        </div>
      </div>

      {/* Performance Legend */}
      <div className="bg-secondary-50 rounded-lg p-4">
        <h5 className="text-sm font-medium text-secondary-900 mb-2">Performance Score Legend:</h5>
        <div className="flex flex-wrap gap-4 text-sm">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            <span>Excellent (80-100)</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
            <span>Good (60-79)</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
            <span>Average (40-59)</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-red-500 rounded-full"></div>
            <span>Below Average (0-39)</span>
          </div>
        </div>
      </div>

      {/* Rankings Table */}
      <div className="bg-white border border-secondary-200 rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-secondary-200">
            <thead className="bg-secondary-50">
              <tr>
                <th className="px-4 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                  Rank
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                  Truck Details
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                  Driver
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                  Total Trips
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                  Avg Duration
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                  Exception Rate
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                  Performance Score
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-secondary-200">
              {data.rankings.map((truck, index) => (
                <tr key={truck.truckId} className={index % 2 === 0 ? 'bg-white' : 'bg-secondary-50'}>
                  <td className="px-4 py-3 whitespace-nowrap">
                    <div className="flex items-center">
                      <span className="text-lg font-bold text-secondary-900">
                        {getRankIcon(truck.rank)}
                      </span>
                    </div>
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap">
                    <div className="flex flex-col">
                      <span className="text-sm font-bold text-secondary-900">
                        {truck.truckNumber}
                      </span>
                      <span className="text-xs text-secondary-500">
                        ID: {truck.truckId}
                      </span>
                    </div>
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap">
                    <div className="flex flex-col">
                      <span className="text-sm font-medium text-secondary-900">
                        {truck.driverName}
                      </span>
                      <span className="text-xs text-secondary-500">
                        {truck.employeeId}
                      </span>
                    </div>
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap">
                    <div className="flex flex-col">
                      <span className="text-sm font-bold text-secondary-900">
                        {truck.totalCompletedTrips}
                      </span>
                      <span className="text-xs text-secondary-500">
                        completed
                      </span>
                    </div>
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap">
                    <div className="flex flex-col">
                      <span className="text-sm font-medium text-secondary-900">
                        {formatDuration(truck.avgTripDuration)}
                      </span>
                      <div className="text-xs text-secondary-500">
                        <div>Load: {formatDuration(truck.avgLoadingDuration)}</div>
                        <div>Travel: {formatDuration(truck.avgTravelDuration)}</div>
                        <div>Unload: {formatDuration(truck.avgUnloadingDuration)}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap">
                    <div className="flex flex-col">
                      <span className="text-sm font-medium text-secondary-900">
                        {truck.exceptionRate}%
                      </span>
                      <span className="text-xs text-secondary-500">
                        ({truck.exceptionCount} exceptions)
                      </span>
                    </div>
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap">
                    <div className="flex flex-col">
                      <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getPerformanceColor(truck.performanceScore)}`}>
                        {truck.performanceScore.toFixed(1)}
                      </span>
                      <span className="text-xs text-secondary-500 mt-1">
                        {getPerformanceLabel(truck.performanceScore)}
                      </span>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Performance Insights */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h5 className="text-lg font-medium text-blue-900 mb-4">📊 Performance Insights</h5>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-900">
              {data.rankings.length > 0 ? data.rankings[0].performanceScore.toFixed(1) : '0'}
            </div>
            <div className="text-sm text-blue-700">Top Performance Score</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-900">
              {data.rankings.reduce((sum, truck) => sum + truck.totalCompletedTrips, 0)}
            </div>
            <div className="text-sm text-blue-700">Total Trips Completed</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-900">
              {data.rankings.length > 0 ? 
                formatDuration(Math.round(data.rankings.reduce((sum, truck) => sum + truck.avgTripDuration, 0) / data.rankings.length)) : 
                '0m'
              }
            </div>
            <div className="text-sm text-blue-700">Fleet Avg Duration</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-900">
              {data.rankings.length > 0 ? 
                (data.rankings.reduce((sum, truck) => sum + parseFloat(truck.exceptionRate), 0) / data.rankings.length).toFixed(1) : 
                '0.0'
              }%
            </div>
            <div className="text-sm text-blue-700">Fleet Exception Rate</div>
          </div>
        </div>
        <p className="text-sm text-blue-700 mt-4 text-center">
          ✅ Rankings based on multi-location trip workflow supporting A→B→C extensions and C→B→C cycles
        </p>
      </div>
    </div>
  );
};

export default TopTruckRankings;