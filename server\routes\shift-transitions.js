/**
 * Shift Transitions API Routes
 * Purpose: Provide API endpoints for manual shift transition control and monitoring
 * Issues Addressed: ISSUE 1 - Missing Automatic Shift Status Transition
 * 
 * Endpoints:
 * - GET /api/shift-transitions/status - Get system status
 * - POST /api/shift-transitions/activate/:shiftId - Manually activate shift
 * - POST /api/shift-transitions/force-cycle - Force transition cycle
 * - GET /api/shift-transitions/health - Health check
 * - GET /api/shift-transitions/metrics - Performance metrics
 */

const express = require('express');
const router = express.Router();
const auth = require('../middleware/auth');
const { manualShiftControls, getShiftTransitionHealth, performanceMonitor } = require('../utils/shift-transition-integration');
const { shiftTransitionManager } = require('../utils/enhanced-shift-transitions');
const EnhancedShiftStatusService = require('../services/EnhancedShiftStatusService');

/**
 * GET /api/shift-transitions/status
 * Get current system status and shift counts
 */
router.get('/status', async (req, res) => {
  try {
    const status = await manualShiftControls.getStatus();
    
    res.json({
      success: true,
      data: status,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Shift transition status error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get shift transition status',
      details: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * POST /api/shift-transitions/activate/:shiftId
 * Manually activate a specific shift
 */
router.post('/activate/:shiftId', async (req, res) => {
  try {
    const shiftId = parseInt(req.params.shiftId);
    
    if (isNaN(shiftId)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid shift ID',
        timestamp: new Date().toISOString()
      });
    }

    const result = await manualShiftControls.activateShift(shiftId);
    
    if (result.success) {
      res.json({
        success: true,
        message: 'Shift activated successfully',
        data: result,
        timestamp: new Date().toISOString()
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error,
        shift_id: shiftId,
        timestamp: new Date().toISOString()
      });
    }
  } catch (error) {
    console.error('Manual shift activation error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to activate shift',
      details: error.message,
      shift_id: req.params.shiftId,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * POST /api/shift-transitions/force-cycle
 * Force run a transition cycle immediately
 */
router.post('/force-cycle', async (req, res) => {
  try {
    const result = await manualShiftControls.forceTransitionCycle();
    
    if (result.success) {
      res.json({
        success: true,
        message: 'Transition cycle completed',
        data: result,
        timestamp: new Date().toISOString()
      });
    } else {
      res.status(500).json({
        success: false,
        error: result.error,
        timestamp: new Date().toISOString()
      });
    }
  } catch (error) {
    console.error('Force transition cycle error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to run transition cycle',
      details: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * GET /api/shift-transitions/health
 * Health check endpoint for monitoring
 */
router.get('/health', async (req, res) => {
  try {
    const health = await getShiftTransitionHealth();
    
    const statusCode = health.status === 'healthy' ? 200 : 
                      health.status === 'warning' ? 200 : 500;
    
    res.status(statusCode).json({
      success: health.status !== 'error',
      data: health,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Shift transition health check error:', error);
    res.status(500).json({
      success: false,
      error: 'Health check failed',
      details: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * GET /api/shift-transitions/metrics
 * Get performance metrics
 */
router.get('/metrics', async (req, res) => {
  try {
    const metrics = performanceMonitor.getMetrics();
    
    res.json({
      success: true,
      data: metrics,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Shift transition metrics error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get metrics',
      details: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * POST /api/shift-transitions/reset-metrics
 * Reset performance metrics (admin only)
 */
router.post('/reset-metrics', async (req, res) => {
  try {
    performanceMonitor.reset();
    
    res.json({
      success: true,
      message: 'Performance metrics reset',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Reset metrics error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to reset metrics',
      details: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * GET /api/shift-transitions/active-drivers
 * Get all currently active drivers with enhanced information
 */
router.get('/active-drivers', async (req, res) => {
  try {
    const activeDrivers = [];
    
    // Get all trucks and check for active drivers
    const { getClient } = require('../config/database');
    const client = await getClient();
    
    try {
      const trucksResult = await client.query('SELECT id, truck_number FROM dump_trucks ORDER BY truck_number');
      
      for (const truck of trucksResult.rows) {
        const driverInfo = await shiftTransitionManager.getCurrentActiveDriverEnhanced(truck.id);
        
        if (driverInfo) {
          activeDrivers.push({
            truck_id: truck.id,
            truck_number: truck.truck_number,
            driver_id: driverInfo.driver_id,
            driver_name: driverInfo.driver_name,
            employee_id: driverInfo.employee_id,
            shift_id: driverInfo.shift_id,
            shift_type: driverInfo.shift_type,
            display_type: driverInfo.display_type,
            recurrence_pattern: driverInfo.recurrence_pattern,
            start_time: driverInfo.start_time,
            end_time: driverInfo.end_time
          });
        }
      }
    } finally {
      client.release();
    }
    
    res.json({
      success: true,
      data: {
        active_drivers: activeDrivers,
        count: activeDrivers.length
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Active drivers error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get active drivers',
      details: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * GET /api/shift-transitions/upcoming-activations
 * Get shifts that will be activated in the next hour
 */
router.get('/upcoming-activations', async (req, res) => {
  try {
    const { getClient } = require('../config/database');
    const client = await getClient();
    
    try {
      const upcomingQuery = `
        SELECT 
          ds.id,
          ds.truck_id,
          dt.truck_number,
          ds.driver_id,
          d.full_name as driver_name,
          d.employee_id,
          ds.shift_type,
          COALESCE(ds.display_type, ds.shift_type) as display_type,
          ds.recurrence_pattern,
          ds.start_time,
          ds.end_time,
          ds.shift_date,
          ds.start_date,
          ds.end_date
        FROM driver_shifts ds
        JOIN drivers d ON ds.driver_id = d.id
        JOIN dump_trucks dt ON ds.truck_id = dt.id
        WHERE ds.status = 'scheduled'
          AND (
            -- Single date shifts for today
            (ds.recurrence_pattern = 'single' AND ds.shift_date = CURRENT_DATE)
            OR
            -- Date range shifts active today
            (ds.recurrence_pattern != 'single' AND CURRENT_DATE BETWEEN ds.start_date AND ds.end_date)
          )
          AND ds.start_time BETWEEN CURRENT_TIME AND (CURRENT_TIME + interval '1 hour')
        ORDER BY ds.start_time, dt.truck_number
      `;
      
      const result = await client.query(upcomingQuery);
      
      res.json({
        success: true,
        data: {
          upcoming_activations: result.rows,
          count: result.rows.length
        },
        timestamp: new Date().toISOString()
      });
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('Upcoming activations error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get upcoming activations',
      details: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * GET /api/shift-transitions/system-info
 * Get comprehensive system information
 */
router.get('/system-info', async (req, res) => {
  try {
    const [status, metrics, health] = await Promise.all([
      manualShiftControls.getStatus(),
      performanceMonitor.getMetrics(),
      getShiftTransitionHealth()
    ]);
    
    res.json({
      success: true,
      data: {
        system_status: status,
        performance_metrics: metrics,
        health_check: health,
        features: {
          automatic_transitions: true,
          date_range_scheduling: true,
          intelligent_classification: true,
          performance_monitoring: true,
          manual_controls: true
        }
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('System info error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get system information',
      details: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * GET /api/shift-transitions/summary
 * Get comprehensive shift status summary using enhanced service
 */
router.get('/summary', auth, async (req, res) => {
  try {
    const { reference_time } = req.query;
    const referenceTimestamp = reference_time ? new Date(reference_time) : null;

    const summary = await EnhancedShiftStatusService.getStatusSummary(referenceTimestamp);

    res.json({
      success: true,
      data: summary,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Enhanced shift summary error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get enhanced shift summary',
      details: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * POST /api/shift-transitions/evaluate/:shiftId
 * Evaluate status for a specific shift using enhanced logic
 */
router.post('/evaluate/:shiftId', auth, async (req, res) => {
  try {
    const { shiftId } = req.params;
    const { reference_time } = req.body;

    if (!shiftId || isNaN(parseInt(shiftId))) {
      return res.status(400).json({
        success: false,
        error: 'Invalid shift ID',
        timestamp: new Date().toISOString()
      });
    }

    const referenceTimestamp = reference_time ? new Date(reference_time) : null;
    const status = await EnhancedShiftStatusService.evaluateShiftStatus(parseInt(shiftId), referenceTimestamp);

    res.json({
      success: true,
      data: {
        shift_id: parseInt(shiftId),
        evaluated_status: status,
        reference_time: referenceTimestamp || new Date()
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Shift evaluation error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to evaluate shift status',
      details: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * POST /api/shift-transitions/test-logic
 * Test shift time logic for validation
 */
router.post('/test-logic', auth, async (req, res) => {
  try {
    const { start_time, end_time, test_time, is_overnight } = req.body;

    if (!start_time || !end_time || !test_time) {
      return res.status(400).json({
        success: false,
        error: 'start_time, end_time, and test_time are required',
        timestamp: new Date().toISOString()
      });
    }

    const result = await EnhancedShiftStatusService.testShiftTimeLogic(
      start_time,
      end_time,
      test_time,
      is_overnight
    );

    res.json({
      success: true,
      data: result,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Shift logic test error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to test shift logic',
      details: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * POST /api/shift-transitions/force-update
 * Force immediate status update for all shifts
 */
router.post('/force-update', auth, async (req, res) => {
  try {
    await EnhancedShiftStatusService.forceUpdate();

    res.json({
      success: true,
      message: 'Force update completed',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Force update error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to force update',
      details: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * GET /api/shift-transitions/service-health
 * Get enhanced service health status
 */
router.get('/service-health', auth, async (req, res) => {
  try {
    const health = EnhancedShiftStatusService.getHealthStatus();

    res.json({
      success: true,
      data: health,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Service health check error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get service health',
      details: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

module.exports = router;
