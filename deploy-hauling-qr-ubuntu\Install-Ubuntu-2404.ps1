# Installs WSL2 + Ubuntu 24.04, creates user 'ariez' with password 'password', enables systemd.

$ErrorActionPreference = "Stop"

$DistroName = "Ubuntu-24.04"
$UserName   = "ariez"
$PlainPassword = ConvertTo-SecureString "password" -AsPlainText -Force

function Test-Admin {
  $id = [Security.Principal.WindowsIdentity]::GetCurrent()
  $p = New-Object Security.Principal.WindowsPrincipal($id)
  if (-not $p.IsInRole([Security.Principal.WindowsBuiltinRole]::Administrator)) {
    throw "Run this script in an elevated PowerShell (Admin)."
  }
}

function Enable-WSLFeatures {
  Write-Host "Enabling WSL features..."
  $features = @("Microsoft-Windows-Subsystem-Linux","VirtualMachinePlatform")
  $needsReboot = $false
  foreach ($f in $features) {
    $res = Enable-WindowsOptionalFeature -Online -FeatureName $f -All -NoRestart -ErrorAction SilentlyContinue
    if ($res -and $res.RestartNeeded) { $needsReboot = $true }
  }
  if ($needsReboot) {
    Write-Warning "Reboot required to finish enabling features. Reboot and re-run this script."
    exit 0
  }
}

function Set-WSL2 {
  Write-Host "Updating WSL and setting default version to 2..."
  wsl --update 2>$null | Out-Null
  wsl --set-default-version 2 2>$null | Out-Null
}

function Install-Ubuntu2404 {
  Write-Host "Installing Ubuntu 24.04 LTS..."
  $installed = $false
  try {
    winget --version *>$null
    foreach ($id in @("Canonical.Ubuntu.2404","Canonical.Ubuntu24.04LTS")) {
      winget install -e --id $id --accept-source-agreements --accept-package-agreements --silent
      if ($LASTEXITCODE -eq 0) { $installed = $true; break }
    }
  } catch { }
  if (-not $installed) {
    wsl --list --online | Out-Null
    wsl --install -d $DistroName
  }
  # Wait until distro shows up
  for ($i=0; $i -lt 60; $i++) {
    $list = wsl -l -v 2>$null
    if ($list -match [Regex]::Escape($DistroName) -or $list -match "Ubuntu 24.04 LTS") { break }
    Start-Sleep -Seconds 2
  }
}

function Get-DistroName {
  $list = wsl -l -v 2>$null
  if ($list -match "Ubuntu-24.04") { return "Ubuntu-24.04" }
  if ($list -match "Ubuntu 24.04 LTS") { return "Ubuntu 24.04 LTS" }
  return $DistroName
}

function Initialize-Ubuntu {
  param([string]$DistroName,[string]$UserName,[SecureString]$PlainPassword)

  $ubuntuExe = "ubuntu2404.exe"
  $hasExe = $false
  try { Get-Command $ubuntuExe -ErrorAction Stop | Out-Null; $hasExe = $true } catch {}

  if ($hasExe) {
    Write-Host "Initializing distro as root (non-interactive)..."
    & $ubuntuExe install --root 2>$null
  } else {
    Write-Host "Priming distro..."
    wsl -d $DistroName -u root -- true 2>$null
  }

  Write-Host "Creating user '$UserName' with sudo..."
  $BSTR = [Runtime.InteropServices.Marshal]::SecureStringToBSTR($PlainPassword)
  try {
    $PlainTextPassword = [Runtime.InteropServices.Marshal]::PtrToStringBSTR($BSTR)
  }
  finally {
    [Runtime.InteropServices.Marshal]::ZeroFreeBSTR($BSTR)
  }

  # Use a single-line bash script to avoid CRLF ($'\r') issues
  wsl -d $DistroName -u root -- env NEWUSER="$UserName" NEWPASS="$PlainTextPassword" bash -lc 'set -e; export DEBIAN_FRONTEND=noninteractive; apt-get update -y >/dev/null 2>&1 || true; apt-get install -y sudo >/dev/null 2>&1 || true; id -u "$NEWUSER" >/dev/null 2>&1 || useradd -m -s /bin/bash "$NEWUSER"; echo "$NEWUSER:$NEWPASS" | chpasswd; usermod -aG sudo "$NEWUSER"; printf "[boot]\nsystemd=true\n[user]\ndefault=%s\n" "$NEWUSER" >/etc/wsl.conf'
  if ($hasExe) {
    & $ubuntuExe config --default-user $UserName 2>$null
  }

  Write-Host "Restarting WSL..."
  wsl --shutdown
}

# ---------------- Main ----------------
Test-Admin
Enable-WSLFeatures
Set-WSL2
Install-Ubuntu2404
$DistroName = Get-DistroName
Initialize-Ubuntu -DistroName $DistroName -UserName $UserName -PlainPassword $PlainPassword

Write-Host "`nUbuntu is ready. Launching as $UserName..."
wsl -d $DistroName -u $UserName