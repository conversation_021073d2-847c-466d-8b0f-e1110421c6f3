# Design Document

## Overview

The Role-Based Access Control (RBAC) system will be implemented as a comprehensive solution that allows administrators to manage user role types and configure granular page access permissions. The system will integrate seamlessly with the existing authentication infrastructure while providing an intuitive interface within the Settings page. The design focuses on maintaining the existing user_role enum system while adding dynamic CRUD operations and a robust permission management layer.

## Architecture

### Simplified Architecture

```mermaid
graph TB
    A[Settings Page] --> B[User Roles Component]
    B --> C[Role List with CRUD]
    B --> D[Permission Checkboxes]
    
    C --> E[roles table]
    D --> F[role_permissions table]
    
    G[Route Protection] --> H[Check user.role in role_permissions]
    H --> F
```

### Database Architecture

Simple two-table approach:

1. **roles table**: Replaces enum with a simple table for role types
2. **role_permissions table**: Stores which roles can access which pages

### Frontend Architecture

Single component approach integrated into existing Settings page:

1. **UserRolesManagement**: One main component with role CRUD and permission matrix
2. **Simple route guards**: Check permissions before rendering routes

### Backend Architecture

Minimal API extension:

1. **Role endpoints**: Basic CRUD for roles
2. **Permission endpoints**: Get/set permissions
3. **Simple middleware**: Check role permissions on protected routes

## Components and Interfaces

### Frontend Components

#### 1. UserRolesManagement Component (Single Component)
```javascript
const UserRolesManagement = () => {
  const [roles, setRoles] = useState([]);
  const [permissions, setPermissions] = useState({});
  const [newRoleName, setNewRoleName] = useState('');
  
  const pages = [
    { key: 'dashboard', name: 'Dashboard' },
    { key: 'users', name: 'User Management' },
    { key: 'trips', name: 'Trip Monitoring' },
    { key: 'assignments', name: 'Assignment Management' },
    { key: 'analytics', name: 'Analytics' },
    { key: 'settings', name: 'Settings' }
  ];
  
  // Simple functions: addRole, deleteRole, togglePermission
}
```

#### 2. Simple Route Protection
```javascript
// Add to existing auth context
const usePermission = (pageKey) => {
  const { user } = useAuth();
  // Check if user.role has access to pageKey
  return checkPermission(user.role, pageKey);
}
```

### Backend API Endpoints

#### Simple API Design
```
GET    /api/roles              - Get all user_role enum values
POST   /api/roles              - Add new value to user_role enum
DELETE /api/roles/:name        - Remove role (only if no users assigned)

GET    /api/permissions         - Get all role permissions
POST   /api/permissions         - Save role permissions (bulk update)
```

### Database Schema

#### 1. Keep existing user_role enum, add CRUD functions
```sql
-- Function to safely add new role to enum
CREATE OR REPLACE FUNCTION add_user_role_enum(new_role TEXT) 
RETURNS VOID AS $$
BEGIN
    -- Check if role already exists
    IF NOT EXISTS (
        SELECT 1 FROM pg_enum 
        WHERE enumlabel = new_role 
        AND enumtypid = 'user_role'::regtype
    ) THEN
        EXECUTE format('ALTER TYPE user_role ADD VALUE %L', new_role);
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Function to get all enum values
CREATE OR REPLACE FUNCTION get_user_roles() 
RETURNS TABLE(role_name TEXT) AS $$
BEGIN
    RETURN QUERY
    SELECT enumlabel::TEXT 
    FROM pg_enum 
    WHERE enumtypid = 'user_role'::regtype
    ORDER BY enumlabel;
END;
$$ LANGUAGE plpgsql;
```

#### 2. role_permissions table (using existing enum)
```sql
CREATE TABLE role_permissions (
    id SERIAL PRIMARY KEY,
    role_name user_role NOT NULL,
    page_key VARCHAR(100) NOT NULL,
    has_access BOOLEAN DEFAULT false,
    UNIQUE(role_name, page_key)
);
```

#### 3. No changes to users table (keeps existing structure)
```sql
-- users table already has: role user_role NOT NULL
-- No changes needed
```

## Data Models

### Simplified Models

#### Role Model
```javascript
{
  name: string,           // Role name
  userCount: number,      // Number of users with this role
  canDelete: boolean      // Whether role can be safely deleted
}
```

#### Permission Model
```javascript
{
  [roleName]: {
    [pageKey]: boolean    // true = has access, false = no access
  }
}
```

#### Hardcoded Pages List
```javascript
const PAGES = [
  { key: 'dashboard', name: 'Dashboard' },
  { key: 'users', name: 'User Management' },
  { key: 'trips', name: 'Trip Monitoring' },
  { key: 'assignments', name: 'Assignment Management' },
  { key: 'shifts', name: 'Shift Management' },
  { key: 'analytics', name: 'Analytics' },
  { key: 'settings', name: 'Settings' }
];
```

## Error Handling

### Simple Error Handling
1. **Role Creation**: Check for duplicate role names
2. **Role Deletion**: Prevent deletion if users are assigned to role
3. **Permission Updates**: Basic validation and error messages
4. **Access Denied**: Simple 403 responses for unauthorized access

### Error Response Format
```javascript
{
  success: false,
  message: 'User-friendly error message'
}
```

## Testing Strategy

### Basic Testing Approach
1. **API Testing**: Test role CRUD and permission endpoints
2. **Permission Logic**: Test that users can/cannot access pages based on their role
3. **UI Testing**: Test the role management interface works correctly

### Test Data Setup
```sql
-- Default permissions for admin role
INSERT INTO role_permissions (role_name, page_key, has_access) VALUES
('admin', 'dashboard', true),
('admin', 'users', true),
('admin', 'trips', true),
('admin', 'assignments', true),
('admin', 'analytics', true),
('admin', 'settings', true);
```

## Security Considerations

### Simple Security Approach
1. **Admin-Only Access**: Only admin users can manage roles and permissions
2. **Basic Validation**: Validate role names and prevent SQL injection
3. **Permission Checks**: Check user role permissions before allowing page access