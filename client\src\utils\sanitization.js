/**
 * Sanitization utilities for user input and WebSocket messages
 */

/**
 * Sanitize HTML content to prevent XSS attacks
 * @param {string} html - HTML content to sanitize
 * @returns {string} Sanitized HTML
 */
export const sanitizeHtml = (html) => {
  if (typeof html !== 'string') return '';
  
  // Basic HTML sanitization - remove script tags and event handlers
  return html
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/on\w+="[^"]*"/gi, '')
    .replace(/on\w+='[^']*'/gi, '')
    .replace(/javascript:/gi, '')
    .replace(/vbscript:/gi, '')
    .replace(/data:/gi, '');
};

/**
 * Sanitize text content for display
 * @param {string} text - Text to sanitize
 * @param {number} maxLength - Maximum length (default: 500)
 * @returns {string} Sanitized text
 */
export const sanitizeText = (text, maxLength = 500) => {
  if (typeof text !== 'string') return '';
  
  // Remove HTML tags and limit length
  const cleaned = text
    .replace(/<[^>]*>/g, '') // Remove HTML tags
    .replace(/&[^;]+;/g, '') // Remove HTML entities
    .trim();
    
  return cleaned.length > maxLength 
    ? cleaned.substring(0, maxLength) + '...'
    : cleaned;
};

/**
 * Sanitize WebSocket message for safe display
 * @param {Object} message - WebSocket message object
 * @returns {Object} Sanitized message
 */
export const sanitizeWebSocketMessage = (message) => {
  if (!message || typeof message !== 'object') return {};
  
  const sanitized = {};
  
  // Sanitize string fields
  Object.keys(message).forEach(key => {
    const value = message[key];
    
    if (typeof value === 'string') {
      // Sanitize text content
      sanitized[key] = sanitizeText(value);
    } else if (typeof value === 'object' && value !== null) {
      // Recursively sanitize nested objects
      sanitized[key] = sanitizeWebSocketMessage(value);
    } else {
      // Keep other types as-is (numbers, booleans, etc.)
      sanitized[key] = value;
    }
  });
  
  return sanitized;
};

/**
 * Validate WebSocket message structure
 * @param {Object} message - WebSocket message
 * @returns {boolean} True if message is valid
 */
export const validateWebSocketMessage = (message) => {
  if (!message || typeof message !== 'object') return false;
  
  // Required fields
  if (!message.type || typeof message.type !== 'string') return false;
  
  // Validate message type against allowed types
  const allowedTypes = [
    'auth_success',
    'welcome',
    'pong',
    'driver_connected',
    'driver_disconnected',
    'driver_handover',
    'bulk_shifts_created',
    'shift_status_changed',
    'exception_created',
    'exception_updated',
    'trip_status_changed',
    'route_discovery_started',
    'route_location_confirmed',
    'route_updated',
    'route_discovery_completed',
    'trip_extended',
    'cycle_started',
    'dynamic_route',
    'workflow_completed'
  ];
  
  if (!allowedTypes.includes(message.type)) {
    console.warn('Unknown WebSocket message type:', message.type);
    return false;
  }
  
  return true;
};

/**
 * Safe JSON parse with error handling
 * @param {string} jsonString - JSON string to parse
 * @returns {Object|null} Parsed object or null if invalid
 */
export const safeJsonParse = (jsonString) => {
  try {
    return JSON.parse(jsonString);
  } catch (error) {
    console.error('Invalid JSON:', error);
    return null;
  }
};