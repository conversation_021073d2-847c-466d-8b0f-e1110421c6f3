const express = require('express');
const router = express.Router();
const { query } = require('../config/database');
const auth = require('../middleware/auth');
const Joi = require('joi');

// Validation schemas
const addRoleSchema = Joi.object({
  role_name: Joi.string().min(2).max(50).pattern(/^[a-zA-Z_]+$/).required()
    .messages({
      'string.pattern.base': 'Role name can only contain letters and underscores'
    })
});

// @route   GET /api/roles
// @desc    Get all user_role enum values
// @access  Private (Admin only)
router.get('/', auth, async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        error: 'Access Denied',
        message: 'Only administrators can access role management'
      });
    }

    // Get all user roles using the database function
    const result = await query('SELECT * FROM get_user_roles()');
    
    // Get user count for each role
    const userCountsResult = await query(`
      SELECT role, COUNT(*) as user_count 
      FROM users 
      GROUP BY role
    `);
    
    // Create a map of role to user count
    const userCounts = {};
    userCountsResult.rows.forEach(row => {
      userCounts[row.role] = parseInt(row.user_count);
    });
    
    // Combine role data with user counts
    const rolesWithCounts = result.rows.map(row => ({
      name: row.role_name,  // Frontend expects 'name' field
      role_name: row.role_name,  // Keep for backward compatibility
      user_count: userCounts[row.role_name] || 0,
      can_delete: userCounts[row.role_name] === 0 || userCounts[row.role_name] === undefined
    }));

    res.json({
      success: true,
      data: rolesWithCounts
    });

  } catch (error) {
    console.error('Get roles error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve user roles'
    });
  }
});

// @route   POST /api/roles
// @desc    Add new role to user_role enum
// @access  Private (Admin only)
router.post('/', auth, async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        error: 'Access Denied',
        message: 'Only administrators can create roles'
      });
    }

    // Validate input
    const { error } = addRoleSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        error: 'Validation Error',
        message: error.details[0].message
      });
    }

    const { role_name } = req.body;

    // Check if role already exists
    const existingRoles = await query('SELECT * FROM get_user_roles()');
    const roleExists = existingRoles.rows.some(row => row.role_name === role_name);

    if (roleExists) {
      return res.status(400).json({
        error: 'Duplicate Error',
        message: 'Role already exists'
      });
    }

    // Add new role to enum using the database function
    await query('SELECT add_user_role_enum($1)', [role_name]);

    // Create default permissions for the new role (no access by default)
    const defaultPages = [
      'dashboard', 'users', 'trucks', 'drivers', 'locations', 
      'assignments', 'shifts', 'trips', 'scanner', 'assignment_monitoring', 
      'truck_trip_summary', 'analytics', 'settings'
    ];
    
    for (const page_key of defaultPages) {
      await query(
        'INSERT INTO role_permissions (role_name, page_key, has_access) VALUES ($1, $2, $3) ON CONFLICT (role_name, page_key) DO NOTHING',
        [role_name, page_key, false]
      );
    }

    res.status(201).json({
      success: true,
      message: 'Role created successfully',
      data: {
        role_name,
        user_count: 0,
        can_delete: true
      }
    });

  } catch (error) {
    console.error('Create role error:', error);
    
    // Handle PostgreSQL enum constraint errors
    if (error.code === '42601' || error.message.includes('enum')) {
      return res.status(400).json({
        error: 'Invalid Role',
        message: 'Invalid role name or role creation failed'
      });
    }

    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to create role'
    });
  }
});

// @route   DELETE /api/roles/:name
// @desc    Remove role from user_role enum (if no users assigned)
// @access  Private (Admin only)
router.delete('/:name', auth, async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        error: 'Access Denied',
        message: 'Only administrators can delete roles'
      });
    }

    const { name } = req.params;

    // Prevent deletion of core roles
    const coreRoles = ['admin', 'supervisor', 'operator'];
    if (coreRoles.includes(name)) {
      return res.status(400).json({
        error: 'Invalid Operation',
        message: 'Cannot delete core system roles'
      });
    }

    // Check if role exists
    const existingRoles = await query('SELECT * FROM get_user_roles()');
    const roleExists = existingRoles.rows.some(row => row.role_name === name);

    if (!roleExists) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Role not found'
      });
    }

    // Check if role can be safely deleted using the database function
    const canDeleteResult = await query('SELECT can_delete_user_role($1) as can_delete', [name]);
    const canDelete = canDeleteResult.rows[0].can_delete;

    if (!canDelete) {
      return res.status(400).json({
        error: 'Invalid Operation',
        message: 'Cannot delete role that has users assigned to it'
      });
    }

    // Remove permissions for this role first
    await query('DELETE FROM role_permissions WHERE role_name = $1', [name]);

    // Note: PostgreSQL doesn't support removing enum values directly
    // This would require recreating the enum type, which is complex and risky
    // For now, we'll just remove the permissions and mark it as "deleted"
    // In a production system, you might want to add a "deleted" flag to track this
    
    res.json({
      success: true,
      message: 'Role permissions removed successfully. Note: Role enum value remains in database but is no longer usable.'
    });

  } catch (error) {
    console.error('Delete role error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to delete role'
    });
  }
});

// @route   GET /api/roles/:name/users
// @desc    Get users assigned to a specific role
// @access  Private (Admin only)
router.get('/:name/users', auth, async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        error: 'Access Denied',
        message: 'Only administrators can view role assignments'
      });
    }

    const { name } = req.params;

    // Get users with this role
    const result = await query(
      'SELECT id, username, full_name, email, status, created_at FROM users WHERE role = $1 ORDER BY username',
      [name]
    );

    res.json({
      success: true,
      data: {
        role_name: name,
        user_count: result.rows.length,
        users: result.rows
      }
    });

  } catch (error) {
    console.error('Get role users error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve role users'
    });
  }
});

module.exports = router;