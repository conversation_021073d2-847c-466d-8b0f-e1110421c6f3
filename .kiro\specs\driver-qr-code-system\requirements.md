# Requirements Document

## Introduction

The Driver QR Code system is a complementary time tracking and identification system that integrates with the existing Hauling QR Trip System. This system enables drivers to check in and out of their shifts using unique QR codes, automatically creating driver-truck associations and tracking precise shift timestamps. The system operates independently of the existing 4-phase trip workflow (loading_start → loading_end → unloading_start → unloading_end → trip_completed) while seamlessly integrating with the current shift management and assignment systems.

## Requirements

### Requirement 1

**User Story:** As a driver, I want to use the same two-step scanning process for both time-in and time-out, so that I can start and end my shift using the same familiar workflow.

#### Acceptance Criteria

1. WHEN a driver accesses the driver connect page THEN the system SHALL provide the same two-step scanning process for both check-in and check-out operations
2. WHEN a driver scans their driver QR code THEN the system SHALL authenticate them and determine their current shift status
3. WHEN a driver has NO active shift THEN scanning a truck QR code SHALL create a new shift with status 'active' immediately (time-in)
4. WHEN a driver has an ACTIVE shift THEN scanning the SAME truck QR code SHALL end the shift (time-out) and calculate simple duration (end_time - start_time)
5. WHEN a driver tries to scan a DIFFERENT truck QR code while having an active shift THEN the system SHALL automatically end their current shift and start a new shift with the new truck

### Requirement 2

**User Story:** As a system administrator, I want to manage driver QR codes within the existing driver management system, so that I can maintain the driver identification system and monitor driver activity using the same interface as trucks and locations.

#### Acceptance Criteria

1. WHEN an administrator accesses the existing driver management system THEN the system SHALL provide QR code generation functionality integrated into the current interface (similar to trucks and locations)
2. WHEN an administrator generates a driver QR code THEN the system SHALL store the QR code data in the drivers table with proper encryption/hashing
3. WHEN an administrator views driver records THEN the system SHALL display QR code generation button in the actions column alongside edit and delete buttons
4. WHEN an administrator clicks the QR code button THEN the system SHALL use the same QRCodeModal component used by trucks and locations
5. WHEN the system generates QR codes THEN the codes SHALL contain driver identification data in a secure, tamper-resistant format

### Requirement 3

**User Story:** As a fleet manager, I want to handle real-world scenarios where multiple drivers work on the same truck, so that I can track all driver activities and shift changes throughout the day with automatic handovers.

#### Acceptance Criteria

1. WHEN multiple drivers work on the same truck (e.g., DT-100) THEN the system SHALL automatically create separate shifts for each driver connection
2. WHEN Driver B connects to a truck already assigned to Driver A THEN the system SHALL automatically end Driver A's shift and start Driver B's shift without requiring confirmation
3. WHEN automatic shift handovers occur THEN the system SHALL maintain accurate records of who worked when, with precise timestamps for seamless transitions
4. WHEN a fleet manager views truck status THEN the system SHALL show the complete history of drivers who worked on that truck during the day
5. WHEN automatic shifts are created THEN they SHALL work with both new automatic shifts and existing manually created shifts in the system

### Requirement 4

**User Story:** As a driver, I want to access a standalone driver check-in page without needing to log into the main system, so that I can quickly scan my ID and truck QR codes for time tracking without authentication barriers.

#### Acceptance Criteria

1. WHEN a driver accesses the driver check-in URL THEN the system SHALL provide a public, standalone page that does not require login authentication
2. WHEN the standalone page loads THEN it SHALL provide a dedicated QR scanner interface optimized for mobile devices with clear instructions
3. WHEN a driver uses the standalone scanner THEN it SHALL authenticate the driver through their QR code scan rather than requiring username/password login
4. WHEN the scanning process completes THEN the system SHALL provide immediate feedback and return to the scanning interface for the next driver
5. WHEN the standalone page is accessed THEN it SHALL be completely separate from the main authenticated system and accessible via the URL /driver-connect (this is separate from the admin driver management enhancement)

### Requirement 5

**User Story:** As a fleet manager, I want the driver QR system to automatically create shifts when drivers connect to trucks, so that I don't need to manually create shifts for multiple drivers working on the same truck throughout the day.

#### Acceptance Criteria

1. WHEN a driver connects to a truck THEN the system SHALL automatically create a new shift record in driver_shifts table with precise start timestamp
2. WHEN multiple drivers work on the same truck (e.g., DT-100) THEN the system SHALL create separate shift records for each driver automatically
3. WHEN a driver disconnects from a truck THEN the system SHALL automatically end their shift record with precise end timestamp
4. WHEN automatic shifts are created THEN they SHALL enhance the existing shift management system by providing real-time, accurate shift data
5. WHEN the AutoAssignmentCreator runs THEN it SHALL benefit from the automatically created shifts to make better assignment decisions based on actual driver availability

### Requirement 6

**User Story:** As a security administrator, I want driver authentication to work through QR code scanning with basic validation, so that drivers can connect easily while maintaining system security.

#### Acceptance Criteria

1. WHEN a driver scans their QR code THEN the system SHALL extract driver_id and employee_id from the QR code data without requiring password authentication
2. WHEN driver QR data is extracted THEN the system SHALL query the drivers table to validate the driver exists AND status = 'active'
3. WHEN a driver has active status THEN the system SHALL proceed to truck scanning step
4. WHEN a driver has inactive status THEN the system SHALL display error "Driver account is inactive. Please contact your supervisor."
5. WHEN the public endpoint is accessed THEN the system SHALL use basic validation only without complex security measures to maintain simplicity

### Requirement 7

**User Story:** As a system administrator, I want to determine if day/night shift classifications are necessary for the driver connect system, so that the implementation focuses on essential functionality without unnecessary complexity.

#### Acceptance Criteria

1. WHEN drivers connect to trucks THEN the system SHALL automatically create shifts without requiring manual day/night shift type selection
2. WHEN automatic shifts are created THEN they SHALL always use 'custom' shift type as default
3. WHEN shift type classification is needed for reporting THEN the system SHALL optionally classify shifts as day/night based on connection timestamps (e.g., 6 AM - 6 PM = day, 6 PM - 6 AM = night)
4. WHEN the basic driver connect functionality operates THEN it SHALL work independently of shift type classifications to avoid complexity
5. WHEN administrators need shift type configuration THEN the system SHALL allow admin to configure shift types and time ranges through the admin interface

### Requirement 8

**User Story:** As a database administrator, I want the driver QR system to follow established database patterns, so that data integrity and performance are maintained.

#### Acceptance Criteria

1. WHEN database schema changes are needed THEN they SHALL be implemented through the existing migration system in database/migrations/
2. WHEN driver QR data is stored THEN it SHALL use proper indexing and follow existing naming conventions
3. WHEN driver-truck associations are created THEN they SHALL use the existing driver_shifts table structure with proper foreign keys to drivers and dump_trucks tables
4. WHEN shift records are automatically created THEN they SHALL use the existing driver_shifts table structure
5. WHEN the system queries driver data THEN it SHALL use optimized queries with proper connection pooling

### Requirement 9

**User Story:** As a driver, I want a user-friendly frontend scanner that automatically determines if I'm checking in (time-in) or checking out (time-out) based on my current status, so that I use the same scanning process for both actions.

#### Acceptance Criteria

1. WHEN a driver opens the driver scanner THEN it SHALL display "Step 1: Scan QR Code on Back of Your ID" with visual instructions and camera viewfinder
2. WHEN a driver successfully scans their ID's QR code THEN the system SHALL check if they have an active shift and display their current status (checked-in or checked-out)
3. WHEN a driver is NOT currently checked in THEN the scanner SHALL display "Step 2: Scan Truck QR Code to CHECK IN" and proceed with time-in process
4. WHEN a driver IS currently checked in THEN the scanner SHALL display "Step 2: Scan the SAME Truck QR Code to CHECK OUT" showing their current truck assignment
5. WHEN the scanning process completes THEN the system SHALL display confirmation showing the action taken (CHECK IN with start time, or CHECK OUT with end time and total duration)

### Requirement 10

**User Story:** As a system administrator, I want to generate QR codes that can be printed on the back of driver ID cards, so that drivers have a convenient and secure way to identify themselves during the scanning process.

#### Acceptance Criteria

1. WHEN driver QR codes are generated THEN they SHALL be stored in the drivers table driver_qr_code JSONB field (similar to dump_trucks.qr_code_data structure)
2. WHEN QR codes are printed on ID cards THEN they SHALL be readable by mobile device cameras at close range (4-8 inches) typical for ID card scanning
3. WHEN drivers use their ID cards THEN the QR codes SHALL be protected from wear and tear through lamination or card printing processes
4. WHEN the frontend scanner operates THEN it SHALL be optimized for close-range scanning of ID cards as well as normal-distance scanning of truck-mounted QR codes
5. WHEN QR codes are scanned THEN the system SHALL validate against the drivers table (not users table) since drivers are separate from admin users

### Requirement 11

**User Story:** As a system administrator, I want only active drivers and active trucks to be usable in the QR scanning process, so that inactive or terminated drivers and out-of-service trucks cannot be used for check-in operations.

#### Acceptance Criteria

1. WHEN a driver scans their ID QR code THEN the system SHALL verify the driver status is 'active' before proceeding to step 2
2. WHEN an inactive driver scans their QR code THEN the system SHALL display a centered error message "Driver account is inactive. Please contact your supervisor."
3. WHEN a driver scans a truck QR code THEN the system SHALL verify the truck status is 'active' before creating the driver-truck association
4. WHEN an inactive truck QR code is scanned THEN the system SHALL display a centered error message "Truck is not available for assignment. Please contact maintenance or supervisor."
5. WHEN either validation fails THEN the system SHALL prevent the scanning process from continuing and require scanning of valid active driver and truck QR codes

### Requirement 12

**User Story:** As a supervisor, I want to handle emergency situations where drivers cannot complete normal check-out procedures, so that shift records can be properly managed without affecting trip operations.

#### Acceptance Criteria

1. WHEN a driver has an accident or emergency THEN the existing trip "stopped" function SHALL handle trip interruptions independently of driver check-in status
2. WHEN a driver cannot physically check out due to emergency THEN supervisors SHALL manually end the shift in the driver_shifts table through the admin interface
3. WHEN emergency manual check-out occurs THEN it SHALL only affect the driver's shift time record and SHALL NOT impact any active or stopped trips
4. WHEN drivers are hospitalized or unavailable THEN their shift status can be updated without requiring physical QR scanning
5. WHEN emergency procedures are used THEN the system SHALL log the manual intervention with supervisor ID and reason for audit purposes

### Requirement 13

**User Story:** As a fleet manager, I want to view calculated driver shift durations and attendance records, so that I can monitor driver work hours, calculate payroll, and track attendance patterns.

#### Acceptance Criteria

1. WHEN a driver completes a shift THEN the system SHALL automatically calculate the simple duration (end_time minus start_time) without complex break time calculations
2. WHEN administrators access the driver attendance page THEN the system SHALL display driver shift records with calculated durations, dates, and truck assignments
3. WHEN viewing attendance records THEN the system SHALL provide filtering options by driver, date range, truck, and shift duration
4. WHEN shift durations are calculated THEN the system SHALL use simple time arithmetic for easy payroll processing and multiple shifts per day tracking
5. WHEN attendance data is displayed THEN it SHALL include daily totals, weekly summaries, and monthly reports for payroll and management purposes

### Requirement 14

**User Story:** As a system operator, I want comprehensive error handling and validation, so that the driver QR system is reliable and provides clear feedback.

#### Acceptance Criteria

1. WHEN invalid QR codes are scanned THEN the system SHALL provide specific error messages explaining the validation failure
2. WHEN drivers attempt to check into trucks already assigned to other active drivers THEN the system SHALL prevent conflicts and suggest resolution
3. WHEN drivers try to check out with active trips in progress THEN the system SHALL handle the scenario gracefully with appropriate warnings
4. WHEN network or database errors occur THEN the system SHALL provide offline-capable feedback and retry mechanisms
5. WHEN validation rules are violated THEN the system SHALL follow the same error handling patterns as the existing trip workflow system

## Implementation Clarifications

### Frontend Specifications
- **QR Code Format**: Driver QR codes SHALL match the truck QR format structure
- **UI/UX Flow**: When Driver B scans a truck assigned to Driver A, show brief notification "Taking over from Driver A" with confirmation screen
- **Error Handling**: Display error messages and require manual retry for network failures
- **Mobile Optimization**: /driver-connect page SHALL work in both portrait and landscape modes

### Database Specifications
- **QR Code Structure**: driver_qr_code JSONB field SHALL include driver_id, employee_id, generated_date
- **Shift Timestamps**: Use existing start_time/end_time (TIME) + start_date/end_date (DATE) fields
- **Indexing**: Add indexes for driver_shifts queries by driver_id and status

### Backend Specifications
- **API Response Format**: Public API responses SHALL include driver full_name and truck assignment details
- **Concurrent Access**: Handle multiple drivers scanning same truck using database transactions with row-level locking
- **Rate Limiting**: Implement global rate limiting for public endpoints
- **WebSocket Integration**: Driver connect/disconnect events SHALL trigger real-time dashboard updates

### Integration Specifications
- **QR Code Storage**: Driver QR codes stored in drivers table (NOT users table)
- **Shift Type Logic**: Always use 'custom' shift type, allow admin to configure shift types and time ranges

## Files to be Modified or Created

Based on the requirements analysis, the following files will be modified or created:

### Database Changes
- **database/migrations/018_driver_qr_code_system.sql** (NEW) - Add driver_qr_code field to drivers table (NOT users table)
- **database/init.sql** (MODIFY) - Will be updated by migration script

**Database Analysis:**
- **drivers table** - Will get new driver_qr_code JSONB field (similar to dump_trucks.qr_code_data)
- **driver_shifts table** - Already exists with all needed fields (truck_id, driver_id, start_time, end_time, status, start_date, end_date)
- **users table** - No changes needed (users are for admin dashboard access only)
- **dump_trucks table** - Already has qr_code_data JSONB field (no changes needed)

### Backend API Changes
- **server/routes/driver.js** (NEW) - New API endpoints for driver connect operations (public endpoints, no auth middleware)
- **server/routes/driver-admin.js** (NEW) - Admin endpoints for driver QR code management and attendance reporting (requires authentication)
- **server/services/DriverQRService.js** (NEW) - Business logic for driver QR code operations
- **server/services/DriverAttendanceService.js** (NEW) - Business logic for calculating shift durations and attendance reporting
- **server/utils/DriverQRCodeGenerator.js** (NEW) - Utility for generating and validating driver QR codes

### Frontend Changes
- **client/src/pages/drivers/DriverConnect.js** (NEW) - Standalone driver connect scanner interface (no login required)
- **client/src/pages/drivers/DriverAttendance.js** (NEW) - Driver attendance and duration reporting page
- **client/src/components/AppRoutes.js** (MODIFY) - Add public route for driver connect page (/driver-connect)
- **client/src/components/layout/DashboardLayout.js** (MODIFY) - Add authenticated routes for driver connect management and attendance
- **client/src/components/layout/Sidebar.js** (MODIFY) - Add navigation links for driver connect management and attendance
- **client/src/pages/drivers/DriversManagement.js** (MODIFY) - Add QR code generation functionality
- **client/src/services/api.js** (MODIFY) - Add driver API endpoints with public access for connect operations and attendance data

### Configuration Changes
- **client/src/hooks/usePermissions.js** (MODIFY) - Add driver_connect and driver_attendance page permissions for admin access
- **database/migrations/017_role_based_access_control.sql** (MODIFY) - Add driver_connect and driver_attendance permissions for admin roles

### Enhanced Integration
- **server/utils/AutoAssignmentCreator.js** (ENHANCED) - Will benefit from automatically created shifts for better assignment decisions
- **Existing shift management system** (ENHANCED) - Will be enhanced with automatic shift creation instead of manual processes
- **database driver_shifts table** (ENHANCED) - Will be populated automatically with real driver connection data

### No Changes Required
- **client/src/pages/scanner/QRScanner.js** (NO CHANGE) - Existing trip scanner remains untouched
- **server/routes/scanner.js** (NO CHANGE) - Existing trip scanning API remains untouched
- **database trip_logs table** (NO CHANGE) - Driver system only affects driver_shifts table
- **database assignments table** (NO CHANGE) - Structure remains the same, but benefits from better shift data