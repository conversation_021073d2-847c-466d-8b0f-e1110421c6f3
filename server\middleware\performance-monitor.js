/**
 * Performance Monitoring Middleware
 * Monitors and logs performance metrics for Driver QR system endpoints
 */

const { logInfo, logError } = require('../utils/logger');

/**
 * Performance monitoring middleware for driver QR endpoints
 * Tracks response times, memory usage, and database query performance
 */
const performanceMonitor = (options = {}) => {
  const {
    slowThreshold = 500, // Log requests slower than 500ms
    memoryThreshold = 100 * 1024 * 1024, // Log if memory usage > 100MB
    logAllRequests = false
  } = options;

  return (req, res, next) => {
    const startTime = Date.now();
    const startMemory = process.memoryUsage();

    // Override res.json to capture response data
    const originalJson = res.json;
    res.json = function(data) {
      const endTime = Date.now();
      const endMemory = process.memoryUsage();
      const responseTime = endTime - startTime;
      const memoryDelta = endMemory.heapUsed - startMemory.heapUsed;

      // Performance metrics
      const metrics = {
        method: req.method,
        url: req.originalUrl,
        response_time_ms: responseTime,
        memory_delta_bytes: memoryDelta,
        memory_delta_mb: Math.round(memoryDelta / 1024 / 1024 * 100) / 100,
        status_code: res.statusCode,
        user_agent: req.get('User-Agent'),
        ip_address: req.ip || req.connection.remoteAddress,
        timestamp: new Date().toISOString()
      };

      // Add request body size if available
      if (req.body && typeof req.body === 'object') {
        metrics.request_body_size = JSON.stringify(req.body).length;
      }

      // Add response data size
      if (data) {
        metrics.response_body_size = JSON.stringify(data).length;
      }

      // Log based on thresholds
      const shouldLog = logAllRequests || 
                       responseTime > slowThreshold || 
                       Math.abs(memoryDelta) > memoryThreshold ||
                       res.statusCode >= 400;

      if (shouldLog) {
        const logLevel = responseTime > slowThreshold ? 'SLOW_REQUEST' : 
                        res.statusCode >= 400 ? 'ERROR_REQUEST' : 
                        'PERFORMANCE_METRICS';

        logInfo(logLevel, `${req.method} ${req.originalUrl} - ${responseTime}ms`, metrics);
      }

      // Add performance headers for debugging
      if (process.env.NODE_ENV === 'development') {
        res.set({
          'X-Response-Time': `${responseTime}ms`,
          'X-Memory-Delta': `${Math.round(memoryDelta / 1024)}KB`
        });
      }

      // Call original json method
      return originalJson.call(this, data);
    };

    next();
  };
};

/**
 * Database query performance monitor
 * Wraps database queries to track performance
 */
const queryPerformanceMonitor = (originalQuery) => {
  return async function(text, params) {
    const startTime = Date.now();
    
    try {
      const result = await originalQuery.call(this, text, params);
      const queryTime = Date.now() - startTime;

      // Log slow queries
      if (queryTime > 100) {
        logInfo('SLOW_DATABASE_QUERY', `Database query took ${queryTime}ms`, {
          query_time_ms: queryTime,
          query_text: text.substring(0, 200) + (text.length > 200 ? '...' : ''),
          param_count: params ? params.length : 0,
          result_rows: result.rows ? result.rows.length : 0
        });
      }

      return result;
    } catch (error) {
      const queryTime = Date.now() - startTime;
      logError('DATABASE_QUERY_ERROR', error, {
        query_time_ms: queryTime,
        query_text: text.substring(0, 200) + (text.length > 200 ? '...' : ''),
        param_count: params ? params.length : 0
      });
      throw error;
    }
  };
};

/**
 * Memory usage monitor
 * Tracks memory usage patterns and alerts on memory leaks
 */
const memoryMonitor = {
  lastCheck: Date.now(),
  lastMemory: process.memoryUsage(),
  
  check() {
    const now = Date.now();
    const currentMemory = process.memoryUsage();
    const timeDelta = now - this.lastCheck;
    const memoryDelta = currentMemory.heapUsed - this.lastMemory.heapUsed;

    // Check every 5 minutes
    if (timeDelta > 5 * 60 * 1000) {
      const memoryGrowthRate = memoryDelta / timeDelta; // bytes per ms
      
      logInfo('MEMORY_USAGE_CHECK', 'Memory usage monitoring', {
        heap_used_mb: Math.round(currentMemory.heapUsed / 1024 / 1024),
        heap_total_mb: Math.round(currentMemory.heapTotal / 1024 / 1024),
        external_mb: Math.round(currentMemory.external / 1024 / 1024),
        memory_delta_mb: Math.round(memoryDelta / 1024 / 1024),
        growth_rate_kb_per_min: Math.round(memoryGrowthRate * 60 * 1000 / 1024),
        check_interval_minutes: Math.round(timeDelta / 60 / 1000)
      });

      // Alert on potential memory leak (>10MB growth per minute)
      if (memoryGrowthRate > 10 * 1024 * 1024 / 60 / 1000) {
        logError('POTENTIAL_MEMORY_LEAK', 'High memory growth rate detected', {
          growth_rate_mb_per_min: Math.round(memoryGrowthRate * 60 * 1000 / 1024 / 1024),
          current_heap_mb: Math.round(currentMemory.heapUsed / 1024 / 1024)
        });
      }

      this.lastCheck = now;
      this.lastMemory = currentMemory;
    }
  },

  startMonitoring() {
    // Check memory every minute
    setInterval(() => this.check(), 60 * 1000);
    logInfo('MEMORY_MONITOR_STARTED', 'Memory monitoring started');
  }
};

/**
 * Performance summary generator
 * Generates periodic performance reports
 */
const performanceSummary = {
  metrics: {
    requests: 0,
    totalResponseTime: 0,
    slowRequests: 0,
    errors: 0,
    lastReset: Date.now()
  },

  record(responseTime, statusCode) {
    this.metrics.requests++;
    this.metrics.totalResponseTime += responseTime;
    
    if (responseTime > 500) {
      this.metrics.slowRequests++;
    }
    
    if (statusCode >= 400) {
      this.metrics.errors++;
    }
  },

  generateReport() {
    const now = Date.now();
    const timePeriod = now - this.metrics.lastReset;
    const avgResponseTime = this.metrics.requests > 0 ? 
      Math.round(this.metrics.totalResponseTime / this.metrics.requests) : 0;

    const report = {
      period_minutes: Math.round(timePeriod / 60 / 1000),
      total_requests: this.metrics.requests,
      average_response_time_ms: avgResponseTime,
      slow_requests: this.metrics.slowRequests,
      error_requests: this.metrics.errors,
      requests_per_minute: Math.round(this.metrics.requests / (timePeriod / 60 / 1000)),
      error_rate_percent: this.metrics.requests > 0 ? 
        Math.round((this.metrics.errors / this.metrics.requests) * 100) : 0
    };

    logInfo('PERFORMANCE_SUMMARY', 'Driver QR system performance summary', report);

    // Reset metrics
    this.metrics = {
      requests: 0,
      totalResponseTime: 0,
      slowRequests: 0,
      errors: 0,
      lastReset: now
    };

    return report;
  },

  startReporting() {
    // Generate report every 15 minutes
    setInterval(() => this.generateReport(), 15 * 60 * 1000);
    logInfo('PERFORMANCE_REPORTING_STARTED', 'Performance reporting started');
  }
};

module.exports = {
  performanceMonitor,
  queryPerformanceMonitor,
  memoryMonitor,
  performanceSummary
};