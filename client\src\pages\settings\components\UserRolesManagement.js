import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import { getApiBaseUrl } from '../../../utils/network-utils';

// API configuration with dynamic base URL
const getApiEndpoints = () => {
    const API_BASE_URL = getApiBaseUrl();
    return {
        roles: `${API_BASE_URL}/roles`,
        permissions: `${API_BASE_URL}/permissions`,
        roleUsers: (roleName) => `${API_BASE_URL}/roles/${roleName}/users`
    };
};

const UserRolesManagement = () => {
    const navigate = useNavigate();
    const [roles, setRoles] = useState([]);
    const [permissions, setPermissions] = useState({});
    const [pages, setPages] = useState([]);
    const [loading, setLoading] = useState(true);
    const [saving, setSaving] = useState(false);
    const [newRoleName, setNewRoleName] = useState('');
    const [showCreateForm, setShowCreateForm] = useState(false);
    const [selectedRoleUsers, setSelectedRoleUsers] = useState(null);
    const [roleUsers, setRoleUsers] = useState([]);
    const [loadingUsers, setLoadingUsers] = useState(false);

    // Get auth token from localStorage
    const getAuthToken = () => {
        const token = localStorage.getItem('hauling_token');
        return token;
    };

    // API configuration
    const getApiConfig = () => {
        const token = getAuthToken();
        if (!token) {
            toast.error('Authentication token not found. Please log in again.');
            return null;
        }
        return {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        };
    };

    // Load roles and permissions data
    const loadData = async () => {
        setLoading(true);
        try {
            const config = getApiConfig();
            if (!config) {
                setLoading(false);
                return;
            }

            const API_ENDPOINTS = getApiEndpoints();

            // Load roles
            const rolesResponse = await axios.get(API_ENDPOINTS.roles, config);
            setRoles(rolesResponse.data.data);

            // Load permissions
            const permissionsResponse = await axios.get(API_ENDPOINTS.permissions, config);
            setPermissions(permissionsResponse.data.data.matrix);
            setPages(permissionsResponse.data.data.pages);

        } catch (error) {
            console.error('Error loading data:', error);
            if (error.response?.status === 401) {
                toast.error('Authentication failed. Please log in again.');
            } else {
                const message = error.response?.data?.message || 'Failed to load roles and permissions data';
                toast.error(message);
            }
        } finally {
            setLoading(false);
        }
    };

    // Navigate to Users page with role filter
    const navigateToUsersPage = (roleName) => {
        // Navigate to users page with role filter as URL parameter
        navigate(`/users?role=${roleName}`);
    };

    // Load users for a specific role (for modal display)
    const loadRoleUsers = async (roleName) => {
        setLoadingUsers(true);
        try {
            const config = getApiConfig();
            if (!config) {
                setLoadingUsers(false);
                return;
            }
            const API_ENDPOINTS = getApiEndpoints();
            const response = await axios.get(API_ENDPOINTS.roleUsers(roleName), config);
            setRoleUsers(response.data.data.users);
            setSelectedRoleUsers(roleName);
        } catch (error) {
            console.error('Error loading role users:', error);
            toast.error('Failed to load users for this role');
        } finally {
            setLoadingUsers(false);
        }
    };

    // Create new role
    const createRole = async () => {
        if (!newRoleName.trim()) {
            toast.error('Please enter a role name');
            return;
        }

        // Validate role name format
        if (!/^[a-zA-Z_]+$/.test(newRoleName)) {
            toast.error('Role name can only contain letters and underscores');
            return;
        }

        // Additional validation
        if (newRoleName.length > 50) {
            toast.error('Role name must be 50 characters or less');
            return;
        }

        if (newRoleName.toLowerCase() === 'admin' || newRoleName.toLowerCase() === 'user') {
            toast.error('This role name is reserved');
            return;
        }

        try {
            const config = getApiConfig();
            if (!config) return;

            const API_ENDPOINTS = getApiEndpoints();
            await axios.post(API_ENDPOINTS.roles, {
                role_name: newRoleName.toLowerCase()
            }, config);

            toast.success('Role created successfully');
            setNewRoleName('');
            setShowCreateForm(false);
            loadData(); // Reload data
        } catch (error) {
            console.error('Error creating role:', error);
            const message = error.response?.data?.message || 'Failed to create role';
            toast.error(message);
        }
    };

    // Delete role
    const deleteRole = async (roleName) => {
        if (!window.confirm(`Are you sure you want to delete the role "${roleName}"?`)) {
            return;
        }

        try {
            const config = getApiConfig();
            if (!config) return;

            const API_ENDPOINTS = getApiEndpoints();
            await axios.delete(`${API_ENDPOINTS.roles}/${roleName}`, config);

            toast.success('Role deleted successfully');
            loadData(); // Reload data
        } catch (error) {
            console.error('Error deleting role:', error);
            const message = error.response?.data?.message || 'Failed to delete role';
            toast.error(message);
        }
    };

    // Remove unused updatePermission function - permissions are saved in bulk

    // Bulk save permissions
    const saveAllPermissions = async () => {
        setSaving(true);
        try {
            const config = getApiConfig();
            if (!config) {
                setSaving(false);
                return;
            }

            const API_ENDPOINTS = getApiEndpoints();

            // Convert permissions matrix to array format
            const permissionsArray = [];
            Object.keys(permissions).forEach(roleName => {
                Object.keys(permissions[roleName]).forEach(pageKey => {
                    permissionsArray.push({
                        role_name: roleName,
                        page_key: pageKey,
                        has_access: permissions[roleName][pageKey]
                    });
                });
            });

            await axios.post(API_ENDPOINTS.permissions, {
                permissions: permissionsArray
            }, config);

            toast.success('All permissions saved successfully');
        } catch (error) {
            console.error('Error saving permissions:', error);
            toast.error('Failed to save permissions');
        } finally {
            setSaving(false);
        }
    };

    // Load data on component mount
    useEffect(() => {
        loadData();
    }, []); // eslint-disable-line react-hooks/exhaustive-deps

    if (loading) {
        return (
            <div className="p-6 flex items-center justify-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <span className="ml-2 text-secondary-600">Loading roles and permissions...</span>
            </div>
        );
    }

    return (
        <div className="p-6 space-y-8">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h2 className="text-2xl font-bold text-secondary-900">👥 User Roles Management</h2>
                    <p className="text-secondary-600 mt-1">Manage user roles and their page access permissions</p>
                </div>
                <div className="flex items-center space-x-3">
                    <button
                        onClick={() => setShowCreateForm(!showCreateForm)}
                        className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm font-medium"
                    >
                        {showCreateForm ? 'Cancel' : '+ Add Role'}
                    </button>
                    <button
                        onClick={saveAllPermissions}
                        disabled={saving}
                        className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors text-sm font-medium disabled:opacity-50"
                    >
                        {saving ? 'Saving...' : 'Save All Changes'}
                    </button>
                </div>
            </div>

            {/* Create Role Form */}
            {showCreateForm && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                    <h3 className="text-lg font-semibold text-blue-900 mb-4">Create New Role</h3>
                    <div className="flex items-end space-x-4">
                        <div className="flex-1">
                            <label className="block text-sm font-medium text-blue-700 mb-2">
                                Role Name
                            </label>
                            <input
                                type="text"
                                value={newRoleName}
                                onChange={(e) => setNewRoleName(e.target.value)}
                                placeholder="e.g., manager, checker, dispatcher"
                                className="w-full px-3 py-2 border border-blue-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                onKeyDown={(e) => e.key === 'Enter' && createRole()}
                            />
                            <p className="text-xs text-blue-600 mt-1">
                                Use lowercase letters and underscores only (e.g., fleet_manager)
                            </p>
                        </div>
                        <button
                            onClick={createRole}
                            className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors font-medium"
                        >
                            Create Role
                        </button>
                    </div>
                </div>
            )}

            {/* Roles Overview */}
            <div className="bg-white rounded-lg border border-secondary-200 p-6">
                <h3 className="text-lg font-semibold text-secondary-900 mb-4">📋 Roles Overview</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {roles.map((role) => (
                        <div key={role.role_name} className="border border-secondary-200 rounded-lg p-4">
                            <div className="flex items-center justify-between mb-2">
                                <h4 className="font-medium text-secondary-900 capitalize">
                                    {role.role_name.replace('_', ' ')}
                                </h4>
                                {role.can_delete && (
                                    <button
                                        onClick={() => deleteRole(role.role_name)}
                                        className="text-red-500 hover:text-red-700 text-sm"
                                        title="Delete role"
                                    >
                                        🗑️
                                    </button>
                                )}
                            </div>
                            <p className="text-sm text-secondary-600 mb-3">
                                {role.user_count} user{role.user_count !== 1 ? 's' : ''} assigned
                            </p>
                            <div className="flex items-center space-x-2">
                                <button
                                    onClick={() => navigateToUsersPage(role.role_name)}
                                    className="px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 font-medium transition-colors"
                                >
                                    Manage Users →
                                </button>
                                <button
                                    onClick={() => loadRoleUsers(role.role_name)}
                                    className="text-xs text-blue-600 hover:text-blue-800 font-medium"
                                >
                                    Quick View
                                </button>
                            </div>
                        </div>
                    ))}
                </div>
            </div>

            {/* Role Users Modal */}
            {selectedRoleUsers && (
                <div className="bg-white rounded-lg border border-secondary-200 p-6">
                    <div className="flex items-center justify-between mb-4">
                        <h3 className="text-lg font-semibold text-secondary-900">
                            👤 Users with "{selectedRoleUsers}" role
                        </h3>
                        <button
                            onClick={() => setSelectedRoleUsers(null)}
                            className="text-secondary-500 hover:text-secondary-700"
                        >
                            ✕
                        </button>
                    </div>

                    {loadingUsers ? (
                        <div className="flex items-center justify-center py-8">
                            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                            <span className="ml-2 text-secondary-600">Loading users...</span>
                        </div>
                    ) : roleUsers.length > 0 ? (
                        <div className="space-y-2">
                            {roleUsers.map((user) => (
                                <div key={user.id} className="flex items-center justify-between p-3 bg-secondary-50 rounded-md">
                                    <div>
                                        <p className="font-medium text-secondary-900">{user.full_name}</p>
                                        <p className="text-sm text-secondary-600">{user.username} • {user.email}</p>
                                    </div>
                                    <span className={`px-2 py-1 text-xs rounded-full ${user.status === 'active'
                                            ? 'bg-green-100 text-green-800'
                                            : 'bg-red-100 text-red-800'
                                        }`}>
                                        {user.status}
                                    </span>
                                </div>
                            ))}
                        </div>
                    ) : (
                        <p className="text-secondary-600 text-center py-8">
                            No users assigned to this role
                        </p>
                    )}
                </div>
            )}

            {/* Permissions Matrix */}
            <div className="bg-white rounded-lg border border-secondary-200 p-6">
                <h3 className="text-lg font-semibold text-secondary-900 mb-4">🔐 Permissions Matrix</h3>
                <p className="text-sm text-secondary-600 mb-6">
                    Configure which pages each role can access. Changes are saved automatically when you click "Save All Changes".
                </p>

                <div className="overflow-x-auto">
                    <table className="min-w-full">
                        <thead>
                            <tr className="border-b border-secondary-200">
                                <th className="text-left py-3 px-4 font-medium text-secondary-900">
                                    Role / Page
                                </th>
                                {pages.map((page) => (
                                    <th key={page} className="text-center py-3 px-2 font-medium text-secondary-900 min-w-[100px]">
                                        <div className="capitalize text-sm">
                                            {page.replace('_', ' ')}
                                        </div>
                                    </th>
                                ))}
                            </tr>
                        </thead>
                        <tbody>
                            {roles.map((role) => (
                                <tr key={role.role_name} className="border-b border-secondary-100 hover:bg-secondary-50">
                                    <td className="py-3 px-4 font-medium text-secondary-900 capitalize">
                                        {role.role_name.replace('_', ' ')}
                                    </td>
                                    {pages.map((page) => (
                                        <td key={`${role.role_name}-${page}`} className="text-center py-3 px-2">
                                            <label className="inline-flex items-center cursor-pointer">
                                                <input
                                                    type="checkbox"
                                                    checked={permissions[role.role_name]?.[page] || false}
                                                    onChange={(e) => {
                                                        const newValue = e.target.checked;
                                                        setPermissions(prev => ({
                                                            ...prev,
                                                            [role.role_name]: {
                                                                ...prev[role.role_name],
                                                                [page]: newValue
                                                            }
                                                        }));
                                                    }}
                                                    className="w-4 h-4 text-blue-600 border-secondary-300 rounded focus:ring-blue-500"
                                                />
                                                <span className="sr-only">
                                                    {permissions[role.role_name]?.[page] ? 'Revoke' : 'Grant'} {page} access for {role.role_name}
                                                </span>
                                            </label>
                                        </td>
                                    ))}
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>

                {/* Legend */}
                <div className="mt-6 flex items-center space-x-6 text-sm text-secondary-600">
                    <div className="flex items-center space-x-2">
                        <input type="checkbox" checked readOnly className="w-4 h-4 text-blue-600" />
                        <span>Has Access</span>
                    </div>
                    <div className="flex items-center space-x-2">
                        <input type="checkbox" readOnly className="w-4 h-4 text-blue-600" />
                        <span>No Access</span>
                    </div>
                    <div className="text-yellow-600">
                        ⚠️ Core roles (admin, supervisor, operator) cannot be deleted
                    </div>
                </div>
            </div>

            {/* Quick Actions */}
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-yellow-800 mb-4">⚡ Quick Actions</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <button
                        onClick={() => {
                            // Grant all permissions to admin
                            setPermissions(prev => ({
                                ...prev,
                                admin: Object.fromEntries(pages.map(page => [page, true]))
                            }));
                            toast.success('Admin granted all permissions');
                        }}
                        className="p-3 bg-green-100 text-green-800 rounded-md hover:bg-green-200 transition-colors text-sm font-medium"
                    >
                        🔓 Grant Admin All Access
                    </button>
                    <button
                        onClick={() => {
                            // Revoke all permissions from operator
                            setPermissions(prev => ({
                                ...prev,
                                operator: Object.fromEntries(pages.map(page => [page, false]))
                            }));
                            toast.success('Operator permissions revoked');
                        }}
                        className="p-3 bg-red-100 text-red-800 rounded-md hover:bg-red-200 transition-colors text-sm font-medium"
                    >
                        🔒 Revoke Operator All Access
                    </button>
                    <button
                        onClick={loadData}
                        className="p-3 bg-blue-100 text-blue-800 rounded-md hover:bg-blue-200 transition-colors text-sm font-medium"
                    >
                        🔄 Refresh Data
                    </button>
                </div>
            </div>
        </div>
    );
};

export default UserRolesManagement;