import React from 'react';

const StatusBadge = ({ status, timeInPhase }) => {
  const getStatusConfig = (status, timeInPhase) => {
    switch (status) {
      case 'stopped':
        return {
          color: 'bg-red-100 text-red-800 border-red-200',
          icon: '⏹️',
          label: 'Stopped'
        };
      case 'active':
        // Check if overdue (more than 2 hours in phase)
        if (timeInPhase > 120) {
          return { 
            color: 'bg-yellow-100 text-yellow-800 border-yellow-200', 
            icon: '⚠️', 
            label: 'Overdue' 
          };
        }
        return { 
          color: 'bg-green-100 text-green-800 border-green-200', 
          icon: '🟢', 
          label: 'Active' 
        };
      case 'assigned':
        return { 
          color: 'bg-blue-100 text-blue-800 border-blue-200', 
          icon: '📋', 
          label: 'Assigned' 
        };
      case 'completed':
        return { 
          color: 'bg-gray-100 text-gray-800 border-gray-200', 
          icon: '✅', 
          label: 'Completed' 
        };
      default:
        return { 
          color: 'bg-gray-100 text-gray-800 border-gray-200', 
          icon: '⭕', 
          label: 'Idle' 
        };
    }
  };

  const config = getStatusConfig(status, timeInPhase);

  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${config.color}`}>
      <span className="mr-1">{config.icon}</span>
      {config.label}
    </span>
  );
};

const formatTimeInPhase = (minutes) => {
  if (minutes < 60) {
    return `${Math.round(minutes)}m`;
  } else {
    const hours = Math.floor(minutes / 60);
    const mins = Math.round(minutes % 60);
    return `${hours}h ${mins}m`;
  }
};

const FleetStatusGrid = ({ data, loading }) => {
  if (loading) {
    return (
      <div className="animate-pulse space-y-4">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="bg-secondary-200 h-16 rounded-lg"></div>
        ))}
      </div>
    );
  }

  if (!data || !data.fleetStatus || data.fleetStatus.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow border border-secondary-200 p-8 text-center">
        <span className="text-4xl block mb-2">🚛</span>
        <p className="text-secondary-500">No fleet status data available</p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow border border-secondary-200 overflow-hidden">
      {/* Desktop View */}
      <div className="hidden md:block">
        <table className="min-w-full divide-y divide-secondary-200">
          <thead className="bg-secondary-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                Truck
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                Driver
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                Current Location
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                Trip Phase
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                Time in Phase
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                Status
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-secondary-200">
            {data.fleetStatus.map((truck, index) => (
              <tr key={`truck-${truck.truckId}-${index}`} className="hover:bg-secondary-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="text-sm font-medium text-secondary-900">
                      {truck.truckNumber}
                    </div>
                    {truck.assignmentCode && (
                      <div className="text-xs text-secondary-500 ml-2">
                        {truck.assignmentCode}
                      </div>
                    )}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-secondary-900">
                    {truck.driverName}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-secondary-900">
                    {truck.currentLocation}
                  </div>
                  {truck.loadingLocation && truck.unloadingLocation && (
                    <div className="text-xs text-secondary-500">
                      {truck.loadingLocation} → {truck.unloadingLocation}
                    </div>
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-secondary-900">
                    {truck.tripStatus === 'loading_start' && '⬆️ Loading'}
                    {truck.tripStatus === 'loading_end' && '🚛 To Unload'}
                    {truck.tripStatus === 'unloading_start' && '⬇️ Unloading'}
                    {truck.tripStatus === 'unloading_end' && '🔄 To Load'}
                    {truck.tripStatus === 'stopped' && '⏹️ Stopped'}
                    {truck.tripStatus === 'assigned' && '📋 Assigned'}
                    {!truck.tripStatus && '⭕ Idle'}
                  </div>
                  {truck.tripNumber && (
                    <div className="text-xs text-secondary-500">
                      Trip #{truck.tripNumber}
                    </div>
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-secondary-900">
                  {formatTimeInPhase(truck.timeInPhaseMinutes)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <StatusBadge 
                    status={truck.statusCategory} 
                    timeInPhase={truck.timeInPhaseMinutes}
                  />
                  {truck.isException && (
                    <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-orange-100 text-orange-800">
                      Exception
                    </span>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Mobile View */}
      <div className="md:hidden">
        <div className="space-y-4 p-4">
          {data.fleetStatus.map((truck, index) => (
            <div key={`mobile-truck-${truck.truckId}-${index}`} className="bg-secondary-50 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <div className="font-medium text-secondary-900">
                  {truck.truckNumber}
                </div>
                <StatusBadge 
                  status={truck.statusCategory} 
                  timeInPhase={truck.timeInPhaseMinutes}
                />
              </div>
              
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span className="text-secondary-500">Driver:</span>
                  <span className="text-secondary-900">{truck.driverName}</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-secondary-500">Location:</span>
                  <span className="text-secondary-900">{truck.currentLocation}</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-secondary-500">Phase:</span>
                  <span className="text-secondary-900">
                    {truck.tripStatus === 'loading_start' && '⬆️ Loading'}
                    {truck.tripStatus === 'loading_end' && '🚛 To Unload'}
                    {truck.tripStatus === 'unloading_start' && '⬇️ Unloading'}
                    {truck.tripStatus === 'unloading_end' && '🔄 To Load'}
                    {truck.tripStatus === 'stopped' && '⏹️ Stopped'}
                    {truck.tripStatus === 'assigned' && '📋 Assigned'}
                    {!truck.tripStatus && '⭕ Idle'}
                  </span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-secondary-500">Time in Phase:</span>
                  <span className="text-secondary-900">
                    {formatTimeInPhase(truck.timeInPhaseMinutes)}
                  </span>
                </div>
                
                {truck.assignmentCode && (
                  <div className="flex justify-between">
                    <span className="text-secondary-500">Assignment:</span>
                    <span className="text-secondary-900">{truck.assignmentCode}</span>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default FleetStatusGrid;
