import React, { useState, useEffect } from 'react';

const DateRangeFilter = ({ 
  startDate, 
  endDate, 
  onDateChange, 
  disabled = false,
  label = "Date Range",
  className = "",
  showPresets = true 
}) => {
  const [validationError, setValidationError] = useState('');

  // Validate date range
  useEffect(() => {
    if (startDate && endDate) {
      const start = new Date(startDate);
      const end = new Date(endDate);
      
      if (end < start) {
        setValidationError('End date must be after start date');
      } else {
        const daysDiff = Math.ceil((end - start) / (1000 * 60 * 60 * 24));
        if (daysDiff > 365) {
          setValidationError('Date range cannot exceed 365 days');
        } else {
          setValidationError('');
        }
      }
    } else {
      setValidationError('');
    }
  }, [startDate, endDate]);

  // Quick preset functions
  const handlePreset = (preset) => {
    const phDateFormatter = new Intl.DateTimeFormat('en-CA', { timeZone: 'Asia/Manila' });
    const today = new Date();
    const todayStr = phDateFormatter.format(today);

    let startDateStr = '';
    let endDateStr = todayStr;

    switch (preset) {
      case 'today':
        startDateStr = todayStr;
        break;
      case 'yesterday':
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);
        startDateStr = phDateFormatter.format(yesterday);
        endDateStr = startDateStr;
        break;
      case 'last7days':
        const week = new Date(today);
        week.setDate(week.getDate() - 7);
        startDateStr = phDateFormatter.format(week);
        break;
      case 'last30days':
        const month = new Date(today);
        month.setDate(month.getDate() - 30);
        startDateStr = phDateFormatter.format(month);
        break;
      case 'thisMonth':
        const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
        startDateStr = phDateFormatter.format(firstDay);
        break;
      case 'lastMonth':
        const lastMonthFirst = new Date(today.getFullYear(), today.getMonth() - 1, 1);
        const lastMonthLast = new Date(today.getFullYear(), today.getMonth(), 0);
        startDateStr = phDateFormatter.format(lastMonthFirst);
        endDateStr = phDateFormatter.format(lastMonthLast);
        break;
      default:
        break;
    }

    if (startDateStr || endDateStr) {
      onDateChange({
        date_from: startDateStr,
        date_to: endDateStr
      });
    }
  };

  const clearDates = () => {
    onDateChange({
      date_from: '',
      date_to: ''
    });
  };

  const formatDisplayDate = (dateStr) => {
    if (!dateStr) return '';
    return new Date(dateStr).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const getDaysDifference = () => {
    if (!startDate || !endDate) return 0;
    const start = new Date(startDate);
    const end = new Date(endDate);
    return Math.ceil((end - start) / (1000 * 60 * 60 * 24)) + 1;
  };

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Date Inputs */}
      <div className="grid grid-cols-2 gap-3">
        <div>
          <label htmlFor="start_date" className="block text-sm font-medium text-secondary-700 mb-1">
            Start Date
          </label>
          <input
            type="date"
            id="start_date"
            value={startDate}
            onChange={(e) => onDateChange({ date_from: e.target.value, date_to: endDate })}
            max={endDate || undefined}
            disabled={disabled}
            className={`input ${validationError ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
          />
        </div>

        <div>
          <label htmlFor="end_date" className="block text-sm font-medium text-secondary-700 mb-1">
            End Date
          </label>
          <input
            type="date"
            id="end_date"
            value={endDate}
            onChange={(e) => onDateChange({ date_from: startDate, date_to: e.target.value })}
            min={startDate || undefined}
            disabled={disabled}
            className={`input ${validationError ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
          />
        </div>
      </div>

      {/* Validation Error */}
      {validationError && (
        <div className="flex items-center text-sm text-red-600">
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
          {validationError}
        </div>
      )}

      {/* Date Range Info */}
      {startDate && endDate && !validationError && (
        <div className="flex items-center justify-between text-sm text-secondary-600 bg-blue-50 p-2 rounded">
          <span>
            📅 {formatDisplayDate(startDate)} → {formatDisplayDate(endDate)} (inclusive)
          </span>
          <span className="font-medium">
            {getDaysDifference()} {getDaysDifference() === 1 ? 'day' : 'days'}
          </span>
        </div>
      )}

      {/* Quick Presets */}
      {showPresets && (
        <div className="space-y-2">
          <div className="text-xs font-medium text-secondary-700 uppercase tracking-wider">
            Quick Filters
          </div>
          <div className="flex flex-wrap gap-2">
            <button
              type="button"
              onClick={() => handlePreset('today')}
              disabled={disabled}
              className="px-2 py-1 text-xs bg-secondary-100 text-secondary-700 rounded hover:bg-secondary-200 disabled:opacity-50"
            >
              Today
            </button>
            <button
              type="button"
              onClick={() => handlePreset('yesterday')}
              disabled={disabled}
              className="px-2 py-1 text-xs bg-secondary-100 text-secondary-700 rounded hover:bg-secondary-200 disabled:opacity-50"
            >
              Yesterday
            </button>
            <button
              type="button"
              onClick={() => handlePreset('last7days')}
              disabled={disabled}
              className="px-2 py-1 text-xs bg-secondary-100 text-secondary-700 rounded hover:bg-secondary-200 disabled:opacity-50"
            >
              Last 7 Days
            </button>
            <button
              type="button"
              onClick={() => handlePreset('last30days')}
              disabled={disabled}
              className="px-2 py-1 text-xs bg-secondary-100 text-secondary-700 rounded hover:bg-secondary-200 disabled:opacity-50"
            >
              Last 30 Days
            </button>
            <button
              type="button"
              onClick={() => handlePreset('thisMonth')}
              disabled={disabled}
              className="px-2 py-1 text-xs bg-secondary-100 text-secondary-700 rounded hover:bg-secondary-200 disabled:opacity-50"
            >
              This Month
            </button>
            <button
              type="button"
              onClick={() => handlePreset('lastMonth')}
              disabled={disabled}
              className="px-2 py-1 text-xs bg-secondary-100 text-secondary-700 rounded hover:bg-secondary-200 disabled:opacity-50"
            >
              Last Month
            </button>
            {(startDate || endDate) && (
              <button
                type="button"
                onClick={clearDates}
                disabled={disabled}
                className="px-2 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200 disabled:opacity-50"
              >
                Clear
              </button>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default DateRangeFilter;