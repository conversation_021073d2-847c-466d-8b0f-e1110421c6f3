/**
 * Custom error class for business rule validation errors
 * These should return 400 Bad Request instead of 500 Internal Server Error
 * 
 * @class ValidationError
 * @extends Error
 * @example
 * // Basic usage
 * throw new ValidationError('Invalid truck assignment');
 * 
 * @example
 * // With details for structured error responses
 * throw new ValidationError('Trip validation failed', {
 *   field: 'truck_id',
 *   code: 'TRUCK_NOT_FOUND',
 *   context: { truck_id: 123 }
 * });
 */
class ValidationError extends Error {
  /**
   * Creates a new ValidationError instance
   * @param {string} message - The error message describing the validation failure
   * @param {Object} [details={}] - Additional error details for structured responses
   * @param {string} [details.field] - The field that failed validation
   * @param {string} [details.code] - Error code for programmatic handling
   * @param {Object} [details.context] - Additional context data
   * @param {string} [details.suggestion] - Suggested action to resolve the error
   */
  constructor(message, details = {}) {
    // Input validation
    if (typeof message !== 'string' || message.trim().length === 0) {
      throw new TypeError('ValidationError message must be a non-empty string');
    }
    
    if (details !== null && typeof details !== 'object') {
      throw new TypeError('ValidationError details must be an object or null');
    }

    super(message);
    this.name = 'ValidationError';
    this.isValidationError = true;
    this.details = details || {};
    
    // Capture stack trace
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, ValidationError);
    }
  }

  // Static factory methods removed - these were unused convenience methods
  // The main ValidationError constructor provides all necessary functionality

  /**
   * Converts the error to a structured JSON response suitable for API responses
   * @returns {Object} Structured error object
   */
  toJSON() {
    return {
      error: this.name,
      message: this.message,
      details: this.details,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Checks if an error is a ValidationError instance
   * @param {*} error - The error to check
   * @returns {boolean} True if the error is a ValidationError
   * @static
   */
  static isValidationError(error) {
    return error instanceof ValidationError || error?.isValidationError === true;
  }
}

module.exports = ValidationError;