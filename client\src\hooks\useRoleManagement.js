import { useState, useCallback } from 'react';
import { toast } from 'react-hot-toast';
import axios from 'axios';

import { getApiBaseUrl } from '../utils/network-utils';

// API configuration with dynamic base URL
const getApiEndpoints = () => {
  const API_BASE_URL = getApiBaseUrl();
  return {
    roles: `${API_BASE_URL}/roles`,
    permissions: `${API_BASE_URL}/permissions`,
    roleUsers: (roleName) => `${API_BASE_URL}/roles/${roleName}/users`
  };
};

export const useRoleManagement = () => {
  const [roles, setRoles] = useState([]);
  const [permissions, setPermissions] = useState({});
  const [pages, setPages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  // Get auth token from localStorage
  const getAuthToken = useCallback(() => {
    const authData = localStorage.getItem('auth');
    if (authData) {
      try {
        const parsed = JSON.parse(authData);
        return parsed.token;
      } catch (error) {
        console.error('Error parsing auth data:', error);
        return null;
      }
    }
    return null;
  }, []);

  // API configuration
  const getApiConfig = useCallback(() => {
    const token = getAuthToken();
    return {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    };
  }, [getAuthToken]);

  // Load roles and permissions data
  const loadData = useCallback(async () => {
    setLoading(true);
    try {
      const config = getApiConfig();
      const API_ENDPOINTS = getApiEndpoints();

      // Load roles
      const rolesResponse = await axios.get(API_ENDPOINTS.roles, config);
      setRoles(rolesResponse.data.data);

      // Load permissions
      const permissionsResponse = await axios.get(API_ENDPOINTS.permissions, config);
      setPermissions(permissionsResponse.data.data.matrix);
      setPages(permissionsResponse.data.data.pages);

    } catch (error) {
      console.error('Error loading data:', error);
      const message = error.response?.data?.message || 'Failed to load roles and permissions data';
      toast.error(message);
    } finally {
      setLoading(false);
    }
  }, [getApiConfig]);

  // Create new role
  const createRole = useCallback(async (roleName) => {
    if (!roleName.trim()) {
      toast.error('Please enter a role name');
      return false;
    }

    // Validate role name format
    if (!/^[a-zA-Z_]+$/.test(roleName)) {
      toast.error('Role name can only contain letters and underscores');
      return false;
    }

    // Additional validation
    if (roleName.length > 50) {
      toast.error('Role name must be 50 characters or less');
      return false;
    }

    if (roleName.toLowerCase() === 'admin' || roleName.toLowerCase() === 'user') {
      toast.error('This role name is reserved');
      return false;
    }

    try {
      const config = getApiConfig();
      const API_ENDPOINTS = getApiEndpoints();
      await axios.post(API_ENDPOINTS.roles, {
        role_name: roleName.toLowerCase()
      }, config);

      toast.success('Role created successfully');
      loadData(); // Reload data
      return true;
    } catch (error) {
      console.error('Error creating role:', error);
      const message = error.response?.data?.message || 'Failed to create role';
      toast.error(message);
      return false;
    }
  }, [getApiConfig, loadData]);

  // Delete role
  const deleteRole = useCallback(async (roleName) => {
    if (!window.confirm(`Are you sure you want to delete the role "${roleName}"?`)) {
      return false;
    }

    try {
      const config = getApiConfig();
      const API_ENDPOINTS = getApiEndpoints();
      await axios.delete(`${API_ENDPOINTS.roles}/${roleName}`, config);
      
      toast.success('Role deleted successfully');
      loadData(); // Reload data
      return true;
    } catch (error) {
      console.error('Error deleting role:', error);
      const message = error.response?.data?.message || 'Failed to delete role';
      toast.error(message);
      return false;
    }
  }, [getApiConfig, loadData]);

  // Load users for a specific role
  const loadRoleUsers = useCallback(async (roleName) => {
    try {
      const config = getApiConfig();
      const API_ENDPOINTS = getApiEndpoints();
      const response = await axios.get(API_ENDPOINTS.roleUsers(roleName), config);
      return response.data.data.users;
    } catch (error) {
      console.error('Error loading role users:', error);
      toast.error('Failed to load users for this role');
      return [];
    }
  }, [getApiConfig]);

  // Bulk save permissions
  const saveAllPermissions = useCallback(async () => {
    setSaving(true);
    try {
      const config = getApiConfig();
      const API_ENDPOINTS = getApiEndpoints();

      // Convert permissions matrix to array format
      const permissionsArray = [];
      Object.keys(permissions).forEach(roleName => {
        Object.keys(permissions[roleName]).forEach(pageKey => {
          permissionsArray.push({
            role_name: roleName,
            page_key: pageKey,
            has_access: permissions[roleName][pageKey]
          });
        });
      });

      await axios.post(API_ENDPOINTS.permissions, {
        permissions: permissionsArray
      }, config);

      toast.success('All permissions saved successfully');
      return true;
    } catch (error) {
      console.error('Error saving permissions:', error);
      toast.error('Failed to save permissions');
      return false;
    } finally {
      setSaving(false);
    }
  }, [getApiConfig, permissions]);

  return {
    // State
    roles,
    permissions,
    pages,
    loading,
    saving,
    
    // Actions
    loadData,
    createRole,
    deleteRole,
    loadRoleUsers,
    saveAllPermissions,
    setPermissions
  };
};