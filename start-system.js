#!/usr/bin/env node

/**
 * Hauling QR Trip System - Unified Startup Script
 * Automatically detects IP address and starts both frontend and backend
 */

const { spawn, exec } = require('child_process');
const path = require('path');
const fs = require('fs');
const IPDetectionService = require('./utils/ip-detection');

class SystemStarter {
  constructor() {
    this.ipDetection = new IPDetectionService();
    this.processes = [];
    this.isShuttingDown = false;
  }

  /**
   * Main startup sequence
   */
  async start() {
    console.log('🚀 HAULING QR TRIP SYSTEM - UNIFIED STARTUP');
    console.log('==========================================\n');

    try {
      // Step 1: IP Detection and Configuration
      console.log('1. 🔍 IP Detection and Configuration...');
      const ipDetected = await this.ipDetection.detectAndUpdate();
      
      if (!ipDetected) {
        console.log('⚠️  IP detection failed, continuing with fallback configuration...');
      }

      // Display configuration summary
      const config = this.ipDetection.getConfigSummary();
      console.log('\n📋 Configuration Summary:');
      console.log(`   🌐 Detected IP: ${config.detectedIP}`);
      console.log(`   🔗 API URL: ${config.apiUrl}`);
      console.log(`   🔌 WebSocket URL: ${config.wsUrl}`);
      console.log(`   🤖 Auto-detect: ${config.autoDetectEnabled ? 'Enabled' : 'Disabled'}`);
      console.log('');

      // Step 2: Check Prerequisites
      console.log('2. 🔧 Checking Prerequisites...');
      await this.checkPrerequisites();

      // Step 3: Start Backend Server
      console.log('3. 🖥️  Starting Backend Server...');
      await this.startBackend();

      // Step 4: Start Frontend Development Server
      console.log('4. 💻 Starting Frontend Development Server...');
      await this.startFrontend();

      // Step 5: Setup Process Management
      this.setupProcessManagement();

      console.log('\n🎉 SYSTEM STARTUP COMPLETED SUCCESSFULLY!');
      console.log('========================================');
      console.log(`📱 Frontend: http://${config.detectedIP}:${config.CLIENT_PORT}`);
      console.log(`🖥️  Backend HTTP: http://${config.detectedIP}:${config.BACKEND_HTTP_PORT}`);
      console.log(`🔐 Backend HTTPS: https://${config.detectedIP}:${config.HTTPS_PORT}`);
      console.log(`🔌 WebSocket: ${config.ENABLE_HTTPS ? 'wss' : 'ws'}://${config.detectedIP}:${config.ENABLE_HTTPS ? config.HTTPS_PORT : config.BACKEND_HTTP_PORT}`);
      console.log('\n💡 Press Ctrl+C to stop all services');

    } catch (error) {
      console.error('❌ System startup failed:', error.message);
      await this.shutdown();
      process.exit(1);
    }
  }

  /**
   * Check system prerequisites
   */
  async checkPrerequisites() {
    return new Promise((resolve, reject) => {
      // Check if Node.js modules are installed
      const backendNodeModules = path.join(__dirname, 'server', 'node_modules');
      const frontendNodeModules = path.join(__dirname, 'client', 'node_modules');

      if (!fs.existsSync(backendNodeModules)) {
        console.log('   ⚠️  Backend dependencies not found, installing...');
        this.installBackendDependencies().then(() => {
          if (!fs.existsSync(frontendNodeModules)) {
            console.log('   ⚠️  Frontend dependencies not found, installing...');
            this.installFrontendDependencies().then(resolve).catch(reject);
          } else {
            console.log('   ✅ Frontend dependencies found');
            resolve();
          }
        }).catch(reject);
      } else if (!fs.existsSync(frontendNodeModules)) {
        console.log('   ⚠️  Frontend dependencies not found, installing...');
        this.installFrontendDependencies().then(resolve).catch(reject);
      } else {
        console.log('   ✅ All dependencies found');
        resolve();
      }
    });
  }

  /**
   * Install backend dependencies
   */
  installBackendDependencies() {
    return new Promise((resolve, reject) => {
      console.log('   📦 Installing backend dependencies...');
      const install = spawn('npm', ['install'], {
        cwd: path.join(__dirname, 'server'),
        stdio: 'inherit',
        shell: true
      });

      install.on('close', (code) => {
        if (code === 0) {
          console.log('   ✅ Backend dependencies installed');
          resolve();
        } else {
          reject(new Error(`Backend dependency installation failed with code ${code}`));
        }
      });
    });
  }

  /**
   * Install frontend dependencies
   */
  installFrontendDependencies() {
    return new Promise((resolve, reject) => {
      console.log('   📦 Installing frontend dependencies...');
      const install = spawn('npm', ['install'], {
        cwd: path.join(__dirname, 'client'),
        stdio: 'inherit',
        shell: true
      });

      install.on('close', (code) => {
        if (code === 0) {
          console.log('   ✅ Frontend dependencies installed');
          resolve();
        } else {
          reject(new Error(`Frontend dependency installation failed with code ${code}`));
        }
      });
    });
  }

  /**
   * Start backend server
   */
  startBackend() {
    return new Promise((resolve, reject) => {
      const config = require('./config-loader').loadConfig();
      const port = config.ENABLE_HTTPS ? config.HTTPS_PORT : config.BACKEND_HTTP_PORT;
      const protocol = config.ENABLE_HTTPS ? 'HTTPS' : 'HTTP';
      console.log(`   🖥️  Launching backend server on port ${port} (${protocol})...`);
      
      const backend = spawn('node', ['server.js'], {
        cwd: path.join(__dirname, 'server'),
        stdio: 'pipe',
        shell: true,
        env: { ...process.env, NODE_ENV: 'development' }
      });

      this.processes.push({ name: 'Backend', process: backend });

      let backendReady = false;

      backend.stdout.on('data', (data) => {
        const output = data.toString();
        console.log(`[SERVER] ${output.trim()}`);
        
        if (output.includes('Server running on') || output.includes('listening on')) {
          if (!backendReady) {
            backendReady = true;
            console.log('   ✅ Backend server started successfully');
            resolve();
          }
        }
      });

      backend.stderr.on('data', (data) => {
        console.error(`[SERVER ERROR] ${data.toString().trim()}`);
      });

      backend.on('close', (code) => {
        if (!backendReady) {
          reject(new Error(`Backend server failed to start (exit code: ${code})`));
        }
      });

      // Timeout after 30 seconds
      setTimeout(() => {
        if (!backendReady) {
          reject(new Error('Backend server startup timeout'));
        }
      }, 30000);
    });
  }

  /**
   * Start frontend development server
   */
  startFrontend() {
    return new Promise((resolve, reject) => {
      console.log('   💻 Launching frontend development server on port 3000...');
      
      const frontend = spawn('npm', ['start'], {
        cwd: path.join(__dirname, 'client'),
        stdio: 'pipe',
        shell: true,
        env: { 
          ...process.env, 
          NODE_ENV: 'development',
          BROWSER: 'none' // Prevent auto-opening browser
        }
      });

      this.processes.push({ name: 'Frontend', process: frontend });

      let frontendReady = false;

      frontend.stdout.on('data', (data) => {
        const output = data.toString();
        console.log(`[CLIENT] ${output.trim()}`);
        
        if (output.includes('webpack compiled') || 
            output.includes('Local:') || 
            output.includes('compiled successfully')) {
          if (!frontendReady) {
            frontendReady = true;
            console.log('   ✅ Frontend development server started successfully');
            resolve();
          }
        }
      });

      frontend.stderr.on('data', (data) => {
        const output = data.toString();
        // Filter out common webpack warnings
        if (!output.includes('WARNING') && !output.includes('deprecated')) {
          console.error(`[CLIENT ERROR] ${output.trim()}`);
        }
      });

      frontend.on('close', (code) => {
        if (!frontendReady) {
          reject(new Error(`Frontend server failed to start (exit code: ${code})`));
        }
      });

      // Timeout after 60 seconds (frontend takes longer)
      setTimeout(() => {
        if (!frontendReady) {
          reject(new Error('Frontend server startup timeout'));
        }
      }, 60000);
    });
  }

  /**
   * Setup process management and cleanup
   */
  setupProcessManagement() {
    // Handle Ctrl+C
    process.on('SIGINT', () => {
      console.log('\n🛑 Received shutdown signal...');
      this.shutdown();
    });

    // Handle process termination
    process.on('SIGTERM', () => {
      console.log('\n🛑 Received termination signal...');
      this.shutdown();
    });

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      console.error('❌ Uncaught exception:', error);
      this.shutdown();
    });
  }

  /**
   * Shutdown all processes
   */
  async shutdown() {
    if (this.isShuttingDown) return;
    this.isShuttingDown = true;

    console.log('\n🔄 Shutting down all services...');

    for (const proc of this.processes) {
      try {
        console.log(`   🛑 Stopping ${proc.name}...`);
        proc.process.kill('SIGTERM');
        
        // Force kill after 5 seconds
        setTimeout(() => {
          if (!proc.process.killed) {
            proc.process.kill('SIGKILL');
          }
        }, 5000);
      } catch (error) {
        console.error(`   ❌ Error stopping ${proc.name}:`, error.message);
      }
    }

    console.log('✅ System shutdown completed');
    process.exit(0);
  }
}

// Run the system starter
if (require.main === module) {
  const starter = new SystemStarter();
  starter.start().catch((error) => {
    console.error('❌ System startup failed:', error);
    process.exit(1);
  });
}

module.exports = SystemStarter;
