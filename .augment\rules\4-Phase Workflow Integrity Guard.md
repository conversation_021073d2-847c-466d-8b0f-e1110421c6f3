---
type: "always_apply"
---

Analyze the modified files to ensure the 4-phase workflow integrity (PENDING → IN_PROGRESS → COMPLETED → VERIFIED) is maintained. Check for: 1) Proper state transition validation, 2) No bypassing of workflow phases, 3) Consistent state management across client and server, 4) Exception handling that preserves workflow integrity, 5) Database constraints that enforce proper state transitions. If any violations are detected, provide specific recommendations to restore workflow integrity. 