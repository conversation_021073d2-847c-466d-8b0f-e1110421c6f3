{"enabled": false, "name": "Documentation Sync", "description": "Monitors all JavaScript source files, configuration files, and database schemas for changes and triggers documentation updates in README or docs folder", "version": "1", "when": {"type": "userTriggered", "patterns": ["*.js", "client/src/**/*.js", "client/src/**/*.jsx", "server/**/*.js", "database/**/*.js", "database/**/*.sql", "scripts/**/*.js", "utils/**/*.js", "*.json", "client/package.json", "server/package.json", "*.md", "*.conf", "*.config.js"]}, "then": {"type": "askAgent", "prompt": "Source code or configuration files have been modified in this hauling QR trip management system. Please review the changes and update the relevant documentation. If there's a README.md file, update it to reflect any new features, API changes, or configuration updates. If there's a /docs folder with specific documentation files, update those as well. Focus on keeping the documentation accurate and helpful for developers and users of this full-stack JavaScript application with React frontend, Express backend, and PostgreSQL database."}}