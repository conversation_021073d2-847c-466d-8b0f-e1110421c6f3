/**
 * Dynamic Assignment Adaptation Service
 * 
 * This module provides intelligent assignment adaptation capabilities for the
 * Hauling QR Trip System. It automatically adapts assignments based on truck
 * movement patterns, location usage, and operational efficiency.
 * 
 * Key Features:
 * - Adaptive assignment creation based on truck patterns
 * - Smart location recognition and route optimization
 * - Real-time assignment updates based on actual movements
 * - Historical pattern analysis for better predictions
 * - Integration with existing exception flow and admin approval workflow
 */

const { getClient } = require('../config/database');
const { logger } = require('./logger');
const { assignmentValidator } = require('./AssignmentValidator');

/**
 * Adaptation Strategies
 */
const ADAPTATION_STRATEGIES = {
  PATTERN_BASED: 'pattern_based',      // Based on historical patterns
  PROXIMITY_BASED: 'proximity_based',  // Based on location proximity
  EFFICIENCY_BASED: 'efficiency_based', // Based on operational efficiency
  MANUAL_OVERRIDE: 'manual_override'   // Manual admin override
};

/**
 * Adaptation Confidence Levels
 */
const CONFIDENCE_LEVELS = {
  HIGH: 'high',       // >80% confidence - auto-approve
  MEDIUM: 'medium',   // 60-80% confidence - admin review
  LOW: 'low'          // <60% confidence - require manual approval
};

/**
 * Dynamic Assignment Adapter Class
 */
class DynamicAssignmentAdapter {
  constructor() {
    this.logger = logger;
    this.validator = assignmentValidator;
  }

  /**
   * Analyze truck movement patterns to suggest adaptive assignments
   * @param {string} truckNumber - Truck number
   * @param {number} currentLocationId - Current location ID
   * @param {Object} options - Analysis options
   * @returns {Promise<Object>} Adaptation suggestions
   */
  async analyzeMovementPatterns(truckNumber, currentLocationId, options = {}) {
    const client = options.client || await getClient();
    const shouldCloseClient = !options.client;

    try {
      // Get truck information
      const truckResult = await client.query(`
        SELECT id, truck_number, status FROM dump_trucks 
        WHERE truck_number = $1
      `, [truckNumber]);

      if (truckResult.rows.length === 0) {
        throw new Error(`Truck ${truckNumber} not found`);
      }

      const truck = truckResult.rows[0];

      // Analyze historical movement patterns (last 30 days)
      const patternAnalysis = await this._analyzeHistoricalPatterns(client, truck.id, currentLocationId);
      
      // Analyze proximity-based suggestions
      const proximityAnalysis = await this._analyzeProximityPatterns(client, currentLocationId);
      
      // Calculate efficiency metrics
      const efficiencyAnalysis = await this._analyzeEfficiencyMetrics(client, truck.id, currentLocationId);

      // Generate adaptation suggestions
      const suggestions = await this._generateAdaptationSuggestions({
        truck,
        currentLocationId,
        patternAnalysis,
        proximityAnalysis,
        efficiencyAnalysis
      });

      this.logger.info('Movement pattern analysis completed', {
        truck_number: truckNumber,
        current_location_id: currentLocationId,
        suggestions_count: suggestions.length
      });

      return {
        success: true,
        truck,
        currentLocationId,
        analysis: {
          patterns: patternAnalysis,
          proximity: proximityAnalysis,
          efficiency: efficiencyAnalysis
        },
        suggestions
      };

    } catch (error) {
      this.logger.error('Movement pattern analysis failed', {
        truck_number: truckNumber,
        current_location_id: currentLocationId,
        error: error.message
      });
      throw error;
    } finally {
      if (shouldCloseClient) {
        client.release();
      }
    }
  }

  /**
   * Create adaptive assignment based on analysis
   * @param {Object} params - Assignment parameters
   * @returns {Promise<Object>} Assignment creation result
   */
  async createAdaptiveAssignment(params) {
    const {
      truckId,
      driverId,
      loadingLocationId,
      unloadingLocationId,
      strategy = ADAPTATION_STRATEGIES.PATTERN_BASED,
      confidence = CONFIDENCE_LEVELS.MEDIUM,
      metadata = {}
    } = params;

    const client = await getClient();

    try {
      await client.query('BEGIN');

      // Generate assignment code
      const assignmentCode = await this._generateAdaptiveAssignmentCode(client, strategy);

      // Determine assignment status based on confidence level
      const status = this._determineAssignmentStatus(confidence);

      // Create the adaptive assignment
      const assignmentResult = await client.query(`
        INSERT INTO assignments (
          assignment_code, truck_id, driver_id, loading_location_id, unloading_location_id,
          assigned_date, status, priority, expected_loads_per_day, notes
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
        RETURNING *
      `, [
        assignmentCode,
        truckId,
        driverId,
        loadingLocationId,
        unloadingLocationId,
        new Date().toISOString().split('T')[0],
        status,
        'normal',
        1,
        JSON.stringify({
          adaptive_assignment: true,
          strategy,
          confidence,
          created_by: 'dynamic_adapter',
          metadata
        })
      ]);

      await client.query('COMMIT');

      const assignment = assignmentResult.rows[0];

      this.logger.info('Adaptive assignment created', {
        assignment_id: assignment.id,
        assignment_code: assignmentCode,
        strategy,
        confidence,
        status
      });

      return {
        success: true,
        assignment,
        strategy,
        confidence,
        requiresApproval: status === 'pending_approval'
      };

    } catch (error) {
      await client.query('ROLLBACK');
      this.logger.error('Adaptive assignment creation failed', {
        truck_id: truckId,
        strategy,
        error: error.message
      });
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Update existing assignment based on adaptive insights
   * @param {number} assignmentId - Assignment ID to update
   * @param {Object} adaptations - Adaptation parameters
   * @returns {Promise<Object>} Update result
   */
  async updateAssignmentAdaptively(assignmentId, adaptations) {
    const client = await getClient();

    try {
      await client.query('BEGIN');

      // Get current assignment
      const currentAssignment = await client.query(`
        SELECT * FROM assignments WHERE id = $1
      `, [assignmentId]);

      if (currentAssignment.rows.length === 0) {
        throw new Error(`Assignment ${assignmentId} not found`);
      }

      const assignment = currentAssignment.rows[0];

      // Apply adaptations
      const updates = this._buildAdaptiveUpdates(assignment, adaptations);

      if (Object.keys(updates.fields).length === 0) {
        return { success: true, message: 'No adaptations needed', assignment };
      }

      // Update assignment
      const updateQuery = this._buildUpdateQuery(updates);
      const updateResult = await client.query(updateQuery.sql, updateQuery.params);

      await client.query('COMMIT');

      this.logger.info('Assignment updated adaptively', {
        assignment_id: assignmentId,
        adaptations: Object.keys(updates.fields)
      });

      return {
        success: true,
        assignment: updateResult.rows[0],
        adaptations: updates.fields
      };

    } catch (error) {
      await client.query('ROLLBACK');
      this.logger.error('Adaptive assignment update failed', {
        assignment_id: assignmentId,
        error: error.message
      });
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Analyze historical movement patterns
   * @private
   */
  async _analyzeHistoricalPatterns(client, truckId, currentLocationId) {
    // Get historical trips for this truck (last 30 days)
    const historyResult = await client.query(`
      SELECT 
        tl.id,
        tl.status,
        a.loading_location_id,
        a.unloading_location_id,
        ll.name as loading_location_name,
        ul.name as unloading_location_name,
        tl.created_at
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE a.truck_id = $1 
        AND tl.created_at >= CURRENT_DATE - INTERVAL '30 days'
        AND tl.status = 'trip_completed'
      ORDER BY tl.created_at DESC
      LIMIT 100
    `, [truckId]);

    const trips = historyResult.rows;

    // Analyze patterns
    const locationFrequency = {};
    const routePatterns = {};

    trips.forEach(trip => {
      // Count location frequency
      [trip.loading_location_id, trip.unloading_location_id].forEach(locId => {
        if (locId) {
          locationFrequency[locId] = (locationFrequency[locId] || 0) + 1;
        }
      });

      // Count route patterns
      if (trip.loading_location_id && trip.unloading_location_id) {
        const route = `${trip.loading_location_id}-${trip.unloading_location_id}`;
        routePatterns[route] = (routePatterns[route] || 0) + 1;
      }
    });

    return {
      totalTrips: trips.length,
      locationFrequency,
      routePatterns,
      mostFrequentLocation: this._findMostFrequent(locationFrequency),
      mostFrequentRoute: this._findMostFrequent(routePatterns)
    };
  }

  /**
   * Analyze proximity-based patterns
   * @private
   */
  async _analyzeProximityPatterns(client, currentLocationId) {
    // Get nearby locations (simplified - could use actual coordinates)
    const nearbyResult = await client.query(`
      SELECT 
        l.id,
        l.name,
        l.type,
        COUNT(a.id) as assignment_count
      FROM locations l
      LEFT JOIN assignments a ON (l.id = a.loading_location_id OR l.id = a.unloading_location_id)
      WHERE l.id != $1 AND l.status = 'active'
      GROUP BY l.id, l.name, l.type
      ORDER BY assignment_count DESC
      LIMIT 10
    `, [currentLocationId]);

    return {
      nearbyLocations: nearbyResult.rows,
      proximityScore: nearbyResult.rows.length > 0 ? 0.8 : 0.3
    };
  }

  /**
   * Analyze efficiency metrics
   * @private
   */
  async _analyzeEfficiencyMetrics(client, truckId, currentLocationId) {
    // Calculate average trip completion time for this truck
    const efficiencyResult = await client.query(`
      SELECT 
        AVG(EXTRACT(EPOCH FROM (tl.updated_at - tl.created_at))/3600) as avg_trip_hours,
        COUNT(*) as completed_trips
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      WHERE a.truck_id = $1 
        AND tl.status = 'trip_completed'
        AND tl.created_at >= CURRENT_DATE - INTERVAL '7 days'
    `, [truckId]);

    const efficiency = efficiencyResult.rows[0];

    return {
      avgTripHours: parseFloat(efficiency.avg_trip_hours) || 0,
      completedTrips: parseInt(efficiency.completed_trips) || 0,
      efficiencyScore: this._calculateEfficiencyScore(efficiency)
    };
  }

  /**
   * Generate adaptation suggestions
   * @private
   */
  async _generateAdaptationSuggestions(data) {
    const suggestions = [];
    const { truck, currentLocationId, patternAnalysis, proximityAnalysis, efficiencyAnalysis } = data;

    // Pattern-based suggestion
    if (patternAnalysis.mostFrequentLocation && patternAnalysis.totalTrips > 5) {
      suggestions.push({
        strategy: ADAPTATION_STRATEGIES.PATTERN_BASED,
        confidence: CONFIDENCE_LEVELS.HIGH,
        suggestedLocationId: patternAnalysis.mostFrequentLocation.key,
        reason: `Truck frequently operates at this location (${patternAnalysis.mostFrequentLocation.count} times)`,
        score: 0.9
      });
    }

    // Proximity-based suggestion
    if (proximityAnalysis.nearbyLocations.length > 0) {
      const topNearby = proximityAnalysis.nearbyLocations[0];
      suggestions.push({
        strategy: ADAPTATION_STRATEGIES.PROXIMITY_BASED,
        confidence: CONFIDENCE_LEVELS.MEDIUM,
        suggestedLocationId: topNearby.id,
        reason: `Nearby location with high activity (${topNearby.assignment_count} assignments)`,
        score: 0.7
      });
    }

    // Efficiency-based suggestion
    if (efficiencyAnalysis.efficiencyScore > 0.7) {
      suggestions.push({
        strategy: ADAPTATION_STRATEGIES.EFFICIENCY_BASED,
        confidence: CONFIDENCE_LEVELS.HIGH,
        reason: `High efficiency truck (${efficiencyAnalysis.avgTripHours.toFixed(1)}h avg trip time)`,
        score: efficiencyAnalysis.efficiencyScore
      });
    }

    return suggestions.sort((a, b) => b.score - a.score);
  }

  /**
   * Helper methods
   * @private
   */
  _findMostFrequent(obj) {
    const entries = Object.entries(obj);
    if (entries.length === 0) return null;
    
    const sorted = entries.sort((a, b) => b[1] - a[1]);
    return { key: sorted[0][0], count: sorted[0][1] };
  }

  _calculateEfficiencyScore(efficiency) {
    const avgHours = parseFloat(efficiency.avg_trip_hours) || 0;
    const trips = parseInt(efficiency.completed_trips) || 0;
    
    if (trips === 0) return 0;
    
    // Score based on trip completion time (lower is better) and trip count
    const timeScore = Math.max(0, 1 - (avgHours / 8)); // 8 hours as baseline
    const volumeScore = Math.min(1, trips / 10); // 10 trips as good volume
    
    return (timeScore + volumeScore) / 2;
  }

  async _generateAdaptiveAssignmentCode(client, strategy) {
    const prefix = strategy === ADAPTATION_STRATEGIES.PATTERN_BASED ? 'ADP-P' : 
                   strategy === ADAPTATION_STRATEGIES.PROXIMITY_BASED ? 'ADP-X' :
                   strategy === ADAPTATION_STRATEGIES.EFFICIENCY_BASED ? 'ADP-E' : 'ADP-M';
    
    const timestamp = Date.now().toString().slice(-6);
    const random = Math.random().toString(36).substr(2, 4).toUpperCase();
    
    return `${prefix}-${timestamp}-${random}`;
  }

  _determineAssignmentStatus(confidence) {
    switch (confidence) {
      case CONFIDENCE_LEVELS.HIGH:
        return 'assigned'; // Auto-approve high confidence
      case CONFIDENCE_LEVELS.MEDIUM:
      case CONFIDENCE_LEVELS.LOW:
      default:
        return 'pending_approval'; // Require approval for medium/low confidence
    }
  }

  _buildAdaptiveUpdates(assignment, adaptations) {
    const fields = {};
    const metadata = JSON.parse(assignment.notes || '{}');

    // Apply adaptations
    if (adaptations.priority && adaptations.priority !== assignment.priority) {
      fields.priority = adaptations.priority;
    }

    if (adaptations.expectedLoads && adaptations.expectedLoads !== assignment.expected_loads_per_day) {
      fields.expected_loads_per_day = adaptations.expectedLoads;
    }

    // Update metadata
    metadata.last_adaptation = new Date().toISOString();
    metadata.adaptation_count = (metadata.adaptation_count || 0) + 1;
    
    if (Object.keys(fields).length > 0) {
      fields.notes = JSON.stringify(metadata);
      fields.updated_at = 'CURRENT_TIMESTAMP';
    }

    return { fields, metadata };
  }

  _buildUpdateQuery(updates) {
    const fields = Object.keys(updates.fields);
    const setClause = fields.map((field, index) => 
      field === 'updated_at' ? `${field} = CURRENT_TIMESTAMP` : `${field} = $${index + 2}`
    ).join(', ');
    
    const params = [updates.assignmentId, ...fields.filter(f => f !== 'updated_at').map(f => updates.fields[f])];
    
    return {
      sql: `UPDATE assignments SET ${setClause} WHERE id = $1 RETURNING *`,
      params
    };
  }
}

// Export singleton instance
const dynamicAssignmentAdapter = new DynamicAssignmentAdapter();

module.exports = {
  DynamicAssignmentAdapter,
  dynamicAssignmentAdapter,
  ADAPTATION_STRATEGIES,
  CONFIDENCE_LEVELS
};
