/**
 * Trip Logs Enhancement Service
 * 
 * Handles proper population of trip_logs records with notes and location sequence information
 * Addresses Requirement 3: Trip logs records include proper notes and location sequence information
 */

const ShiftTypeDetector = require('../utils/ShiftTypeDetector');

class TripLogsEnhancementService {
  /**
   * Generate contextual notes for trip log entries
   * @param {Object} params - Parameters for note generation
   * @returns {Object} - Structured notes object
   */
  static generateTripNotes(params) {
    const {
      action,
      timestamp,
      driver,
      truck,
      location,
      assignment,
      tripNumber,
      workflowType = 'standard',
      additionalContext = {}
    } = params;

    const notes = {
      // Core action information
      action: action,
      timestamp: timestamp.toISOString(),
      workflow_type: workflowType,
      trip_number: tripNumber,
      assignment_code: assignment?.assignment_code || 'N/A',

      // Driver information
      driver: driver ? {
        id: driver.driver_id || driver.id,
        name: driver.driver_name || driver.full_name,
        employee_id: driver.employee_id,
        shift_id: driver.shift_id,
        shift_type: driver.shift_type
      } : null,

      // Truck information
      truck: truck ? {
        id: truck.id,
        truck_number: truck.truck_number,
        license_plate: truck.license_plate
      } : null,

      // Location information
      location: location ? {
        id: location.id,
        name: location.name,
        location_code: location.location_code,
        type: location.type
      } : null,

      // Contextual message
      message: this.generateContextualMessage(action, location, driver, truck),

      // Additional context
      ...additionalContext,

      // Metadata
      generated_at: new Date().toISOString(),
      generated_by: 'TripLogsEnhancementService'
    };

    return notes;
  }

  /**
   * Generate human-readable contextual message
   */
  static generateContextualMessage(action, location, driver, truck) {
    const actionDisplay = this.getActionDisplay(action);
    const locationName = location?.name || 'unknown location';
    const driverName = driver?.driver_name || driver?.full_name || 'unknown driver';
    const truckNumber = truck?.truck_number || 'unknown truck';

    return `${actionDisplay} at ${locationName} by ${driverName} using truck ${truckNumber}`;
  }

  /**
   * Get display name for action
   */
  static getActionDisplay(action) {
    const actionMap = {
      'loading_start': 'Loading started',
      'loading_complete': 'Loading completed',
      'unloading_start': 'Unloading started',
      'unloading_complete': 'Unloading completed',
      'in_transit': 'In transit',
      'arrived': 'Arrived',
      'departed': 'Departed'
    };

    return actionMap[action] || action.replace('_', ' ');
  }

  /**
   * Calculate location sequence for multi-location workflows
   * @param {Object} params - Parameters for sequence calculation
   * @returns {number} - Location sequence number
   */
  static calculateLocationSequence(params) {
    const {
      assignment,
      location,
      action,
      existingTrips = [],
      workflowType = 'standard'
    } = params;

    try {
      // For standard A→B workflow
      if (workflowType === 'standard') {
        return this.calculateStandardSequence(assignment, location, action);
      }

      // For A→B→C extension workflow
      if (workflowType === 'extension') {
        return this.calculateExtensionSequence(assignment, location, action, existingTrips);
      }

      // For C→B→C cycle workflow
      if (workflowType === 'cycle') {
        return this.calculateCycleSequence(assignment, location, action, existingTrips);
      }

      // Default to 1 for unknown workflow types
      return 1;

    } catch (error) {
      console.error('Error calculating location sequence:', error);
      return 1; // Safe default
    }
  }

  /**
   * Calculate sequence for standard A→B workflow
   */
  static calculateStandardSequence(assignment, location, action) {
    // Standard workflow: Loading location = 1, Unloading location = 2
    if (location.type === 'loading' || location.id === assignment.loading_location_id) {
      return 1;
    } else if (location.type === 'unloading' || location.id === assignment.unloading_location_id) {
      return 2;
    }

    // Default to 1 if location type is unclear
    return 1;
  }

  /**
   * Calculate sequence for A→B→C extension workflow
   */
  static calculateExtensionSequence(assignment, location, action, existingTrips) {
    // A→B→C: Loading = 1, Intermediate = 2, Final = 3
    if (location.type === 'loading' || location.id === assignment.loading_location_id) {
      return 1;
    } else if (location.id === assignment.unloading_location_id) {
      return 2;
    } else {
      // Additional locations get sequence 3+
      const maxSequence = Math.max(...existingTrips.map(t => t.location_sequence || 0), 2);
      return maxSequence + 1;
    }
  }

  /**
   * Calculate sequence for C→B→C cycle workflow
   */
  static calculateCycleSequence(assignment, location, action, existingTrips) {
    // C→B→C: Track visits to each location
    const locationVisits = existingTrips.filter(t => t.actual_loading_location_id === location.id || t.actual_unloading_location_id === location.id);
    
    // Increment sequence based on number of visits to this location
    return locationVisits.length + 1;
  }

  /**
   * Update location sequence for existing trip
   * @param {Object} client - Database client
   * @param {Object} trip - Trip object
   * @param {Object} assignment - Assignment object
   * @param {Object} location - Location object
   * @param {string} action - Trip action
   */
  static async updateLocationSequence(client, trip, assignment, location, action) {
    try {
      // Get existing trips for this assignment to calculate sequence
      const existingTripsResult = await client.query(`
        SELECT id, location_sequence, actual_loading_location_id, actual_unloading_location_id
        FROM trip_logs
        WHERE assignment_id = $1 AND id != $2
        ORDER BY created_at ASC
      `, [assignment.id, trip.id]);

      const existingTrips = existingTripsResult.rows;

      // Determine workflow type based on assignment and existing trips
      const workflowType = this.determineWorkflowType(assignment, existingTrips);

      // Calculate location sequence
      const locationSequence = this.calculateLocationSequence({
        assignment,
        location,
        action,
        existingTrips,
        workflowType
      });

      // Update the trip with calculated sequence
      await client.query(`
        UPDATE trip_logs 
        SET location_sequence = $1, updated_at = NOW()
        WHERE id = $2
      `, [locationSequence, trip.id]);

      console.log(`Updated location sequence for trip ${trip.id}: ${locationSequence}`);

      return locationSequence;

    } catch (error) {
      console.error('Error updating location sequence:', error);
      throw error;
    }
  }

  /**
   * Determine workflow type based on assignment and existing trips
   */
  static determineWorkflowType(assignment, existingTrips) {
    // If no existing trips, assume standard workflow
    if (existingTrips.length === 0) {
      return 'standard';
    }

    // Check for unique locations visited
    const uniqueLocations = new Set();
    existingTrips.forEach(trip => {
      if (trip.actual_loading_location_id) uniqueLocations.add(trip.actual_loading_location_id);
      if (trip.actual_unloading_location_id) uniqueLocations.add(trip.actual_unloading_location_id);
    });

    // If more than 2 unique locations, it's likely an extension workflow
    if (uniqueLocations.size > 2) {
      return 'extension';
    }

    // Check for cycle pattern (same location visited multiple times)
    const locationCounts = {};
    existingTrips.forEach(trip => {
      const loadingId = trip.actual_loading_location_id;
      const unloadingId = trip.actual_unloading_location_id;
      
      if (loadingId) locationCounts[loadingId] = (locationCounts[loadingId] || 0) + 1;
      if (unloadingId) locationCounts[unloadingId] = (locationCounts[unloadingId] || 0) + 1;
    });

    // If any location is visited more than twice, it's likely a cycle
    if (Object.values(locationCounts).some(count => count > 2)) {
      return 'cycle';
    }

    // Default to standard workflow
    return 'standard';
  }

  /**
   * Enhance trip log entry with complete information
   * @param {Object} params - Parameters for enhancement
   * @returns {Object} - Enhanced trip log data
   */
  static enhanceTripLogEntry(params) {
    const {
      assignment,
      tripNumber,
      action,
      timestamp,
      location,
      driver,
      truck,
      existingTrips = [],
      additionalContext = {}
    } = params;

    // Generate comprehensive notes
    const notes = this.generateTripNotes({
      action,
      timestamp,
      driver,
      truck,
      location,
      assignment,
      tripNumber,
      workflowType: this.determineWorkflowType(assignment, existingTrips),
      additionalContext
    });

    // Calculate location sequence
    const locationSequence = this.calculateLocationSequence({
      assignment,
      location,
      action,
      existingTrips,
      workflowType: this.determineWorkflowType(assignment, existingTrips)
    });

    // Ensure shift type is populated correctly
    const shiftType = driver?.shift_type || ShiftTypeDetector.detectShiftType(timestamp);

    return {
      // Driver information (from captureActiveDriverInfo)
      performed_by_driver_id: driver?.driver_id || null,
      performed_by_driver_name: driver?.driver_name || driver?.full_name || null,
      performed_by_employee_id: driver?.employee_id || null,
      performed_by_shift_id: driver?.shift_id || null,
      performed_by_shift_type: shiftType,

      // Enhanced fields
      notes: JSON.stringify(notes),
      location_sequence: locationSequence,

      // Metadata
      enhanced_at: timestamp.toISOString(),
      enhancement_version: '1.0'
    };
  }

  /**
   * Validate trip log completeness
   * @param {Object} tripLog - Trip log entry to validate
   * @returns {Object} - Validation result
   */
  static validateTripLogCompleteness(tripLog) {
    const requiredFields = [
      'performed_by_driver_id',
      'performed_by_driver_name', 
      'performed_by_employee_id',
      'performed_by_shift_id',
      'performed_by_shift_type',
      'notes',
      'location_sequence'
    ];

    const missingFields = requiredFields.filter(field => 
      tripLog[field] === null || tripLog[field] === undefined || tripLog[field] === ''
    );

    const isComplete = missingFields.length === 0;

    return {
      is_complete: isComplete,
      missing_fields: missingFields,
      completeness_score: ((requiredFields.length - missingFields.length) / requiredFields.length) * 100,
      validation_timestamp: new Date().toISOString()
    };
  }

  /**
   * Get trip logs quality metrics
   * @param {Object} client - Database client
   * @param {string} dateFrom - Start date
   * @param {string} dateTo - End date
   * @returns {Object} - Quality metrics
   */
  static async getTripLogsQualityMetrics(client, dateFrom, dateTo) {
    try {
      const query = `
        SELECT 
          COUNT(*) as total_trips,
          COUNT(performed_by_driver_id) as trips_with_driver_id,
          COUNT(performed_by_driver_name) as trips_with_driver_name,
          COUNT(performed_by_employee_id) as trips_with_employee_id,
          COUNT(performed_by_shift_id) as trips_with_shift_id,
          COUNT(performed_by_shift_type) as trips_with_shift_type,
          COUNT(notes) as trips_with_notes,
          COUNT(location_sequence) as trips_with_location_sequence,
          COUNT(CASE 
            WHEN performed_by_driver_id IS NOT NULL 
            AND performed_by_driver_name IS NOT NULL
            AND performed_by_employee_id IS NOT NULL
            AND performed_by_shift_id IS NOT NULL
            AND performed_by_shift_type IS NOT NULL
            AND notes IS NOT NULL
            AND location_sequence IS NOT NULL
            THEN 1 END) as complete_trips,
          ROUND(
            (COUNT(CASE 
              WHEN performed_by_driver_id IS NOT NULL 
              AND performed_by_driver_name IS NOT NULL
              AND performed_by_employee_id IS NOT NULL
              AND performed_by_shift_id IS NOT NULL
              AND performed_by_shift_type IS NOT NULL
              AND notes IS NOT NULL
              AND location_sequence IS NOT NULL
              THEN 1 END)::decimal / COUNT(*)) * 100, 2
          ) as completeness_rate
        FROM trip_logs
        WHERE created_at BETWEEN $1 AND $2
      `;

      const result = await client.query(query, [dateFrom, dateTo]);
      return result.rows[0];
    } catch (error) {
      console.error('Error getting trip logs quality metrics:', error);
      throw error;
    }
  }
}

module.exports = TripLogsEnhancementService;