import React, { useState } from 'react';
import { usePWAInstall } from '../../hooks/usePWAInstall';

/**
 * PWA Installation Buttons Component
 * Displays installation options for Trip Scanner and Driver Connect
 */
const PWAInstallButtons = ({ className = '' }) => {
  const { isInstallable, isInstalled, isInstalling, installPWA, getInstallInstructions } = usePWAInstall();
  const [showInstructions, setShowInstructions] = useState(false);

  // Don't show if already installed
  if (isInstalled) {
    return (
      <div className={`text-center ${className}`}>
        <div className="bg-green-900/30 border border-green-300/20 rounded-xl p-4">
          <p className="text-green-100 text-sm">
            <i className="fas fa-check-circle mr-2"></i>
            <strong>PWA Installed!</strong> Access scanners from your home screen.
          </p>
        </div>
      </div>
    );
  }

  const handleInstallClick = async (scannerType) => {
    const success = await installP<PERSON>(`login-page-${scannerType}`);
    if (success) {
      console.log(`[PWA Install] Successfully installed from ${scannerType} button`);
    }
  };

  const handleShowInstructions = () => {
    setShowInstructions(!showInstructions);
  };

  const instructions = getInstallInstructions();

  return (
    <div className={`space-y-4 ${className}`}>
      {/* PWA Installation Header */}
      <div className="text-center">
        <h3 className="text-lg font-semibold text-white mb-2">
          📱 Install Mobile Scanners
        </h3>
        <p className="text-blue-200 text-sm mb-4">
          Add QR scanners to your home screen for quick access
        </p>
      </div>

      {/* Installation Buttons */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
        {/* Trip Scanner Installation */}
        <div className="bg-white/10 border border-white/20 rounded-xl p-4 backdrop-blur-md hover:bg-white/15 transition-all duration-300">
          <div className="text-center">
            <div className="text-2xl mb-2">🚛</div>
            <h4 className="text-white font-medium mb-1">Trip Scanner</h4>
            <p className="text-blue-200 text-xs mb-3">Supervisory QR Scanner</p>
            
            {isInstallable ? (
              <button
                onClick={() => handleInstallClick('trip-scanner')}
                disabled={isInstalling}
                className="w-full bg-blue-500/30 hover:bg-blue-500/40 text-white text-sm py-2 px-3 rounded-lg transition-all duration-300 border border-blue-400/30 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isInstalling ? (
                  <>
                    <div className="inline-block w-3 h-3 border border-white border-t-transparent rounded-full animate-spin mr-1"></div>
                    Installing...
                  </>
                ) : (
                  <>
                    <i className="fas fa-download mr-1"></i>
                    Install App
                  </>
                )}
              </button>
            ) : (
              <a
                href="/trip-scanner"
                target="_blank"
                rel="noopener noreferrer"
                className="block w-full bg-green-500/30 hover:bg-green-500/40 text-white text-sm py-2 px-3 rounded-lg transition-all duration-300 border border-green-400/30"
              >
                <i className="fas fa-external-link-alt mr-1"></i>
                Open Scanner
              </a>
            )}
          </div>
        </div>

        {/* Driver Connect Installation */}
        <div className="bg-white/10 border border-white/20 rounded-xl p-4 backdrop-blur-md hover:bg-white/15 transition-all duration-300">
          <div className="text-center">
            <div className="text-2xl mb-2">👤</div>
            <h4 className="text-white font-medium mb-1">Driver Connect</h4>
            <p className="text-blue-200 text-xs mb-3">Driver Authentication</p>
            
            {isInstallable ? (
              <button
                onClick={() => handleInstallClick('driver-connect')}
                disabled={isInstalling}
                className="w-full bg-blue-500/30 hover:bg-blue-500/40 text-white text-sm py-2 px-3 rounded-lg transition-all duration-300 border border-blue-400/30 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isInstalling ? (
                  <>
                    <div className="inline-block w-3 h-3 border border-white border-t-transparent rounded-full animate-spin mr-1"></div>
                    Installing...
                  </>
                ) : (
                  <>
                    <i className="fas fa-download mr-1"></i>
                    Install App
                  </>
                )}
              </button>
            ) : (
              <a
                href="/driver-connect"
                target="_blank"
                rel="noopener noreferrer"
                className="block w-full bg-green-500/30 hover:bg-green-500/40 text-white text-sm py-2 px-3 rounded-lg transition-all duration-300 border border-green-400/30"
              >
                <i className="fas fa-external-link-alt mr-1"></i>
                Open Scanner
              </a>
            )}
          </div>
        </div>
      </div>

      {/* Manual Installation Instructions */}
      <div className="text-center">
        <button
          onClick={handleShowInstructions}
          className="text-blue-300 hover:text-blue-200 text-sm underline transition-colors duration-300"
        >
          {showInstructions ? 'Hide' : 'Show'} manual installation instructions
        </button>
      </div>

      {showInstructions && (
        <div className="bg-blue-900/30 border border-blue-300/20 rounded-xl p-4 animate-card-appear">
          <h4 className="text-white font-medium mb-2">
            <i className="fas fa-info-circle mr-2"></i>
            Manual Installation - {instructions.platform}
          </h4>
          <ol className="list-decimal list-inside text-blue-100 text-sm space-y-1">
            {instructions.steps.map((step, index) => (
              <li key={index}>{step}</li>
            ))}
          </ol>
          <div className="mt-3 pt-3 border-t border-blue-400/20">
            <p className="text-blue-200 text-xs">
              <i className="fas fa-lightbulb mr-1"></i>
              <strong>Tip:</strong> Once installed, you can access scanners directly from your home screen without opening the browser.
            </p>
          </div>
        </div>
      )}

      {/* PWA Benefits */}
      <div className="bg-purple-900/30 border border-purple-300/20 rounded-xl p-4">
        <h4 className="text-white font-medium mb-2">
          <i className="fas fa-star mr-2"></i>
          PWA Benefits
        </h4>
        <ul className="text-purple-100 text-sm space-y-1">
          <li><i className="fas fa-bolt mr-2 text-yellow-400"></i>Faster loading and offline support</li>
          <li><i className="fas fa-camera mr-2 text-green-400"></i>Direct camera access for QR scanning</li>
          <li><i className="fas fa-home mr-2 text-blue-400"></i>Home screen shortcuts for quick access</li>
          <li><i className="fas fa-sync mr-2 text-purple-400"></i>Background sync when connection returns</li>
        </ul>
      </div>
    </div>
  );
};

export default PWAInstallButtons;
