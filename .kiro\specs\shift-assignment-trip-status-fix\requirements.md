# Requirements Document

## Introduction

This feature addresses status synchronization and data consistency issues in the trip logging system after the recent driver QR code implementation. Investigation shows that the core driver chain (QR check-in → active shift → driver capture → trip_logs population) is working correctly. However, there are edge cases in shift type detection logic, date filtering issues in the shift management UI, and potential status synchronization issues between shift management, assignment management, and trip monitoring systems that need to be resolved to ensure complete data integrity and operational accuracy across overnight and multi-day shift scenarios.

## Status: COMPLETED ✅

**Resolution Summary:** Fixed critical date filtering issue in shift management UI where active shifts were not displaying due to timestamp/date format mismatch in PostgreSQL queries. The backend API was correctly querying the `driver_shifts` table but date filtering logic was failing to match timestamp fields with date parameters.

## Requirements

### Requirement 1

**User Story:** As a system administrator, I want the shift type detection logic to correctly classify day and night shifts according to business rules, so that driver shifts and trip logs have accurate shift type attribution.

#### Acceptance Criteria

1. WHEN a driver checks in at 06:00 AM to 06:00 PM THEN the system SHALL classify the shift as 'day' type
2. WHEN a driver checks in at 06:01 PM to 05:59 AM THEN the system SHALL classify the shift as 'night' type  
3. WHEN a driver checks in at July 28 08:00 AM THEN the system SHALL create a shift with shift_type = 'day'
4. WHEN a driver checks in at July 28 22:00 PM THEN the system SHALL create a shift with shift_type = 'night'
5. WHEN trip logs are created THEN the system SHALL populate performed_by_shift_type with the correct shift type from the active shift

### Requirement 2

**User Story:** As a fleet manager, I want the captureActiveDriverInfo function to correctly identify active drivers across overnight and multi-day shift scenarios, so that driver information is accurately captured in trip logs regardless of shift timing patterns.

#### Acceptance Criteria

1. WHEN a driver checks in on July 28 08:00 AM and a trip is scanned on July 28 22:00 PM THEN the system SHALL find the active driver
2. WHEN a driver checks in on July 28 22:00 PM and a trip is scanned on July 29 02:00 AM THEN the system SHALL find the active driver  
3. WHEN a driver checks in on July 28 08:00 AM and a trip is scanned on July 29 10:00 AM THEN the system SHALL find the active driver if still checked in
4. WHEN a driver has checked out THEN the system SHALL NOT find them as active for subsequent trip scans
5. WHEN driver capture fails THEN the system SHALL log detailed debugging information and attempt fallback methods

### Requirement 3

**User Story:** As a dispatcher, I want trip_logs records to include proper notes and location sequence information, so that I can track trip progression and understand the context of each scanning action.

#### Acceptance Criteria

1. WHEN a trip log entry is created THEN the system SHALL populate the notes field with contextual information about the scanning action
2. WHEN a trip log entry is created THEN the system SHALL populate the location_sequence field with the appropriate sequence number for multi-location workflows
3. WHEN a trip progresses through phases THEN the system SHALL update location_sequence to reflect the current position in the workflow
4. WHEN trip notes are generated THEN they SHALL include information about the driver, truck, location, and action performed
5. WHEN location sequence is calculated THEN it SHALL accurately reflect A→B, A→B→C, and C→B→C workflow patterns

### Requirement 4

**User Story:** As a system developer, I want comprehensive logging and debugging capabilities for driver capture operations, so that I can quickly identify and resolve issues when driver information is not being captured correctly.

#### Acceptance Criteria

1. WHEN driver capture operations execute THEN the system SHALL log detailed information about the query parameters and results
2. WHEN driver capture fails THEN the system SHALL log the specific reason for failure with sufficient context for debugging
3. WHEN fallback driver capture methods are used THEN the system SHALL log which method succeeded and why the primary method failed
4. WHEN shift queries are executed THEN the system SHALL log the shift matching criteria and results for troubleshooting
5. WHEN driver information is successfully captured THEN the system SHALL log confirmation with driver details and shift information

### Requirement 5

**User Story:** As a quality assurance analyst, I want automated validation and monitoring of driver capture accuracy, so that I can ensure the system consistently captures driver information correctly across all trip operations.

#### Acceptance Criteria

1. WHEN trips are created THEN the system SHALL validate that driver information was successfully captured and log any failures
2. WHEN driver capture validation fails THEN the system SHALL attempt alternative capture methods before proceeding
3. WHEN trip_logs entries are created without driver information THEN the system SHALL flag these as data quality issues
4. WHEN monitoring driver capture rates THEN the system SHALL provide metrics on success/failure rates and common failure patterns
5. WHEN data quality issues are detected THEN the system SHALL provide actionable recommendations for resolution

### Requirement 6

**User Story:** As an operations supervisor, I want status synchronization between shift management, assignment management, and trip monitoring to be accurate and real-time, so that I can make informed decisions based on current operational state.

#### Acceptance Criteria

1. WHEN a driver checks in via QR code THEN the assignment management system SHALL reflect the driver's availability status within 5 seconds
2. WHEN a trip starts THEN the shift management system SHALL show the driver as actively engaged in trip operations
3. WHEN shift status changes THEN the trip monitoring system SHALL update to reflect the current driver assignment
4. WHEN status conflicts are detected THEN the system SHALL automatically resolve them using predefined business rules
5. WHEN status synchronization fails THEN the system SHALL alert supervisors and provide manual resolution options

### Requirement 7 ✅ COMPLETED

**User Story:** As a fleet manager, I want the shift management UI to display active shifts correctly when filtering by date range, so that I can view and manage current driver assignments without encountering "No shifts found" errors when shifts actually exist.

#### Acceptance Criteria ✅ ALL COMPLETED

1. ✅ WHEN I access the Enhanced Shift Management page THEN the system SHALL display active shifts that exist in the database
2. ✅ WHEN I filter shifts by date range (e.g., July 28-August 3, 2025) THEN the system SHALL show shifts that fall within that date range
3. ✅ WHEN the backend queries the driver_shifts table THEN the date filtering logic SHALL correctly handle timestamp-to-date conversion
4. ✅ WHEN shift_date fields contain timestamps (e.g., "2025-07-28T16:00:00.000Z") THEN the system SHALL extract the date portion for comparison
5. ✅ WHEN no date filters are applied THEN the system SHALL display all available shifts
6. ✅ WHEN API calls succeed but return empty results due to date filtering issues THEN the system SHALL be fixed to return the correct data

**Resolution Details:**
- **Root Cause:** PostgreSQL date filtering was using `::date` casting which failed to properly convert timestamp fields to dates for comparison
- **Solution:** Changed to `DATE()` function for reliable timestamp-to-date conversion in SQL queries
- **Files Modified:** 
  - `server/routes/shifts.js` - Fixed date filtering logic in GET /api/shifts endpoint
  - `client/src/pages/shifts/SimplifiedShiftManagement.js` - Enhanced debugging and error handling
- **Testing:** Verified that 3 active shifts now display correctly in the UI with proper date filtering