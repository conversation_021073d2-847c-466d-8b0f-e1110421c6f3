import React, { useState } from 'react';
import TripNumberManager from './TripNumberManager';
import AnalyticsAPITest from '../../tests/AnalyticsAPITest';
import DatabaseCleanupPanel from './components/DatabaseCleanupPanel';
import CacheManagementPanel from './components/CacheManagementPanel';
import AppearanceSettings from './components/AppearanceSettings';
import SystemHealthMonitor from './components/SystemHealthMonitor';
import UserRolesManagement from './components/UserRolesManagement';
import TripLogsDataQuality from './components/TripLogsDataQuality.jsx';

// Move settings configuration outside component to prevent recreation on every render
const settingsMenus = [
    {
      id: 'user-roles-management',
      title: 'User Roles Management',
      icon: '👥',
      description: 'Manage user roles and configure page access permissions for different user types',
      component: UserRolesManagement
    },
    {
      id: 'system-health-monitor',
      title: 'System Health Monitor',
      icon: '🏥',
      description: 'Comprehensive monitoring and automated fixing for Shift Management, Assignment Management, and Trip Monitoring modules',
      component: SystemHealthMonitor
    },
    {
      id: 'trip-logs-data-quality',
      title: 'Trip Logs Data Quality',
      icon: '📊',
      description: 'Validate trip_logs field population completeness and monitor data quality metrics with automated alerts',
      component: TripLogsDataQuality
    },
    {
      id: 'appearance',
      title: 'Appearance Settings',
      icon: '🎨',
      description: 'Customize the look and feel of your application including logo, fonts, and visual themes',
      component: AppearanceSettings
    },
    {
      id: 'trip-numbers',
      title: 'Trip Number Manager',
      icon: '🔢',
      description: 'Manage and fix trip number duplications',
      component: TripNumberManager
    },
    {
      id: 'analytics-test',
      title: 'Analytics API Test Suite',
      icon: '🧪',
      description: 'Test analytics endpoints and API functionality',
      component: AnalyticsAPITest
    },
    {
      id: 'database-cleanup',
      title: 'Database Cleanup Tools',
      icon: '🗑️',
      description: 'Clean specific database tables (trip_logs, scan_logs, assignments, migration_log)',
      component: DatabaseCleanupPanel
    },
    {
      id: 'cache-management',
      title: 'Cache Management',
      icon: '🧹',
      description: 'Clear browser storage, memory caches, and application state to fix display issues',
      component: CacheManagementPanel
    }
  ];

const Settings = () => {
  const [activeSubmenu, setActiveSubmenu] = useState(null);

  const renderMainSettings = () => (
    <div className="space-y-6">
      <div className="md:flex md:items-center md:justify-between">
        <div className="flex-1 min-w-0">
          <h1 className="text-2xl font-bold leading-7 text-secondary-900 sm:text-3xl sm:truncate">
            ⚙️ Settings
          </h1>
          <p className="mt-1 text-sm text-secondary-500">System configuration and administrative tools.</p>
        </div>
      </div>

      {/* Settings Menu Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {settingsMenus.map((menu) => (
          <div
            key={menu.id}
            onClick={() => setActiveSubmenu(menu)}
            className="bg-white rounded-lg shadow border border-secondary-200 p-6 hover:shadow-lg transition-shadow cursor-pointer"
          >
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <span className="text-2xl">{menu.icon}</span>
                </div>
              </div>
              <div className="ml-4 flex-1">
                <h3 className="text-lg font-medium text-secondary-900 mb-2">
                  {menu.title}
                </h3>
                <p className="text-sm text-secondary-600">
                  {menu.description}
                </p>
                <div className="mt-3">
                  <span className="inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-500">
                    Open →
                  </span>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Future Settings Placeholder */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <div className="flex items-center">
          <span className="text-2xl mr-3">🔧</span>
          <div>
            <h3 className="text-lg font-medium text-blue-800">Additional Settings</h3>
            <p className="mt-1 text-sm text-blue-700">
              More system configuration options will be added in future updates.
            </p>
          </div>
        </div>
      </div>
    </div>
  );

  const renderSubmenu = () => {
    const Component = activeSubmenu.component;

    return (
      <div className="space-y-6">
        {/* Breadcrumb Navigation */}
        <div className="flex items-center space-x-2 text-sm">
          <button
            onClick={() => setActiveSubmenu(null)}
            className="text-blue-600 hover:text-blue-500 font-medium"
          >
            ⚙️ Settings
          </button>
          <span className="text-secondary-400">→</span>
          <span className="text-secondary-900 font-medium">
            {activeSubmenu.icon} {activeSubmenu.title}
          </span>
        </div>

        {/* Back Button */}
        <div className="flex items-center justify-between">
          <button
            onClick={() => setActiveSubmenu(null)}
            className="inline-flex items-center px-4 py-2 border border-secondary-300 rounded-md shadow-sm text-sm font-medium text-secondary-700 bg-white hover:bg-secondary-50"
          >
            ← Back to Settings
          </button>
        </div>

        {/* Submenu Component */}
        <div className="bg-white rounded-lg shadow border border-secondary-200">
          <Component />
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-secondary-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {activeSubmenu ? renderSubmenu() : renderMainSettings()}
      </div>
    </div>
  );
};

export default Settings;