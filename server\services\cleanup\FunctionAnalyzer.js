const fs = require('fs').promises;
const path = require('path');
const { execSync } = require('child_process');

class FunctionAnalyzer {
    constructor() {
        this.criticalPatterns = [
            /\.get\s*\(/, /\.post\s*\(/, /\.put\s*\(/, /\.delete\s*\(/, /\.patch\s*\(/,
            /router\./, /app\./, /middleware/i, /authenticate/i, /authorize/i, /validate/i,
            /pool\.query/, /db\./, /connection\./, /transaction/i,
            /express\(/, /listen\(/, /server\./, /module\.exports/, /exports\./,
            /server\.js/, /app\.js/, /index\.js/
        ];
        
        this.criticalNames = [
            'main', 'init', 'setup', 'start', 'listen', 'connect',
            'authenticate', 'authorize', 'validate', 'middleware',
            'route', 'handler', 'controller', 'service'
        ];
    }

    async analyzeUnusedFunctions(directories = ['server', 'scripts']) {
        const allFiles = await this._getAllJavaScriptFiles(directories);
        
        const analysis = {
            totalFiles: allFiles.length,
            analyzedFunctions: [],
            unusedFunctions: [],
            criticalFunctions: [],
            safeToRemove: [],
            warnings: []
        };

        for (const filePath of allFiles) {
            const fileAnalysis = await this._analyzeFile(filePath);
            analysis.analyzedFunctions.push(...fileAnalysis.functions);
            analysis.criticalFunctions.push(...fileAnalysis.critical);
            analysis.unusedFunctions.push(...fileAnalysis.unused);
        }

        const usageMap = await this._buildUsageMap(allFiles);
        analysis.safeToRemove = this._identifySafeToRemove(analysis.unusedFunctions, usageMap);

        return analysis;
    }

    async _getAllJavaScriptFiles(directories) {
        const allFiles = [];
        
        for (const directory of directories) {
            const files = await this._getJavaScriptFiles(directory);
            allFiles.push(...files);
        }
        
        return allFiles;
    }

    async _getJavaScriptFiles(directory) {
        const files = [];
        const basePath = path.join(__dirname, '../..', directory);

        try {
            await fs.access(basePath);
            await this._walkDirectory(basePath, files, '.js');
            return files.map(file => path.relative(path.join(__dirname, '../..'), file));
        } catch (error) {
            console.warn(`Warning: Could not scan directory ${directory}: ${error.message}`);
            return [];
        }
    }

    async _walkDirectory(dir, files, extension) {
        try {
            const entries = await fs.readdir(dir, { withFileTypes: true });

            for (const entry of entries) {
                const fullPath = path.join(dir, entry.name);

                if (entry.isDirectory() && !['node_modules', '.git'].includes(entry.name)) {
                    await this._walkDirectory(fullPath, files, extension);
                } else if (entry.isFile() && entry.name.endsWith(extension)) {
                    files.push(fullPath);
                }
            }
        } catch (error) {
            console.warn(`Warning: Could not read directory ${dir}: ${error.message}`);
        }
    }

    async _analyzeFile(filePath) {
        try {
            const content = await fs.readFile(filePath, 'utf8');
            const functions = this._extractFunctions(content);

            const analysis = { file: filePath, functions: [], critical: [], unused: [] };

            for (const func of functions) {
                const functionInfo = {
                    name: func.name,
                    file: filePath,
                    line: func.line,
                    type: func.type,
                    isCritical: this._isCriticalFunction(content, func)
                };

                analysis.functions.push(functionInfo);

                if (functionInfo.isCritical) {
                    analysis.critical.push(functionInfo);
                } else {
                    const isUsed = await this._isFunctionUsed(func.name, filePath);
                    if (!isUsed) {
                        analysis.unused.push(functionInfo);
                    }
                }
            }

            return analysis;
        } catch (error) {
            throw new Error(`Failed to analyze file ${filePath}: ${error.message}`);
        }
    }

    _extractFunctions(content) {
        const functions = [];
        
        // Function declarations
        const functionMatches = [...content.matchAll(/function\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*\(/g)];
        functionMatches.forEach(match => {
            const lineNumber = content.substring(0, match.index).split('\n').length;
            functions.push({ name: match[1], type: 'function', line: lineNumber });
        });

        // Arrow functions
        const arrowMatches = [...content.matchAll(/(?:const|let|var)\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*=\s*(?:\([^)]*\)|[a-zA-Z_$][a-zA-Z0-9_$]*)\s*=>/g)];
        arrowMatches.forEach(match => {
            const lineNumber = content.substring(0, match.index).split('\n').length;
            functions.push({ name: match[1], type: 'arrow', line: lineNumber });
        });

        // Method definitions
        const lines = content.split('\n');
        lines.forEach((line, index) => {
            const trimmed = line.trim();
            const methodMatch = trimmed.match(/^([a-zA-Z_$][a-zA-Z0-9_$]*)\s*\([^)]*\)\s*\{/);
            
            if (methodMatch && 
                !trimmed.startsWith('//') && 
                !['if', 'for', 'while', 'switch'].some(keyword => trimmed.includes(keyword))) {
                functions.push({ name: methodMatch[1], type: 'method', line: index + 1 });
            }
        });

        return functions;
    }

    _isCriticalFunction(content, func) {
        // Check patterns in content
        if (this.criticalPatterns.some(pattern => pattern.test(content))) {
            return true;
        }

        // Check function name
        return this.criticalNames.some(name =>
            func.name.toLowerCase().includes(name.toLowerCase())
        );
    }

    async _isFunctionUsed(functionName, filePath) {
        try {
            const searchPattern = `\\b${functionName}\\b`;
            const searchCommand = process.platform === 'win32'
                ? `findstr /r /s "${searchPattern}" server\\*.js scripts\\*.js`
                : `grep -r "${searchPattern}" server/ scripts/ || true`;

            const result = execSync(searchCommand, {
                cwd: path.join(__dirname, '../..'),
                encoding: 'utf8',
                stdio: 'pipe'
            });

            const matches = result.split('\n').filter(line => line.trim());
            const usageFiles = new Set(matches.map(match => match.split(':')[0]));

            return usageFiles.size > 1 || matches.some(match => match.includes(`${functionName}(`));
        } catch (error) {
            return true; // Safer to assume it's used
        }
    }

    async _buildUsageMap(files) {
        const usageMap = new Map();

        for (const filePath of files) {
            try {
                const content = await fs.readFile(filePath, 'utf8');
                usageMap.set(filePath, {
                    imports: this._extractImports(content),
                    calls: this._extractFunctionCalls(content)
                });
            } catch (error) {
                console.warn(`Warning: Could not build usage map for ${filePath}: ${error.message}`);
            }
        }

        return usageMap;
    }

    _extractImports(content) {
        const imports = [];
        const patterns = [
            /require\s*\(\s*['"`]([^'"`]+)['"`]\s*\)/g,
            /import\s+.*\s+from\s+['"`]([^'"`]+)['"`]/g
        ];

        patterns.forEach(pattern => {
            const matches = [...content.matchAll(pattern)];
            matches.forEach(match => imports.push(match[1]));
        });

        return imports;
    }

    _extractFunctionCalls(content) {
        const calls = [];
        const matches = [...content.matchAll(/([a-zA-Z_$][a-zA-Z0-9_$]*)\s*\(/g)];
        matches.forEach(match => calls.push(match[1]));
        return calls;
    }

    _identifySafeToRemove(unusedFunctions, usageMap) {
        return unusedFunctions.filter(func => {
            for (const [, usage] of usageMap) {
                if (usage.calls.includes(func.name)) {
                    return false;
                }
            }
            return true;
        });
    }
}

module.exports = FunctionAnalyzer;