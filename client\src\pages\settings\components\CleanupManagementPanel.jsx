import React, { useState } from 'react';
import { getApiBaseUrl } from '../../../utils/network-utils';

/**
 * CleanupManagementPanel Component
 * 
 * Provides a user interface for analyzing and cleaning up unused JavaScript functions
 * and test files in the codebase. Includes analysis, execution, and rollback capabilities.
 */
const CleanupManagementPanel = () => {
  // State for analysis results and options
  const [analysisResults, setAnalysisResults] = useState(null);
  const [cleanupResults, setCleanupResults] = useState(null);
  const [verificationResults, setVerificationResults] = useState(null);
  const [testFilesResults, setTestFilesResults] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState('analysis');

  // Test files cleanup options
  const [testFilesOptions, setTestFilesOptions] = useState({
    includePatterns: ['*test*', 'test-*', 'test*'],
    excludePatterns: ['node_modules/**', 'build/**'],
    dryRun: true,
    createBackup: true,
    maxFilesToDelete: 50
  });

  // Analysis options
  const [analysisOptions, setAnalysisOptions] = useState({
    includeServer: true,
    includeScripts: true,
    preserveCritical: true,
    maxFilesToAnalyze: 100
  });

  // Cleanup options
  const [cleanupOptions, setCleanupOptions] = useState({
    dryRun: true,
    createBackup: true,
    maxFilesToModify: 50
  });

  // Handle analysis options change
  const handleAnalysisOptionChange = (e) => {
    const { name, value, type, checked } = e.target;
    setAnalysisOptions({
      ...analysisOptions,
      [name]: type === 'checkbox' ? checked : (type === 'number' ? parseInt(value) : value)
    });
  };

  // Handle cleanup options change
  const handleCleanupOptionChange = (e) => {
    const { name, value, type, checked } = e.target;
    setCleanupOptions({
      ...cleanupOptions,
      [name]: type === 'checkbox' ? checked : (type === 'number' ? parseInt(value) : value)
    });
  };

  // Handle test files options change
  const handleTestFilesOptionChange = (e) => {
    const { name, value, type, checked } = e.target;
    setTestFilesOptions({
      ...testFilesOptions,
      [name]: type === 'checkbox' ? checked : (type === 'number' ? parseInt(value) : value)
    });
  };

  // Handle pattern change for test files
  const handlePatternChange = (e, index, patternType) => {
    const newPatterns = [...testFilesOptions[patternType]];
    newPatterns[index] = e.target.value;
    setTestFilesOptions({
      ...testFilesOptions,
      [patternType]: newPatterns
    });
  };

  // Add new pattern for test files
  const addPattern = (patternType) => {
    setTestFilesOptions({
      ...testFilesOptions,
      [patternType]: [...testFilesOptions[patternType], '']
    });
  };

  // Remove pattern for test files
  const removePattern = (index, patternType) => {
    const newPatterns = [...testFilesOptions[patternType]];
    newPatterns.splice(index, 1);
    setTestFilesOptions({
      ...testFilesOptions,
      [patternType]: newPatterns
    });
  };

  // Run code analysis
  const runAnalysis = async () => {
    try {
      setLoading(true);
      setError(null);

      const apiUrl = getApiBaseUrl();
      const response = await fetch(`${apiUrl}/system-health/cleanup/analyze`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('hauling_token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(analysisOptions)
      });

      if (!response.ok) {
        throw new Error(`Failed to analyze code: ${response.statusText}`);
      }

      const result = await response.json();
      setAnalysisResults(result);
      setActiveTab('results');

    } catch (err) {
      console.error('Error analyzing code:', err);
      setError(`Failed to analyze code: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Execute cleanup
  const executeCleanup = async () => {
    if (!analysisResults) {
      setError('Analysis results are required for cleanup execution');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const apiUrl = getApiBaseUrl();
      const response = await fetch(`${apiUrl}/system-health/cleanup/execute`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('hauling_token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          analysisResults,
          ...cleanupOptions
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to execute cleanup: ${response.statusText}`);
      }

      const result = await response.json();
      setCleanupResults(result);
      setActiveTab('cleanup');

    } catch (err) {
      console.error('Error executing cleanup:', err);
      setError(`Failed to execute cleanup: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Verify system integrity
  const verifySystem = async () => {
    try {
      setLoading(true);
      setError(null);

      const apiUrl = getApiBaseUrl();
      const response = await fetch(`${apiUrl}/system-health/cleanup/verify`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('hauling_token')}`
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to verify system: ${response.statusText}`);
      }

      const result = await response.json();
      setVerificationResults(result);

    } catch (err) {
      console.error('Error verifying system:', err);
      setError(`Failed to verify system: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Rollback changes
  const rollbackChanges = async () => {
    if (!cleanupResults?.backupPath) {
      setError('Backup path is required for rollback');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const apiUrl = getApiBaseUrl();
      const response = await fetch(`${apiUrl}/system-health/cleanup/rollback`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('hauling_token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          backupPath: cleanupResults.backupPath
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to rollback changes: ${response.statusText}`);
      }

      await response.json();
      alert('Changes rolled back successfully');

      // Reset results
      setCleanupResults(null);
      setAnalysisResults(null);
      setActiveTab('analysis');

    } catch (err) {
      console.error('Error rolling back changes:', err);
      setError(`Failed to rollback changes: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Analyze test files
  const analyzeTestFiles = async () => {
    try {
      setLoading(true);
      setError(null);

      const apiUrl = getApiBaseUrl();
      const response = await fetch(`${apiUrl}/system-health/cleanup/analyze-test-files`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('hauling_token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          includePatterns: testFilesOptions.includePatterns,
          excludePatterns: testFilesOptions.excludePatterns
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to analyze test files: ${response.statusText}`);
      }

      const result = await response.json();
      setTestFilesResults(result);

    } catch (err) {
      console.error('Error analyzing test files:', err);
      setError(`Failed to analyze test files: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Clean up test files
  const cleanupTestFiles = async () => {
    if (!testFilesResults) {
      setError('Test file analysis results are required for cleanup');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const apiUrl = getApiBaseUrl();
      const response = await fetch(`${apiUrl}/system-health/cleanup/remove-test-files`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('hauling_token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          testFiles: testFilesResults.testFiles,
          dryRun: testFilesOptions.dryRun,
          createBackup: testFilesOptions.createBackup,
          maxFilesToDelete: testFilesOptions.maxFilesToDelete
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to clean up test files: ${response.statusText}`);
      }

      const result = await response.json();
      setTestFilesResults({
        ...testFilesResults,
        cleanupResults: result
      });

    } catch (err) {
      console.error('Error cleaning up test files:', err);
      setError(`Failed to clean up test files: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Rollback test file changes
  const rollbackTestFileChanges = async () => {
    if (!testFilesResults?.cleanupResults?.backupPath) {
      setError('Backup path is required for rollback');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const apiUrl = getApiBaseUrl();
      const response = await fetch(`${apiUrl}/system-health/cleanup/rollback`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('hauling_token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          backupPath: testFilesResults.cleanupResults.backupPath
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to rollback changes: ${response.statusText}`);
      }

      await response.json();
      alert('Test file changes rolled back successfully');

      // Reset results
      setTestFilesResults(null);

    } catch (err) {
      console.error('Error rolling back test file changes:', err);
      setError(`Failed to rollback test file changes: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };


  return (
    <div className="mt-6 bg-white rounded-lg shadow border border-secondary-200">
      <div className="px-6 py-4 border-b border-secondary-200">
        <h3 className="text-lg font-medium text-secondary-900">
          🧹 Code Cleanup Management
        </h3>
        <p className="text-sm text-secondary-500 mt-1">
          Analyze and clean up unused JavaScript functions and test files to optimize codebase
        </p>
      </div>

      <div className="p-6">
        {error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-600">❌ {error}</p>
          </div>
        )}

        {/* Tab Navigation */}
        <div className="border-b border-secondary-200 mb-4">
          <nav className="flex -mb-px">
            <button
              onClick={() => setActiveTab('analysis')}
              className={`py-2 px-4 text-sm font-medium ${activeTab === 'analysis'
                ? 'border-b-2 border-blue-500 text-blue-600'
                : 'text-secondary-500 hover:text-secondary-700'
                }`}
            >
              Analysis
            </button>
            <button
              onClick={() => setActiveTab('results')}
              disabled={!analysisResults}
              className={`py-2 px-4 text-sm font-medium ${activeTab === 'results'
                ? 'border-b-2 border-blue-500 text-blue-600'
                : 'text-secondary-500 hover:text-secondary-700'
                } ${!analysisResults ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              Results
            </button>
            <button
              onClick={() => setActiveTab('cleanup')}
              disabled={!cleanupResults}
              className={`py-2 px-4 text-sm font-medium ${activeTab === 'cleanup'
                ? 'border-b-2 border-blue-500 text-blue-600'
                : 'text-secondary-500 hover:text-secondary-700'
                } ${!cleanupResults ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              Cleanup
            </button>
            <button
              onClick={() => setActiveTab('testFiles')}
              className={`py-2 px-4 text-sm font-medium ${activeTab === 'testFiles'
                ? 'border-b-2 border-blue-500 text-blue-600'
                : 'text-secondary-500 hover:text-secondary-700'
                }`}
            >
              Test Files
            </button>
            <button
              onClick={() => setActiveTab('verification')}
              className={`py-2 px-4 text-sm font-medium ${activeTab === 'verification'
                ? 'border-b-2 border-blue-500 text-blue-600'
                : 'text-secondary-500 hover:text-secondary-700'
                }`}
            >
              Verification
            </button>
          </nav>
        </div>

        {/* Analysis Tab */}
        {activeTab === 'analysis' && (
          <div>
            <div className="mb-4">
              <h4 className="text-md font-medium text-secondary-900 mb-2">
                Analysis Options
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="includeServer"
                      name="includeServer"
                      checked={analysisOptions.includeServer}
                      onChange={handleAnalysisOptionChange}
                      className="rounded border-secondary-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                    />
                    <label htmlFor="includeServer" className="ml-2 text-sm text-secondary-700">
                      Include server files
                    </label>
                  </div>
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="includeScripts"
                      name="includeScripts"
                      checked={analysisOptions.includeScripts}
                      onChange={handleAnalysisOptionChange}
                      className="rounded border-secondary-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                    />
                    <label htmlFor="includeScripts" className="ml-2 text-sm text-secondary-700">
                      Include script files
                    </label>
                  </div>
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="preserveCritical"
                      name="preserveCritical"
                      checked={analysisOptions.preserveCritical}
                      onChange={handleAnalysisOptionChange}
                      className="rounded border-secondary-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                    />
                    <label htmlFor="preserveCritical" className="ml-2 text-sm text-secondary-700">
                      Preserve critical functions
                    </label>
                  </div>
                </div>
                <div>
                  <div className="mb-3">
                    <label htmlFor="maxFilesToAnalyze" className="block text-sm text-secondary-700 mb-1">
                      Max files to analyze
                    </label>
                    <input
                      type="number"
                      id="maxFilesToAnalyze"
                      name="maxFilesToAnalyze"
                      value={analysisOptions.maxFilesToAnalyze}
                      onChange={handleAnalysisOptionChange}
                      min="1"
                      max="500"
                      className="w-full rounded-md border-secondary-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                    />
                  </div>
                </div>
              </div>
            </div>

            <div className="mt-6">
              <button
                onClick={runAnalysis}
                disabled={loading}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
              >
                {loading ? '⏳ Analyzing...' : '🔍 Analyze Code'}
              </button>
            </div>
          </div>
        )}

        {/* Results Tab */}
        {activeTab === 'results' && analysisResults && (
          <div>
            <div className="mb-4">
              <h4 className="text-md font-medium text-secondary-900 mb-2">
                Analysis Results
              </h4>
              <div className="bg-secondary-50 rounded-lg p-4 mb-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-secondary-700">
                      <span className="font-medium">Total files scanned:</span> {analysisResults.stats?.totalFiles || 0}
                    </p>
                    <p className="text-sm text-secondary-700">
                      <span className="font-medium">Total functions:</span> {analysisResults.stats?.totalFunctions || 0}
                    </p>
                    <p className="text-sm text-secondary-700">
                      <span className="font-medium">Critical functions:</span> {analysisResults.stats?.criticalFunctions || 0}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-secondary-700">
                      <span className="font-medium">Unused functions:</span> {analysisResults.stats?.unusedFunctions || 0}
                    </p>
                    <p className="text-sm text-secondary-700">
                      <span className="font-medium">Safe to remove:</span> {analysisResults.stats?.safeToRemove || 0}
                    </p>
                    <p className="text-sm text-secondary-700">
                      <span className="font-medium">Analysis time:</span> {analysisResults.timestamp ? new Date(analysisResults.timestamp).toLocaleString() : 'N/A'}
                    </p>
                  </div>
                </div>
              </div>

              {analysisResults.unusedFunctions?.length > 0 ? (
                <div>
                  <h5 className="text-sm font-medium text-secondary-900 mb-2">
                    Unused Functions ({analysisResults.unusedFunctions.length})
                  </h5>
                  <div className="border border-secondary-200 rounded-lg overflow-hidden">
                    <table className="min-w-full divide-y divide-secondary-200">
                      <thead className="bg-secondary-50">
                        <tr>
                          <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                            Function Name
                          </th>
                          <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                            File
                          </th>
                          <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                            Type
                          </th>
                          <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                            Exported
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-secondary-200">
                        {analysisResults.unusedFunctions.slice(0, 10).map((func, index) => (
                          <tr key={index}>
                            <td className="px-4 py-2 text-sm text-secondary-900">
                              {func.name}
                            </td>
                            <td className="px-4 py-2 text-sm text-secondary-500">
                              {func.file}
                            </td>
                            <td className="px-4 py-2 text-sm text-secondary-500">
                              {func.type}
                            </td>
                            <td className="px-4 py-2 text-sm text-secondary-500">
                              {func.exported ? 'Yes' : 'No'}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                    {analysisResults.unusedFunctions.length > 10 && (
                      <div className="px-4 py-2 bg-secondary-50 text-sm text-secondary-500">
                        Showing 10 of {analysisResults.unusedFunctions.length} unused functions
                      </div>
                    )}
                  </div>
                </div>
              ) : (
                <div className="p-4 bg-green-50 border border-green-200 rounded-md">
                  <p className="text-sm text-green-700">
                    ✅ No unused functions detected. Codebase appears clean.
                  </p>
                </div>
              )}
            </div>

            {analysisResults.unusedFunctions?.length > 0 && (
              <div className="mt-6">
                <h4 className="text-md font-medium text-secondary-900 mb-2">
                  Cleanup Options
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div className="space-y-3">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="dryRun"
                        name="dryRun"
                        checked={cleanupOptions.dryRun}
                        onChange={handleCleanupOptionChange}
                        className="rounded border-secondary-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                      />
                      <label htmlFor="dryRun" className="ml-2 text-sm text-secondary-700">
                        Dry run (no actual changes)
                      </label>
                    </div>
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="createBackup"
                        name="createBackup"
                        checked={cleanupOptions.createBackup}
                        onChange={handleCleanupOptionChange}
                        className="rounded border-secondary-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                      />
                      <label htmlFor="createBackup" className="ml-2 text-sm text-secondary-700">
                        Create backup before cleanup
                      </label>
                    </div>
                  </div>
                  <div>
                    <div className="mb-3">
                      <label htmlFor="maxFilesToModify" className="block text-sm text-secondary-700 mb-1">
                        Max files to modify
                      </label>
                      <input
                        type="number"
                        id="maxFilesToModify"
                        name="maxFilesToModify"
                        value={cleanupOptions.maxFilesToModify}
                        onChange={handleCleanupOptionChange}
                        min="1"
                        max="100"
                        className="w-full rounded-md border-secondary-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                      />
                    </div>
                  </div>
                </div>

                <div className="mt-4">
                  <button
                    onClick={executeCleanup}
                    disabled={loading}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                  >
                    {loading ? '⏳ Executing...' : '🧹 Execute Cleanup'}
                  </button>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Cleanup Tab */}
        {activeTab === 'cleanup' && cleanupResults && (
          <div>
            <div className="mb-4">
              <h4 className="text-md font-medium text-secondary-900 mb-2">
                Cleanup Results
              </h4>
              <div className="bg-secondary-50 rounded-lg p-4 mb-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-secondary-700">
                      <span className="font-medium">Files modified:</span> {cleanupResults.stats?.filesModified || 0}
                    </p>
                    <p className="text-sm text-secondary-700">
                      <span className="font-medium">Functions removed:</span> {cleanupResults.stats?.functionsRemoved || 0}
                    </p>
                    <p className="text-sm text-secondary-700">
                      <span className="font-medium">Errors:</span> {cleanupResults.stats?.errors || 0}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-secondary-700">
                      <span className="font-medium">Dry run:</span> {cleanupResults.dryRun ? 'Yes' : 'No'}
                    </p>
                    <p className="text-sm text-secondary-700">
                      <span className="font-medium">Backup created:</span> {cleanupResults.backupPath ? 'Yes' : 'No'}
                    </p>
                    <p className="text-sm text-secondary-700">
                      <span className="font-medium">Execution time:</span> {cleanupResults.timestamp ? new Date(cleanupResults.timestamp).toLocaleString() : 'N/A'}
                    </p>
                  </div>
                </div>
              </div>

              {cleanupResults.modifiedFiles?.length > 0 && (
                <div className="mb-4">
                  <h5 className="text-sm font-medium text-secondary-900 mb-2">
                    Modified Files ({cleanupResults.modifiedFiles.length})
                  </h5>
                  <div className="border border-secondary-200 rounded-lg p-4 max-h-40 overflow-y-auto">
                    <ul className="space-y-1">
                      {cleanupResults.modifiedFiles.map((file, index) => (
                        <li key={index} className="text-sm text-secondary-700">
                          {file}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              )}

              {!cleanupResults.dryRun && cleanupResults.backupPath && (
                <div className="mt-6">
                  <button
                    onClick={rollbackChanges}
                    disabled={loading}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
                  >
                    {loading ? '⏳ Rolling back...' : '↩️ Rollback Changes'}
                  </button>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Test Files Tab */}
        {activeTab === 'testFiles' && (
          <div>
            <div className="mb-4">
              <h4 className="text-md font-medium text-secondary-900 mb-2">
                Test Files Cleanup
              </h4>
              <p className="text-sm text-secondary-600 mb-4">
                Analyze and clean up test files matching specified patterns
              </p>

              {/* Include Patterns */}
              <div className="mb-4">
                <h5 className="text-sm font-medium text-secondary-900 mb-2">
                  Include Patterns
                </h5>
                <div className="space-y-2">
                  {testFilesOptions.includePatterns.map((pattern, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <input
                        type="text"
                        value={pattern}
                        onChange={(e) => handlePatternChange(e, index, 'includePatterns')}
                        placeholder="e.g., *test*, test-*, test*"
                        className="flex-1 rounded-md border-secondary-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                      />
                      <button
                        onClick={() => removePattern(index, 'includePatterns')}
                        disabled={testFilesOptions.includePatterns.length <= 1}
                        className="px-2 py-1 text-red-600 hover:text-red-800 disabled:opacity-50"
                      >
                        ✕
                      </button>
                    </div>
                  ))}
                  <button
                    onClick={() => addPattern('includePatterns')}
                    className="text-sm text-blue-600 hover:text-blue-800"
                  >
                    + Add Pattern
                  </button>
                </div>
              </div>

              {/* Exclude Patterns */}
              <div className="mb-4">
                <h5 className="text-sm font-medium text-secondary-900 mb-2">
                  Exclude Patterns
                </h5>
                <div className="space-y-2">
                  {testFilesOptions.excludePatterns.map((pattern, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <input
                        type="text"
                        value={pattern}
                        onChange={(e) => handlePatternChange(e, index, 'excludePatterns')}
                        placeholder="e.g., node_modules/**, build/**"
                        className="flex-1 rounded-md border-secondary-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                      />
                      <button
                        onClick={() => removePattern(index, 'excludePatterns')}
                        disabled={testFilesOptions.excludePatterns.length <= 1}
                        className="px-2 py-1 text-red-600 hover:text-red-800 disabled:opacity-50"
                      >
                        ✕
                      </button>
                    </div>
                  ))}
                  <button
                    onClick={() => addPattern('excludePatterns')}
                    className="text-sm text-blue-600 hover:text-blue-800"
                  >
                    + Add Pattern
                  </button>
                </div>
              </div>

              {/* Options */}
              <div className="mb-4">
                <h5 className="text-sm font-medium text-secondary-900 mb-2">
                  Cleanup Options
                </h5>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-3">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="testFilesDryRun"
                        name="dryRun"
                        checked={testFilesOptions.dryRun}
                        onChange={handleTestFilesOptionChange}
                        className="rounded border-secondary-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                      />
                      <label htmlFor="testFilesDryRun" className="ml-2 text-sm text-secondary-700">
                        Dry run (preview only)
                      </label>
                    </div>
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="testFilesCreateBackup"
                        name="createBackup"
                        checked={testFilesOptions.createBackup}
                        onChange={handleTestFilesOptionChange}
                        className="rounded border-secondary-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                      />
                      <label htmlFor="testFilesCreateBackup" className="ml-2 text-sm text-secondary-700">
                        Create backup before deletion
                      </label>
                    </div>
                  </div>
                  <div>
                    <div className="mb-3">
                      <label htmlFor="maxFilesToDelete" className="block text-sm text-secondary-700 mb-1">
                        Max files to delete
                      </label>
                      <input
                        type="number"
                        id="maxFilesToDelete"
                        name="maxFilesToDelete"
                        value={testFilesOptions.maxFilesToDelete}
                        onChange={handleTestFilesOptionChange}
                        min="1"
                        max="200"
                        className="w-full rounded-md border-secondary-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Analyze Button */}
              <div className="mb-6">
                <button
                  onClick={analyzeTestFiles}
                  disabled={loading}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                >
                  {loading ? '⏳ Analyzing...' : '🔍 Analyze Test Files'}
                </button>
              </div>

              {/* Test Files Results */}
              {testFilesResults && (
                <div className="mb-6">
                  <h5 className="text-sm font-medium text-secondary-900 mb-2">
                    Analysis Results
                  </h5>
                  <div className="bg-secondary-50 rounded-lg p-4 mb-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm text-secondary-700">
                          <span className="font-medium">Total test files found:</span> {testFilesResults.stats?.totalFiles || 0}
                        </p>
                        <p className="text-sm text-secondary-700">
                          <span className="font-medium">Total size:</span> {testFilesResults.stats?.totalSize || '0 B'}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-secondary-700">
                          <span className="font-medium">Patterns matched:</span> {testFilesResults.stats?.patternsMatched || 0}
                        </p>
                        <p className="text-sm text-secondary-700">
                          <span className="font-medium">Analysis time:</span> {testFilesResults.timestamp ? new Date(testFilesResults.timestamp).toLocaleString() : 'N/A'}
                        </p>
                      </div>
                    </div>
                  </div>

                  {testFilesResults.testFiles?.length > 0 ? (
                    <div>
                      <h6 className="text-sm font-medium text-secondary-900 mb-2">
                        Test Files Found ({testFilesResults.testFiles.length})
                      </h6>
                      <div className="border border-secondary-200 rounded-lg overflow-hidden mb-4">
                        <div className="max-h-60 overflow-y-auto">
                          <table className="min-w-full divide-y divide-secondary-200">
                            <thead className="bg-secondary-50 sticky top-0">
                              <tr>
                                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                                  File Path
                                </th>
                                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                                  Size
                                </th>
                                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                                  Pattern
                                </th>
                              </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-secondary-200">
                              {testFilesResults.testFiles.map((file, index) => (
                                <tr key={index}>
                                  <td className="px-4 py-2 text-sm text-secondary-900">
                                    {file.path}
                                  </td>
                                  <td className="px-4 py-2 text-sm text-secondary-500">
                                    {file.size}
                                  </td>
                                  <td className="px-4 py-2 text-sm text-secondary-500">
                                    {file.matchedPattern}
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      </div>

                      {/* Cleanup Button */}
                      <div className="flex space-x-4">
                        <button
                          onClick={cleanupTestFiles}
                          disabled={loading}
                          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
                        >
                          {loading ? '⏳ Processing...' : (testFilesOptions.dryRun ? '👁️ Preview Cleanup' : '🗑️ Delete Test Files')}
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div className="p-4 bg-green-50 border border-green-200 rounded-md">
                      <p className="text-sm text-green-700">
                        ✅ No test files found matching the specified patterns.
                      </p>
                    </div>
                  )}

                  {/* Cleanup Results */}
                  {testFilesResults.cleanupResults && (
                    <div className="mt-6">
                      <h6 className="text-sm font-medium text-secondary-900 mb-2">
                        Cleanup Results
                      </h6>
                      <div className="bg-secondary-50 rounded-lg p-4 mb-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <p className="text-sm text-secondary-700">
                              <span className="font-medium">Files deleted:</span> {testFilesResults.cleanupResults.stats?.filesDeleted || 0}
                            </p>
                            <p className="text-sm text-secondary-700">
                              <span className="font-medium">Space freed:</span> {testFilesResults.cleanupResults.stats?.spaceFreed || '0 B'}
                            </p>
                            <p className="text-sm text-secondary-700">
                              <span className="font-medium">Errors:</span> {testFilesResults.cleanupResults.stats?.errors || 0}
                            </p>
                          </div>
                          <div>
                            <p className="text-sm text-secondary-700">
                              <span className="font-medium">Dry run:</span> {testFilesResults.cleanupResults.dryRun ? 'Yes' : 'No'}
                            </p>
                            <p className="text-sm text-secondary-700">
                              <span className="font-medium">Backup created:</span> {testFilesResults.cleanupResults.backupPath ? 'Yes' : 'No'}
                            </p>
                            <p className="text-sm text-secondary-700">
                              <span className="font-medium">Execution time:</span> {testFilesResults.cleanupResults.timestamp ? new Date(testFilesResults.cleanupResults.timestamp).toLocaleString() : 'N/A'}
                            </p>
                          </div>
                        </div>
                      </div>

                      {testFilesResults.cleanupResults.deletedFiles?.length > 0 && (
                        <div className="mb-4">
                          <h6 className="text-sm font-medium text-secondary-900 mb-2">
                            {testFilesResults.cleanupResults.dryRun ? 'Files to be deleted' : 'Deleted Files'} ({testFilesResults.cleanupResults.deletedFiles.length})
                          </h6>
                          <div className="border border-secondary-200 rounded-lg p-4 max-h-40 overflow-y-auto">
                            <ul className="space-y-1">
                              {testFilesResults.cleanupResults.deletedFiles.map((file, index) => (
                                <li key={index} className="text-sm text-secondary-700">
                                  {file}
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      )}

                      {!testFilesResults.cleanupResults.dryRun && testFilesResults.cleanupResults.backupPath && (
                        <div className="mt-4">
                          <button
                            onClick={rollbackTestFileChanges}
                            disabled={loading}
                            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50"
                          >
                            {loading ? '⏳ Rolling back...' : '↩️ Restore Test Files'}
                          </button>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              )}

              {/* Help Text for Test Files */}
              <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
                <h6 className="text-sm font-medium text-yellow-900 mb-2">⚠️ Test Files Cleanup:</h6>
                <ul className="text-sm text-yellow-700 space-y-1">
                  <li>• Matches files using glob patterns: *test*, test-*, test*</li>
                  <li>• Always use dry run first to preview what will be deleted</li>
                  <li>• Creates backup before deletion for safety</li>
                  <li>• Excludes node_modules and build directories by default</li>
                  <li>• Be careful - deleted test files cannot be easily recovered</li>
                  <li>• Consider version control before running cleanup</li>
                </ul>
              </div>
            </div>
          </div>
        )}

        {/* Verification Tab */}
        {activeTab === 'verification' && (
          <div>
            <div className="mb-4">
              <h4 className="text-md font-medium text-secondary-900 mb-2">
                System Verificationon
              </h4>
              <p className="text-sm text-secondary-600 mb-4">
                Verify system integrity after cleanup operations
              </p>

              <button
                onClick={verifySystem}
                disabled={loading}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
              >
                {loading ? '⏳ Verifying...' : '🔍 Verify System Integrity'}
              </button>

              {verificationResults && (
                <div className="mt-4">
                  <div className={`p-4 rounded-md ${verificationResults.success
                    ? 'bg-green-50 border border-green-200'
                    : 'bg-red-50 border border-red-200'
                    }`}>
                    <p className={`text-sm font-medium ${verificationResults.success ? 'text-green-700' : 'text-red-700'
                      }`}>
                      {verificationResults.success
                        ? '✅ System integrity check passed'
                        : '❌ System integrity check failed'}
                    </p>

                    {verificationResults.missingFiles?.length > 0 && (
                      <div className="mt-2">
                        <p className="text-sm text-red-700 font-medium">Missing critical files:</p>
                        <ul className="mt-1 space-y-1">
                          {verificationResults.missingFiles.map((file, index) => (
                            <li key={index} className="text-sm text-red-600">
                              • {file}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {verificationResults.serverRequireError && (
                      <p className="mt-2 text-sm text-red-600">
                        Server require error: {verificationResults.serverRequireError}
                      </p>
                    )}

                    {verificationResults.dbConnectError && (
                      <p className="mt-2 text-sm text-red-600">
                        Database connection error: {verificationResults.dbConnectError}
                      </p>
                    )}

                    <p className="mt-2 text-xs text-secondary-500">
                      Verification time: {new Date(verificationResults.timestamp).toLocaleString()}
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Help Text */}
        <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-md">
          <h5 className="text-sm font-medium text-blue-900 mb-2">ℹ️ About Code Cleanup:</h5>
          <ul className="text-sm text-blue-700 space-y-1">
            <li>• Analyzes server and script files for unused JavaScript functions</li>
            <li>• Preserves critical functions (routes, middleware, database operations)</li>
            <li>• Creates backups before making any changes</li>
            <li>• Provides rollback capability if issues are detected</li>
            <li>• Verifies system integrity after cleanup operations</li>
            <li>• Use dry run mode to preview changes without modifying files</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default CleanupManagementPanel;