import { useState, useCallback, useRef } from 'react';

export const useQRScanner = () => {
  const [isScanning, setIsScanning] = useState(false);
  const [cameraError, setCameraError] = useState(null);
  const [facingMode, setFacingMode] = useState('environment');
  const [loading, setLoading] = useState(false);

  // Debounce scan processing to prevent rapid duplicate scans
  const lastScanRef = useRef('');
  const scanTimeoutRef = useRef();

  const refreshCamera = useCallback(() => {
    setIsScanning(false);
    setTimeout(() => {
      setIsScanning(true);
    }, 500);
  }, []);

  const toggleCamera = useCallback(() => {
    setCameraError(null);
    setIsScanning(!isScanning);
  }, [isScanning]);

  const switchCamera = useCallback(() => {
    setFacingMode(prev => prev === 'environment' ? 'user' : 'environment');
  }, []);

  const handleScanError = useCallback((error) => {
    console.error('Scanner error:', error);
    
    let errorMessage = 'Camera error. Please try again.';
    
    if (error?.name === 'NotAllowedError') {
      errorMessage = 'Camera permission denied. Please allow camera access.';
    } else if (error?.name === 'NotFoundError') {
      errorMessage = 'No camera found. Please check your device.';
    } else if (error?.message?.includes('WebAssembly') || error?.message?.includes('wasm')) {
      errorMessage = 'Browser compatibility issue. Please try refreshing the page or use a different browser.';
    } else if (error?.message?.includes('BarcodeDetector')) {
      errorMessage = 'QR scanning not supported in this browser. Please try Chrome or Edge.';
    }
    
    setCameraError(errorMessage);
    return errorMessage;
  }, []);

  return {
    isScanning,
    cameraError,
    facingMode,
    loading,
    setLoading,
    lastScanRef,
    scanTimeoutRef,
    refreshCamera,
    toggleCamera,
    switchCamera,
    handleScanError
  };
};