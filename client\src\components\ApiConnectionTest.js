import React, { useState, useEffect } from 'react';
import { getApiUrlWithFallback, testBackendConnectivity } from '../utils/network-utils';

/**
 * Component to test and display API connectivity status
 * Useful for debugging production VPS IP access
 */
const ApiConnectionTest = ({ showDetails = false }) => {
  const [connectionStatus, setConnectionStatus] = useState({
    primary: null,
    fallback: null,
    loading: true
  });

  const testConnections = async () => {
    setConnectionStatus(prev => ({ ...prev, loading: true }));
    
    const urls = getApiUrlWithFallback();
    
    // Test primary URL
    const primaryTest = await testBackendConnectivity(urls.primary);
    
    // Test fallback URL if available
    let fallbackTest = null;
    if (urls.fallback) {
      fallbackTest = await testBackendConnectivity(urls.fallback);
    }
    
    setConnectionStatus({
      primary: primaryTest,
      fallback: fallbackTest,
      loading: false,
      urls
    });
  };

  useEffect(() => {
    testConnections();
  }, []);

  const getStatusIcon = (test) => {
    if (!test) return '❓';
    return test.available ? '✅' : '❌';
  };

  const getStatusText = (test) => {
    if (!test) return 'Not tested';
    if (test.available) return `OK (${test.status})`;
    return `Failed (${test.error || test.status || 'Unknown error'})`;
  };

  if (!showDetails) {
    // Simple status indicator
    const primaryOk = connectionStatus.primary?.available;
    const fallbackOk = connectionStatus.fallback?.available;
    const anyOk = primaryOk || fallbackOk;
    
    return (
      <div className="inline-flex items-center space-x-2">
        <span className={`text-sm ${anyOk ? 'text-green-600' : 'text-red-600'}`}>
          {connectionStatus.loading ? '⏳' : anyOk ? '✅' : '❌'} API
        </span>
        {showDetails && (
          <button 
            onClick={testConnections}
            className="text-xs text-blue-600 hover:text-blue-800"
            disabled={connectionStatus.loading}
          >
            Test
          </button>
        )}
      </div>
    );
  }

  return (
    <div className="bg-white border rounded-lg p-4 shadow-sm">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-lg font-medium text-gray-900">API Connection Status</h3>
        <button
          onClick={testConnections}
          disabled={connectionStatus.loading}
          className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
        >
          {connectionStatus.loading ? 'Testing...' : 'Test Again'}
        </button>
      </div>
      
      {connectionStatus.urls && (
        <div className="space-y-3">
          {/* Primary URL */}
          <div className="flex items-center space-x-3">
            <span className="text-xl">{getStatusIcon(connectionStatus.primary)}</span>
            <div className="flex-1">
              <div className="text-sm font-medium text-gray-900">Primary API</div>
              <div className="text-xs text-gray-600 truncate">{connectionStatus.urls.primary}</div>
              <div className="text-xs text-gray-500">{getStatusText(connectionStatus.primary)}</div>
            </div>
          </div>
          
          {/* Fallback URL */}
          {connectionStatus.urls.fallback && (
            <div className="flex items-center space-x-3">
              <span className="text-xl">{getStatusIcon(connectionStatus.fallback)}</span>
              <div className="flex-1">
                <div className="text-sm font-medium text-gray-900">Fallback API (VPS IP)</div>
                <div className="text-xs text-gray-600 truncate">{connectionStatus.urls.fallback}</div>
                <div className="text-xs text-gray-500">{getStatusText(connectionStatus.fallback)}</div>
              </div>
            </div>
          )}
          
          {/* VPS IP Info */}
          {connectionStatus.urls.vpsIP && (
            <div className="pt-2 border-t border-gray-200">
              <div className="text-xs text-gray-600">
                VPS IP: <span className="font-mono">{connectionStatus.urls.vpsIP}</span>
              </div>
            </div>
          )}
          
          {/* Connection Summary */}
          <div className="pt-2 border-t border-gray-200">
            <div className="text-sm">
              {connectionStatus.primary?.available ? (
                <span className="text-green-600">✅ Primary connection working</span>
              ) : connectionStatus.fallback?.available ? (
                <span className="text-yellow-600">⚠️ Using fallback connection</span>
              ) : (
                <span className="text-red-600">❌ No API connection available</span>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ApiConnectionTest;
