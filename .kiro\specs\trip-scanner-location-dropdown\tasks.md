# Implementation Plan

- [x] 1. Add location API endpoint for dropdown data
  - Add `getActive` method to `locationsAPI` in `client/src/services/api.js`
  - Implement API call to fetch active locations with required fields (id, name, type, code)
  - Add error handling and retry logic for location fetching
  - _Requirements: 2.1, 2.3, 2.4_

- [x] 2. Create LocationDropdown component with core functionality
  - Create new component file `client/src/components/LocationDropdown.js`
  - Implement dropdown structure with search input and options list
  - Add loading states, error handling, and "no results found" messaging
  - Implement mobile-responsive design with touch-friendly interactions (44px targets)
  - Add debounced search functionality (300ms delay) for real-time filtering
  - _Requirements: 1.1, 1.2, 1.3, 2.2, 5.1, 5.2, 5.4, 9.4, 9.5_

- [x] 3. Implement location selection and data consistency
  - Create location selection handler that generates identical locationData structure as QR scanning
  - Ensure locationData format matches: `{id: location.code, type: 'location', name: location.name, last_scan: ISO_timestamp}`
  - Implement localStorage storage using exact same key 'tripScanner_locationScanData'
  - Add validation for location data structure before storage
  - _Requirements: 7.1, 7.2, 7.3_

- [x] 4. Integrate LocationDropdown into TripScanner component
  - Import LocationDropdown component into TripScanner
  - Replace location QR scanning UI with dropdown when scanStep === 'location'
  - Update `getStepInstructions()` method to show dropdown instructions for Step 1
  - Maintain existing state management for `locationScanData` and `scanStep`
  - Preserve all existing truck scanning functionality (Step 2) unchanged
  - _Requirements: 1.1, 3.1, 3.2, 4.1, 4.2_

- [x] 5. Implement location restoration and persistence
  - Ensure dropdown shows selected location when restored from localStorage on component mount
  - Use existing location restoration logic in useEffect hook
  - Update dropdown state to reflect restored location selection
  - Maintain location selection across page refreshes
  - _Requirements: 6.4, 7.3_

- [x] 6. Update reset functionality for dropdown compatibility
  - Modify reset confirmation dialog to reference "location selection" instead of "QR scanning"
  - Ensure `resetScannerState()` function works with dropdown (clear selection vs keep location)
  - Test both full reset (clear location) and partial reset (keep location) scenarios
  - Maintain existing reset button behavior and user experience
  - _Requirements: 6.2, 6.3, 7.4, 7.5_

- [x] 7. Implement online-only operation and error handling
  - Add online status checking before allowing location selection (use existing `isOnline` state)
  - Display "Internet Connection Required" message when offline (same as current QR implementation)
  - Implement proper error handling for API failures during location fetching
  - Add loading states and user feedback for all dropdown operations
  - _Requirements: 8.2, 8.3, 9.3_

- [x] 8. Add mobile and PWA optimizations
  - Ensure dropdown works identically in PWA and web browser modes
  - Implement proper viewport handling to prevent zoom on input focus
  - Test dropdown behavior in landscape and portrait orientations
  - Optimize touch interactions for mobile devices
  - _Requirements: 5.1, 5.2, 5.3, 8.1, 8.4_

- [x] 9. Implement enhanced UI/UX features
  - Add clear visual distinction between location selection (dropdown) and truck scanning steps
  - Display location type (loading/unloading) in dropdown options for easy identification
  - Implement location caching mechanism with 5-minute expiration to reduce API calls
  - Add proper error recovery and state cleanup for all error scenarios
  - _Requirements: 2.1, 2.5, 4.5, 9.1_

- [x] 10. Create unit tests for LocationDropdown component
  - Write tests for location fetching and API integration
  - Test search functionality and debouncing behavior
  - Test location selection and data structure creation
  - Test error handling and loading states
  - Test mobile responsiveness and touch interactions
  - _Requirements: All requirements validation_

- [x] 11. Create integration tests for TripScanner enhancement
  - Test complete workflow from location selection to truck scanning
  - Test localStorage integration and data persistence
  - Test reset functionality with both full and partial reset scenarios
  - Test online/offline behavior and error recovery
  - Validate that existing truck scanning (Step 2) continues to work unchanged
  - _Requirements: All requirements validation_

- [x] 12. Validate data consistency and backend compatibility
  - Test that dropdown-selected location data is processed identically to QR-scanned data
  - Verify that existing backend validation and error handling continues to work
  - Test trip workflow completion and exception handling with dropdown-selected locations
  - Ensure no changes are required to backend API endpoints
  - _Requirements: 3.3, 7.1, 7.2_

- [x] 13. Final integration testing and deployment preparation





  - Perform end-to-end testing of complete trip workflow with location dropdown
  - Test all reset scenarios and location persistence functionality
  - Validate that all existing TripScanner features continue to work unchanged
  - Test mobile and PWA functionality across different devices and screen sizes
  - Prepare deployment with feature flag for gradual rollout
  - _Requirements: All requirements validation_