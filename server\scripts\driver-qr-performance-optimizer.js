#!/usr/bin/env node

/**
 * Driver QR System Performance Optimizer
 * 
 * This script analyzes and optimizes the performance of the Driver QR system:
 * 1. Verifies GIN index effectiveness for driver QR code lookups
 * 2. Tests composite index performance for driver_shifts queries
 * 3. Optimizes database queries with proper connection pooling
 * 4. Tests concurrent user load and race condition handling
 * 5. Monitors public endpoint performance and rate limiting
 */

const { query, getClient } = require('../config/database');
const DriverQRService = require('../services/DriverQRService');

class DriverQRPerformanceOptimizer {
  constructor() {
    this.results = {
      ginIndexEffectiveness: null,
      compositeIndexPerformance: null,
      connectionPooling: null,
      concurrentLoadHandling: null,
      rateLimitingEffectiveness: null,
      overallPerformance: null
    };
  }

  async runOptimization() {
    console.log('🚀 Starting Driver QR System Performance Optimization...\n');

    try {
      // Test 1: Verify GIN index effectiveness
      await this.testGINIndexEffectiveness();

      // Test 2: Test composite index performance
      await this.testCompositeIndexPerformance();

      // Test 3: Verify connection pooling optimization
      await this.testConnectionPooling();

      // Test 4: Test concurrent user load handling
      await this.testConcurrentLoadHandling();

      // Test 5: Monitor rate limiting effectiveness
      await this.testRateLimitingEffectiveness();

      // Overall assessment
      this.assessOverallPerformance();

      this.printResults();

    } catch (error) {
      console.error('❌ Performance optimization failed with error:', error.message);
      process.exit(1);
    }
  }

  async testGINIndexEffectiveness() {
    console.log('1️⃣ Testing GIN index effectiveness for driver QR code lookups...');

    try {
      // Check if GIN index exists
      const indexCheck = await query(`
        SELECT indexname, indexdef 
        FROM pg_indexes 
        WHERE tablename = 'drivers' 
          AND indexname = 'idx_drivers_qr_code_gin'
      `);

      if (indexCheck.rows.length > 0) {
        console.log('   ✅ GIN index idx_drivers_qr_code_gin exists');
        console.log(`   📋 Index definition: ${indexCheck.rows[0].indexdef}`);
      } else {
        console.log('   ❌ GIN index idx_drivers_qr_code_gin not found');
        this.results.ginIndexEffectiveness = false;
        return;
      }

      // Test query performance with EXPLAIN ANALYZE
      const testQuery = `
        EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON)
        SELECT id, employee_id, full_name, driver_qr_code
        FROM drivers 
        WHERE driver_qr_code @> '{"driver_id": 1}'::jsonb
          AND status = 'active'
      `;

      const explainResult = await query(testQuery);
      const queryPlan = explainResult.rows[0]['QUERY PLAN'][0];

      console.log(`   📊 Query execution time: ${queryPlan['Execution Time']}ms`);
      console.log(`   📊 Planning time: ${queryPlan['Planning Time']}ms`);

      // Check if GIN index is being used
      const usesGINIndex = JSON.stringify(queryPlan).includes('idx_drivers_qr_code_gin');
      
      if (usesGINIndex) {
        console.log('   ✅ GIN index is being used effectively');
        this.results.ginIndexEffectiveness = true;
      } else {
        console.log('   ⚠️ GIN index may not be used optimally');
        this.results.ginIndexEffectiveness = false;
      }

      // Performance benchmark
      const startTime = Date.now();
      await query(`
        SELECT id, employee_id, full_name
        FROM drivers 
        WHERE driver_qr_code @> '{"driver_id": 1}'::jsonb
          AND status = 'active'
        LIMIT 10
      `);
      const endTime = Date.now();

      console.log(`   ⚡ Actual query performance: ${endTime - startTime}ms`);

      if (endTime - startTime < 50) {
        console.log('   ✅ Query performance is excellent (<50ms)');
      } else if (endTime - startTime < 100) {
        console.log('   ✅ Query performance is good (<100ms)');
      } else {
        console.log('   ⚠️ Query performance could be improved (>100ms)');
      }

    } catch (error) {
      console.log('   ❌ GIN index effectiveness test failed:', error.message);
      this.results.ginIndexEffectiveness = false;
    }
  }

  async testCompositeIndexPerformance() {
    console.log('\n2️⃣ Testing composite index performance for driver_shifts queries...');

    try {
      // Check composite indexes
      const indexesCheck = await query(`
        SELECT indexname, indexdef 
        FROM pg_indexes 
        WHERE tablename = 'driver_shifts' 
          AND indexname IN (
            'idx_driver_shifts_driver_status',
            'idx_driver_shifts_truck_status_active',
            'idx_driver_shifts_start_date_status'
          )
      `);

      console.log(`   📊 Found ${indexesCheck.rows.length} composite indexes for driver_shifts`);
      indexesCheck.rows.forEach(index => {
        console.log(`   ✅ ${index.indexname}: ${index.indexdef}`);
      });

      // Test most common query patterns with EXPLAIN ANALYZE
      const testQueries = [
        {
          name: 'Active driver lookup by truck',
          query: `
            EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON)
            SELECT ds.driver_id, d.full_name, ds.start_time
            FROM driver_shifts ds
            JOIN drivers d ON ds.driver_id = d.id
            WHERE ds.truck_id = 1 AND ds.status = 'active'
            ORDER BY ds.created_at DESC
            LIMIT 1
          `
        },
        {
          name: 'Driver shift history lookup',
          query: `
            EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON)
            SELECT ds.id, ds.start_date, ds.start_time, ds.end_time, ds.status
            FROM driver_shifts ds
            WHERE ds.driver_id = 1 AND ds.status IN ('active', 'completed')
            ORDER BY ds.start_date DESC, ds.start_time DESC
            LIMIT 10
          `
        },
        {
          name: 'Date range attendance query',
          query: `
            EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON)
            SELECT COUNT(*) as shift_count
            FROM driver_shifts ds
            WHERE ds.start_date >= CURRENT_DATE - INTERVAL '7 days'
              AND ds.status = 'completed'
          `
        }
      ];

      let totalExecutionTime = 0;
      let optimizedQueries = 0;

      for (const testQuery of testQueries) {
        try {
          const explainResult = await query(testQuery.query);
          const queryPlan = explainResult.rows[0]['QUERY PLAN'][0];
          const executionTime = queryPlan['Execution Time'];

          console.log(`   📊 ${testQuery.name}: ${executionTime}ms`);
          totalExecutionTime += executionTime;

          if (executionTime < 10) {
            optimizedQueries++;
          }
        } catch (error) {
          console.log(`   ⚠️ ${testQuery.name}: Failed to analyze`);
        }
      }

      const avgExecutionTime = totalExecutionTime / testQueries.length;
      console.log(`   📊 Average query execution time: ${avgExecutionTime.toFixed(2)}ms`);

      if (optimizedQueries >= testQueries.length - 1) {
        console.log('   ✅ Composite indexes are performing excellently');
        this.results.compositeIndexPerformance = true;
      } else {
        console.log('   ⚠️ Some queries could benefit from index optimization');
        this.results.compositeIndexPerformance = false;
      }

    } catch (error) {
      console.log('   ❌ Composite index performance test failed:', error.message);
      this.results.compositeIndexPerformance = false;
    }
  }

  async testConnectionPooling() {
    console.log('\n3️⃣ Testing database connection pooling optimization...');

    try {
      // Test connection pool status
      const poolStats = await query(`
        SELECT 
          count(*) as total_connections,
          count(*) FILTER (WHERE state = 'active') as active_connections,
          count(*) FILTER (WHERE state = 'idle') as idle_connections
        FROM pg_stat_activity 
        WHERE datname = current_database()
      `);

      const stats = poolStats.rows[0];
      console.log(`   📊 Total connections: ${stats.total_connections}`);
      console.log(`   📊 Active connections: ${stats.active_connections}`);
      console.log(`   📊 Idle connections: ${stats.idle_connections}`);

      // Test concurrent connection handling
      const concurrentQueries = [];
      const startTime = Date.now();

      for (let i = 0; i < 5; i++) {
        concurrentQueries.push(
          query('SELECT COUNT(*) FROM driver_shifts WHERE status = $1', ['active'])
        );
      }

      await Promise.all(concurrentQueries);
      const endTime = Date.now();

      console.log(`   ⚡ 5 concurrent queries completed in: ${endTime - startTime}ms`);

      if (endTime - startTime < 100) {
        console.log('   ✅ Connection pooling is working efficiently');
        this.results.connectionPooling = true;
      } else {
        console.log('   ⚠️ Connection pooling could be optimized');
        this.results.connectionPooling = false;
      }

      // Test transaction handling
      const client = await getClient();
      try {
        await client.query('BEGIN');
        await client.query('SELECT 1');
        await client.query('COMMIT');
        console.log('   ✅ Transaction handling is working correctly');
      } catch (error) {
        await client.query('ROLLBACK');
        console.log('   ⚠️ Transaction handling issue detected');
      } finally {
        client.release();
      }

    } catch (error) {
      console.log('   ❌ Connection pooling test failed:', error.message);
      this.results.connectionPooling = false;
    }
  }

  async testConcurrentLoadHandling() {
    console.log('\n4️⃣ Testing concurrent user load and race condition handling...');

    try {
      // Simulate concurrent driver check-ins to the same truck
      const testTruckId = 1;
      const testDriverIds = [1, 2, 3];

      console.log('   🔄 Simulating concurrent driver connections...');

      const concurrentOperations = testDriverIds.map(async (driverId, index) => {
        try {
          // Simulate slight timing differences
          await new Promise(resolve => setTimeout(resolve, index * 10));
          
          const client = await getClient();
          try {
            await client.query('BEGIN');
            
            // Simulate the driver connection process with row-level locking
            const lockResult = await client.query(`
              SELECT ds.id, ds.driver_id, ds.status
              FROM driver_shifts ds
              WHERE ds.truck_id = $1 AND ds.status = 'active'
              FOR UPDATE NOWAIT
            `, [testTruckId]);

            await client.query('COMMIT');
            return { success: true, driverId, lockResult: lockResult.rows };
          } catch (error) {
            await client.query('ROLLBACK');
            return { success: false, driverId, error: error.message };
          } finally {
            client.release();
          }
        } catch (error) {
          return { success: false, driverId, error: error.message };
        }
      });

      const results = await Promise.all(concurrentOperations);
      
      const successfulOperations = results.filter(r => r.success).length;
      const failedOperations = results.filter(r => !r.success).length;

      console.log(`   📊 Successful concurrent operations: ${successfulOperations}`);
      console.log(`   📊 Failed concurrent operations: ${failedOperations}`);

      // Test race condition prevention
      if (failedOperations > 0) {
        console.log('   ✅ Race condition prevention is working (some operations properly failed)');
      } else {
        console.log('   ⚠️ All operations succeeded - race condition handling needs verification');
      }

      // Test deadlock prevention
      const deadlockTest = await this.testDeadlockPrevention();
      
      if (deadlockTest) {
        console.log('   ✅ Deadlock prevention is working correctly');
        this.results.concurrentLoadHandling = true;
      } else {
        console.log('   ⚠️ Deadlock prevention needs improvement');
        this.results.concurrentLoadHandling = false;
      }

    } catch (error) {
      console.log('   ❌ Concurrent load handling test failed:', error.message);
      this.results.concurrentLoadHandling = false;
    }
  }

  async testDeadlockPrevention() {
    try {
      // Simple deadlock prevention test
      const client1 = await getClient();
      const client2 = await getClient();

      try {
        await client1.query('BEGIN');
        await client2.query('BEGIN');

        // Try to acquire locks in different orders
        await client1.query('SELECT * FROM driver_shifts WHERE id = 1 FOR UPDATE NOWAIT');
        
        try {
          await client2.query('SELECT * FROM driver_shifts WHERE id = 1 FOR UPDATE NOWAIT');
          // If this succeeds, there might be an issue
          await client2.query('ROLLBACK');
          await client1.query('ROLLBACK');
          return false;
        } catch (error) {
          // Expected behavior - second lock should fail
          await client2.query('ROLLBACK');
          await client1.query('ROLLBACK');
          return true;
        }
      } finally {
        client1.release();
        client2.release();
      }
    } catch (error) {
      return false;
    }
  }

  async testRateLimitingEffectiveness() {
    console.log('\n5️⃣ Testing rate limiting effectiveness for public endpoints...');

    try {
      // Check if rate limiting middleware exists
      const fs = require('fs');
      const driverRoutes = fs.readFileSync(require.resolve('../routes/driver.js'), 'utf8');
      
      const hasRateLimit = driverRoutes.includes('rate') || driverRoutes.includes('limit');
      
      if (hasRateLimit) {
        console.log('   ✅ Rate limiting middleware detected in driver routes');
      } else {
        console.log('   ⚠️ Rate limiting middleware not clearly detected');
      }

      // Simulate rapid requests (in a controlled manner)
      console.log('   🔄 Testing request throttling...');
      
      const rapidRequests = [];
      const startTime = Date.now();

      // Create 10 rapid database queries to simulate load
      for (let i = 0; i < 10; i++) {
        rapidRequests.push(
          query('SELECT COUNT(*) FROM drivers WHERE status = $1', ['active'])
        );
      }

      const results = await Promise.allSettled(rapidRequests);
      const endTime = Date.now();

      const successfulRequests = results.filter(r => r.status === 'fulfilled').length;
      const failedRequests = results.filter(r => r.status === 'rejected').length;

      console.log(`   📊 10 rapid requests completed in: ${endTime - startTime}ms`);
      console.log(`   📊 Successful requests: ${successfulRequests}`);
      console.log(`   📊 Failed requests: ${failedRequests}`);

      if (endTime - startTime < 500 && successfulRequests >= 8) {
        console.log('   ✅ System handles rapid requests efficiently');
        this.results.rateLimitingEffectiveness = true;
      } else {
        console.log('   ⚠️ System may need rate limiting optimization');
        this.results.rateLimitingEffectiveness = false;
      }

    } catch (error) {
      console.log('   ❌ Rate limiting effectiveness test failed:', error.message);
      this.results.rateLimitingEffectiveness = false;
    }
  }

  assessOverallPerformance() {
    const passedTests = Object.values(this.results).filter(result => result === true).length;
    const totalTests = Object.keys(this.results).length - 1; // Exclude overallPerformance

    this.results.overallPerformance = passedTests >= totalTests - 1; // Allow 1 test to have issues

    console.log(`\n📊 Performance Assessment: ${passedTests}/${totalTests} optimizations verified`);
  }

  printResults() {
    console.log('\n' + '='.repeat(60));
    console.log('⚡ DRIVER QR SYSTEM PERFORMANCE OPTIMIZATION RESULTS');
    console.log('='.repeat(60));

    const tests = [
      { name: 'GIN Index Effectiveness', key: 'ginIndexEffectiveness' },
      { name: 'Composite Index Performance', key: 'compositeIndexPerformance' },
      { name: 'Connection Pooling', key: 'connectionPooling' },
      { name: 'Concurrent Load Handling', key: 'concurrentLoadHandling' },
      { name: 'Rate Limiting Effectiveness', key: 'rateLimitingEffectiveness' }
    ];

    tests.forEach(test => {
      const status = this.results[test.key] ? '✅ OPTIMIZED' : '⚠️ NEEDS ATTENTION';
      console.log(`${status} ${test.name}`);
    });

    console.log('='.repeat(60));
    
    if (this.results.overallPerformance) {
      console.log('🚀 OVERALL RESULT: PERFORMANCE OPTIMIZATION SUCCESSFUL');
      console.log('\n✅ The Driver QR system is performing optimally:');
      console.log('   • Database indexes are effective for fast lookups');
      console.log('   • Connection pooling is working efficiently');
      console.log('   • Concurrent load handling prevents race conditions');
      console.log('   • Rate limiting protects against abuse');
    } else {
      console.log('⚠️ OVERALL RESULT: PERFORMANCE IMPROVEMENTS NEEDED');
      console.log('\nSome performance optimizations need attention. Review the results above.');
    }

    console.log('='.repeat(60));
  }

  // Additional optimization recommendations
  async generateOptimizationRecommendations() {
    console.log('\n📋 PERFORMANCE OPTIMIZATION RECOMMENDATIONS:');
    
    if (!this.results.ginIndexEffectiveness) {
      console.log('• Consider rebuilding GIN index: REINDEX INDEX idx_drivers_qr_code_gin;');
    }
    
    if (!this.results.compositeIndexPerformance) {
      console.log('• Add missing composite indexes for common query patterns');
      console.log('• Consider partial indexes for frequently filtered columns');
    }
    
    if (!this.results.connectionPooling) {
      console.log('• Optimize connection pool settings in database configuration');
      console.log('• Consider increasing max_connections if needed');
    }
    
    if (!this.results.concurrentLoadHandling) {
      console.log('• Implement proper row-level locking in critical sections');
      console.log('• Add retry logic for deadlock scenarios');
    }
    
    if (!this.results.rateLimitingEffectiveness) {
      console.log('• Implement express-rate-limit middleware on public endpoints');
      console.log('• Add IP-based throttling for driver connect endpoints');
    }
  }
}

// Run optimization if called directly
if (require.main === module) {
  const optimizer = new DriverQRPerformanceOptimizer();
  optimizer.runOptimization()
    .then(async () => {
      await optimizer.generateOptimizationRecommendations();
      process.exit(optimizer.results.overallPerformance ? 0 : 1);
    })
    .catch(error => {
      console.error('Performance optimization script failed:', error);
      process.exit(1);
    });
}

module.exports = DriverQRPerformanceOptimizer;