#!/bin/bash

# =============================================================================
# Check Log Sizes - Hauling QR Trip System
# =============================================================================
# This script shows the sizes of all log files before clearing them
#
# Usage: ./check-log-sizes.sh
# =============================================================================

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
APP_DIR="/var/www/hauling-qr-system"
DEPLOYMENT_LOG_DIR="/var/log/hauling-deployment"

echo -e "${BLUE}📊 Checking Log File Sizes - Hauling QR System${NC}"
echo -e "${BLUE}===============================================${NC}"
echo ""

# Function to check file size
check_file_size() {
    local file="$1"
    local description="$2"
    
    if [[ -f "$file" ]]; then
        local size=$(du -h "$file" 2>/dev/null | cut -f1 || echo "0")
        local lines=$(wc -l < "$file" 2>/dev/null || echo "0")
        if [[ "$size" != "0" && "$size" != "0B" ]]; then
            echo -e "${GREEN}📄 $description: ${CYAN}$size${NC} (${YELLOW}$lines lines${NC})"
        else
            echo -e "${CYAN}📄 $description: Empty${NC}"
        fi
    else
        echo -e "${RED}❌ $description: Not found${NC}"
    fi
}

# Function to check directory size
check_directory_size() {
    local dir="$1"
    local description="$2"
    local pattern="$3"
    
    if [[ -d "$dir" ]]; then
        local count=$(find "$dir" -name "$pattern" -type f 2>/dev/null | wc -l)
        if [[ $count -gt 0 ]]; then
            local total_size=$(find "$dir" -name "$pattern" -type f -exec du -ch {} + 2>/dev/null | tail -1 | cut -f1 || echo "0")
            echo -e "${GREEN}📁 $description: ${CYAN}$total_size${NC} (${YELLOW}$count files${NC})"
            
            # Show individual files if not too many
            if [[ $count -le 10 ]]; then
                find "$dir" -name "$pattern" -type f -exec du -h {} \; 2>/dev/null | sed 's/^/   /' | head -10
            fi
        else
            echo -e "${CYAN}📁 $description: No log files${NC}"
        fi
    else
        echo -e "${RED}❌ $description: Directory not found${NC}"
    fi
}

echo -e "${YELLOW}📋 PM2 Logs${NC}"

# PM2 logs
if command -v pm2 >/dev/null 2>&1; then
    PM2_HOME="${PM2_HOME:-$HOME/.pm2}"
    echo -e "${CYAN}PM2 Home: $PM2_HOME${NC}"
    
    check_directory_size "$PM2_HOME/logs" "PM2 log directory" "*.log"
    
    # Specific PM2 files
    check_file_size "$PM2_HOME/logs/hauling-qr-server-out.log" "PM2 hauling-qr-server output"
    check_file_size "$PM2_HOME/logs/hauling-qr-server-error.log" "PM2 hauling-qr-server error"
    check_file_size "$PM2_HOME/logs/hauling-qr-server.log" "PM2 hauling-qr-server combined"
    
    # Check root PM2 if accessible
    if [[ -d "/root/.pm2/logs" ]] && [[ -r "/root/.pm2/logs" ]]; then
        check_directory_size "/root/.pm2/logs" "Root PM2 logs" "*.log"
    fi
else
    echo -e "${RED}❌ PM2 not found${NC}"
fi

echo ""
echo -e "${YELLOW}📋 Application Logs${NC}"

# Application logs
check_file_size "$APP_DIR/server/logs/err.log" "Application error log"
check_file_size "$APP_DIR/server/logs/out.log" "Application output log"
check_file_size "$APP_DIR/server/logs/combined.log" "Application combined log"

check_directory_size "$APP_DIR/server/logs" "Server logs directory" "*.log"

# Other possible application logs
check_file_size "$APP_DIR/logs/app.log" "App log"
check_file_size "/home/<USER>/logs/hauling-qr-app.log" "Ubuntu user app log"

echo ""
echo -e "${YELLOW}📋 Deployment Logs${NC}"

check_directory_size "$DEPLOYMENT_LOG_DIR" "Deployment logs" "*.log"

echo ""
echo -e "${YELLOW}📋 System Service Logs${NC}"

# Nginx logs
check_file_size "/var/log/nginx/access.log" "Nginx access log"
check_file_size "/var/log/nginx/error.log" "Nginx error log"

# PostgreSQL logs
if [[ -d "/var/log/postgresql" ]]; then
    check_directory_size "/var/log/postgresql" "PostgreSQL logs" "*.log"
fi

echo ""
echo -e "${YELLOW}📋 Node.js and NPM Logs${NC}"

# NPM logs
check_directory_size "$HOME/.npm/_logs" "NPM logs" "*.log"
check_file_size "$HOME/.npm/_logs/debug.log" "NPM debug log"

# Node debug logs
check_directory_size "$APP_DIR" "App directory debug logs" "npm-debug.log*"

echo ""
echo -e "${YELLOW}📋 Total Summary${NC}"

# Calculate total sizes
TOTAL_SIZE=0
LOG_LOCATIONS=(
    "$PM2_HOME/logs"
    "$APP_DIR/server/logs"
    "$DEPLOYMENT_LOG_DIR"
    "/var/log/nginx"
    "/var/log/postgresql"
    "$HOME/.npm/_logs"
)

echo -e "${CYAN}Directory summaries:${NC}"
for dir in "${LOG_LOCATIONS[@]}"; do
    if [[ -d "$dir" ]]; then
        SIZE=$(du -sh "$dir" 2>/dev/null | cut -f1 || echo "0")
        if [[ "$SIZE" != "0" && "$SIZE" != "0B" ]]; then
            echo -e "   ${GREEN}$dir: $SIZE${NC}"
        fi
    fi
done

echo ""
echo -e "${BLUE}📝 Recent Log Activity:${NC}"

# Show recent log activity
if [[ -f "$PM2_HOME/logs/hauling-qr-server-out.log" ]]; then
    RECENT_LINES=$(tail -5 "$PM2_HOME/logs/hauling-qr-server-out.log" 2>/dev/null | wc -l)
    if [[ $RECENT_LINES -gt 0 ]]; then
        echo -e "${CYAN}Last 3 lines from PM2 output log:${NC}"
        tail -3 "$PM2_HOME/logs/hauling-qr-server-out.log" 2>/dev/null | sed 's/^/   /' || true
    fi
fi

echo ""
echo -e "${BLUE}🧹 To clear all logs:${NC}"
echo -e "   ${CYAN}sudo ./clear-all-logs.sh${NC}"
echo ""
echo -e "${BLUE}📊 To monitor logs in real-time:${NC}"
echo -e "   ${CYAN}pm2 logs hauling-qr-server -f${NC}"
echo -e "   ${CYAN}tail -f /var/log/nginx/access.log${NC}"