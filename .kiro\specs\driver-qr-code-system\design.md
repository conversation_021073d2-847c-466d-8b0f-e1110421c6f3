# Design Document

## Overview

The Driver QR Code system is a standalone time tracking solution that enables drivers to check in and out of their shifts by scanning their driver ID QR codes and truck QR codes. The system operates independently of the existing 4-phase trip workflow while integrating seamlessly with the current shift management system. The design focuses on simplicity, reliability, and real-world usability for drivers working in industrial environments.

## Architecture

### System Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Driver with   │    │  Public Driver  │    │  Admin Dashboard│
│   ID Card QR    │───▶│  Connect Page   │◀───│   Management    │
│   /driver-connect│    │  (No Login)     │    │   (Authenticated)│
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │                         │
                              ▼                         ▼
┌─────────────────────────────────────────────────────────────────┐
│                    Backend API Layer                            │
│  ┌─────────────────┐              ┌─────────────────┐          │
│  │  Public Driver  │              │  Admin Driver   │          │
│  │  API Routes     │              │  API Routes     │          │
│  │  (No Auth)      │              │  (Auth Required)│          │
│  └─────────────────┘              └─────────────────┘          │
└─────────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────────┐
│                    Database Layer                               │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐            │
│  │   drivers   │  │driver_shifts│  │ dump_trucks │            │
│  │ +qr_code    │  │ (existing)  │  │ (existing)  │            │
│  └─────────────┘  └─────────────┘  └─────────────┘            │
└─────────────────────────────────────────────────────────────────┘
```

### Component Interaction Flow

1. **Driver Scanning Flow**:
   - Driver accesses `/driver-connect` (public page)
   - Scans driver ID QR code → Authentication via drivers table
   - Scans truck QR code → Creates/ends shift in driver_shifts table
   - Real-time updates sent via WebSocket to admin dashboard

2. **Admin Management Flow**:
   - Admin accesses authenticated dashboard
   - Generates driver QR codes → Stored in drivers.driver_qr_code
   - Views attendance reports → Queries driver_shifts with duration calculations
   - Manages emergency situations → Manual shift adjustments

## Components and Interfaces

### Frontend Components

#### 1. DriverConnect.js (Public Page)
**Purpose**: Standalone driver check-in/check-out interface
**Location**: `/driver-connect` (public access, no authentication)

**Key Features**:
- Two-step scanning process (driver ID → truck QR)
- Mobile-optimized for both portrait and landscape
- Real-time feedback and confirmation screens
- Automatic handover notifications ("Taking over from Driver A")

**Component Structure**:
```javascript
const DriverConnect = () => {
  const [scanStep, setScanStep] = useState('driver'); // 'driver' or 'truck'
  const [driverData, setDriverData] = useState(null);
  const [isScanning, setIsScanning] = useState(false);
  const [currentShift, setCurrentShift] = useState(null);
  
  // QR Scanner integration with @yudiel/react-qr-scanner
  // Step-by-step UI guidance
  // Error handling with manual retry
  // Confirmation screens for handovers
};
```

#### 2. DriverAttendance.js (Admin Page)
**Purpose**: Attendance reporting and duration tracking
**Location**: `/driver-attendance` (authenticated access)

**Key Features**:
- Driver shift records with calculated durations
- Filtering by driver, date range, truck
- Daily totals, weekly summaries, monthly reports
- Export functionality for payroll processing

#### 3. DriversManagement.js (Enhanced)
**Purpose**: Enhanced existing driver management with QR code functionality (following trucks/locations pattern)
**Enhancements**:
- Import and use existing QRCodeModal component (same as trucks/locations)
- Add QR code generation button in DriversTable actions column
- Add handleViewQR function similar to trucks and locations
- Use existing driversAPI.generateQR method
- Maintain existing driver management functionality without changes

### Backend Components

#### 1. Public Driver API (server/routes/driver.js)
**Purpose**: Handle driver connect operations without authentication

**Endpoints**:
```javascript
// POST /api/driver/connect
// Process driver QR scan and truck QR scan
// No authentication required - validates via QR code data

// GET /api/driver/status/:employeeId
// Get current driver shift status
// Used for determining check-in vs check-out
```

**Key Features**:
- Global rate limiting (100 requests per minute per IP)
- Database transactions with row-level locking
- WebSocket notifications for real-time updates
- Comprehensive error handling with user-friendly messages

#### 2. Admin Driver API (server/routes/driver-admin.js)
**Purpose**: Administrative driver management (authenticated)

**Endpoints**:
```javascript
// POST /api/driver-admin/generate-qr/:driverId
// Generate QR code for driver

// GET /api/driver-admin/attendance
// Get attendance records with filtering

// POST /api/driver-admin/manual-checkout/:shiftId
// Emergency manual checkout for supervisors
```

#### 3. DriverQRService.js
**Purpose**: Business logic for driver QR operations

**Key Methods**:
```javascript
class DriverQRService {
  // Generate driver QR code with proper structure
  generateDriverQR(driverId, employeeId)
  
  // Validate driver QR code and check status
  validateDriverQR(qrData)
  
  // Handle driver-truck connection logic
  processDriverTruckConnection(driverId, truckId)
  
  // Automatic shift handover between drivers
  handleShiftHandover(currentDriverId, newDriverId, truckId)
}
```

#### 4. DriverAttendanceService.js
**Purpose**: Attendance calculation and reporting

**Key Methods**:
```javascript
class DriverAttendanceService {
  // Calculate simple duration (end_time - start_time)
  calculateShiftDuration(startDate, startTime, endDate, endTime)
  
  // Get attendance records with filtering
  getAttendanceRecords(filters)
  
  // Generate daily/weekly/monthly summaries
  generateAttendanceSummary(period, driverId)
}
```

## Data Models

### Driver QR Code Structure
**Storage**: `drivers.driver_qr_code` (JSONB field)

```json
{
  "id": "DR-001",
  "driver_id": 123,
  "employee_id": "DR-001",
  "generated_date": "2025-01-01T00:00:00Z"
}
```

### Database Schema Changes

#### Migration: 018_driver_qr_code_system.sql
```sql
-- Add driver QR code field to drivers table
ALTER TABLE drivers 
ADD COLUMN driver_qr_code JSONB;

-- Add GIN index for fast QR code lookups
CREATE INDEX idx_drivers_qr_code_gin 
ON drivers USING gin (driver_qr_code) 
WHERE (driver_qr_code IS NOT NULL);

-- Add composite index for driver_shifts queries
CREATE INDEX idx_driver_shifts_driver_status 
ON driver_shifts (driver_id, status);

-- Add unique constraint to prevent multiple active shifts per driver
ALTER TABLE driver_shifts 
ADD CONSTRAINT unique_active_driver_shift 
UNIQUE (driver_id) 
WHERE status = 'active';
```

### Shift Management Integration

**Existing driver_shifts table usage**:
- **truck_id**: Links to dump_trucks.id
- **driver_id**: Links to drivers.id  
- **start_date/start_time**: Check-in timestamp
- **end_date/end_time**: Check-out timestamp
- **status**: 'active' during shift, 'completed' after checkout
- **shift_type**: Always 'custom' for driver QR created shifts

**Automatic shift creation flow**:
1. Driver scans truck QR → Check for existing active shift
2. If no active shift → Create new shift with status 'active'
3. If active shift exists → End current shift, start new shift
4. Duration calculated as: `(end_date + end_time) - (start_date + start_time)`

## Error Handling

### Frontend Error Handling
- **Network failures**: Display error message, require manual retry
- **Invalid QR codes**: Clear error messages explaining validation failure
- **Camera issues**: Fallback options and troubleshooting guidance
- **Concurrent access**: Show handover confirmation screens

### Backend Error Handling
- **Database deadlocks**: Automatic retry with exponential backoff
- **Constraint violations**: Graceful handling with user-friendly messages
- **Rate limiting**: HTTP 429 responses with retry-after headers
- **Invalid QR data**: Detailed validation error responses

### Error Response Format
```json
{
  "success": false,
  "error": "DRIVER_INACTIVE",
  "message": "Driver account is inactive. Please contact your supervisor.",
  "details": {
    "driver_id": "DR-001",
    "status": "inactive"
  }
}
```

## Testing Strategy

### Unit Testing
- **QR Code Generation**: Validate structure and uniqueness
- **Duration Calculations**: Test various time scenarios including overnight shifts
- **Validation Logic**: Test active/inactive driver and truck scenarios
- **Concurrent Access**: Test race conditions and deadlock handling

### Integration Testing
- **End-to-End Scanning Flow**: Driver ID → Truck QR → Shift creation
- **Handover Scenarios**: Multiple drivers on same truck
- **WebSocket Updates**: Real-time dashboard notifications
- **API Rate Limiting**: Verify global rate limiting functionality

### Mobile Testing
- **Device Compatibility**: Test on various mobile browsers
- **Orientation Changes**: Portrait and landscape mode functionality
- **Camera Performance**: QR scanning in various lighting conditions
- **Touch Interface**: Ensure 44px minimum touch targets

### Performance Testing
- **Database Query Performance**: Verify index effectiveness
- **Concurrent User Load**: Test multiple simultaneous scans
- **WebSocket Scalability**: Real-time update performance
- **Mobile Network Conditions**: Test on 3G/4G connections

## Security Considerations

### Public Endpoint Security
- **Global Rate Limiting**: 100 requests per minute per IP address
- **Input Validation**: Strict QR code format validation
- **SQL Injection Prevention**: Parameterized queries only
- **Audit Logging**: Log all driver connect/disconnect events

### QR Code Security
- **Tamper Detection**: Validate QR code structure integrity
- **Driver Status Validation**: Always verify active status
- **Truck Status Validation**: Ensure truck is available for assignment
- **No Sensitive Data**: QR codes contain only necessary identifiers

### Data Privacy
- **Minimal Data Exposure**: Public API returns only necessary information
- **Audit Trails**: Log all operations with timestamps and IP addresses
- **Emergency Access**: Supervisor override capabilities for emergencies
- **Data Retention**: Follow company policies for shift record retention

## Files to be Modified and Created

### Database Changes
**NEW FILES:**
- `database/migrations/018_driver_qr_code_system.sql` - Add driver_qr_code field and indexes

**MODIFIED FILES:**
- `database/init.sql` - Updated by migration script

### Backend Changes
**NEW FILES:**
- `server/routes/driver.js` - Public driver connect API endpoints
- `server/routes/driver-admin.js` - Admin driver management API endpoints  
- `server/services/DriverQRService.js` - Driver QR code business logic
- `server/services/DriverAttendanceService.js` - Attendance calculation service
- `server/utils/DriverQRCodeGenerator.js` - QR code generation utility

**MODIFIED FILES:**
- `server/server.js` - Add new route registrations
- `server/websocket.js` - Add driver connect/disconnect event broadcasting

### Frontend Changes
**NEW FILES:**
- `client/src/pages/drivers/DriverConnect.js` - Standalone driver connect page
- `client/src/pages/drivers/DriverAttendance.js` - Attendance reporting page
- `client/src/components/drivers/DriverQRScanner.js` - Reusable QR scanner component
- `client/src/components/drivers/ShiftHandoverModal.js` - Handover confirmation modal
- `client/src/services/driverAPI.js` - Driver API service functions

**MODIFIED FILES:**
- `client/src/components/AppRoutes.js` - Add public /driver-connect route (for standalone page only)
- `client/src/pages/drivers/DriversManagement.js` - Enhanced with QR code generation (following trucks/locations pattern)
- `client/src/pages/drivers/components/DriversTable.js` - Add QR code button in actions column  
- `client/src/services/api.js` - Add driversAPI.generateQR method
- `client/src/hooks/usePermissions.js` - Add driver_connect and driver_attendance permissions (if needed)
- `server/server.js` - Register driver-admin routes

### Configuration Changes
**MODIFIED FILES:**
- `database/migrations/017_role_based_access_control.sql` - Add driver permissions for admin roles

### Integration Points
**ENHANCED FILES:**
- `server/utils/AutoAssignmentCreator.js` - Enhanced with automatic shift data
- Existing shift management system - Enhanced with automatic shift creation

### No Changes Required
**UNCHANGED FILES:**
- `client/src/pages/scanner/QRScanner.js` - Existing trip scanner (untouched)
- `server/routes/scanner.js` - Existing trip scanning API (untouched)
- Database `trip_logs` table - Driver system only affects `driver_shifts` table
- Database `assignments` table - Structure unchanged, benefits from better shift data

This design provides a comprehensive, scalable solution that integrates seamlessly with the existing system while maintaining simplicity and reliability for daily driver operations.