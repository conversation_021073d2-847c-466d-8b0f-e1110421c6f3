import { useState, useEffect, useCallback } from 'react';

export const useDeviceDetection = () => {
  const [isMobile, setIsMobile] = useState(false);
  const [isLandscape, setIsLandscape] = useState(false);

  const checkMobile = useCallback(() => {
    const userAgent = navigator.userAgent || window.opera || '';
    const isMobileDevice = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent.toLowerCase());
    setIsMobile(isMobileDevice);
  }, []);

  const checkOrientation = useCallback(() => {
    setIsLandscape(window.innerWidth > window.innerHeight);
  }, []);

  const getCameraDimensions = useCallback(() => {
    if (isMobile) {
      return isLandscape ? '300px' : '400px';
    }
    return '500px';
  }, [isMobile, isLandscape]);

  useEffect(() => {
    checkMobile();
    checkOrientation();

    window.addEventListener('resize', checkOrientation);
    window.addEventListener('orientationchange', checkOrientation);

    return () => {
      window.removeEventListener('resize', checkOrientation);
      window.removeEventListener('orientationchange', checkOrientation);
    };
  }, [checkMobile, checkOrientation]);

  return {
    isMobile,
    isLandscape,
    getCameraDimensions
  };
};