{"enabled": true, "name": "Code Quality Analyzer", "description": "Analyzes code changes and suggests improvements for better readability, maintainability, and performance", "version": "1", "when": {"type": "fileEdited", "patterns": ["**/*.js", "**/*.jsx", "**/*.ts", "**/*.tsx", "**/*.sql"]}, "then": {"type": "askAgent", "prompt": "Analyze the following code changes and provide specific improvement suggestions:\n\n1. Code smells: Identify any potential code smells like duplicate code, long methods, complex conditionals, or excessive comments.\n\n2. Design patterns: Suggest appropriate design patterns that could improve the code structure.\n\n3. Best practices: Recommend adherence to JavaScript/TypeScript/SQL best practices based on the file type.\n\n4. Performance optimizations: Identify areas where performance could be improved.\n\n5. Readability improvements: Suggest ways to make the code more readable and maintainable.\n\n6. Error handling: Check for proper error handling and suggest improvements.\n\n7. Security concerns: Identify any potential security issues.\n\nFor each suggestion, provide:\n- A clear explanation of the issue\n- A code example showing how to implement the improvement\n- The benefit of making the change\n\nFocus on practical, actionable improvements while maintaining the existing functionality. Prioritize suggestions based on their impact on code quality."}}