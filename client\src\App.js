import React, { useEffect } from 'react';
import { <PERSON>rowser<PERSON>outer as Router } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import AppRoutes from './components/AppRoutes';
import { AuthProvider } from './context/AuthContext';
import appearanceService from './services/appearanceService';
import './styles/appearance.css';

function App() {
  // Initialize appearance settings on app start
  useEffect(() => {
    appearanceService.initialize();
  }, []);

  return (
    <AuthProvider>
      <Router future={{ v7_startTransition: true, v7_relativeSplatPath: true }}>
        <div className="App min-h-screen bg-secondary-50">
          <AppRoutes />
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: '#fff',
                color: '#1f2937',
                boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
              },
              success: {
                iconTheme: {
                  primary: '#22c55e',
                  secondary: '#fff',
                },
              },
              error: {
                iconTheme: {
                  primary: '#ef4444',
                  secondary: '#fff',
                },
              },
            }}
          />
        </div>
      </Router>
    </AuthProvider>
  );
}

export default App;