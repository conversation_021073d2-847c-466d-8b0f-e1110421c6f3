import React, { useState, useEffect, useCallback } from 'react';
import { analyticsService } from '../../services/analytics';
import toast from 'react-hot-toast';

const AnalyticsDashboard = () => {
  const [analytics, setAnalytics] = useState(null);
  const [performance, setPerformance] = useState(null);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('7d');
  const [activeTab, setActiveTab] = useState('overview');

  const loadAnalytics = useCallback(async () => {
    setLoading(true);
    try {
      const [tripAnalytics, performanceAnalytics] = await Promise.all([
        analyticsService.getTripAnalytics(timeRange),
        analyticsService.getPerformanceAnalytics('24h')
      ]);

      setAnalytics(tripAnalytics);
      setPerformance(performanceAnalytics);
    } catch (error) {
      toast.error('Failed to load analytics data');
      console.error('Analytics loading error:', error);
    } finally {
      setLoading(false);
    }
  }, [timeRange]);

  useEffect(() => {
    loadAnalytics();
  }, [loadAnalytics]);

  const exportData = async (format) => {
    try {
      await analyticsService.exportAnalytics(format, timeRange);
      toast.success(`Analytics exported as ${format.toUpperCase()}`);
    } catch (error) {
      toast.error('Failed to export analytics');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  const MetricCard = ({ title, value, subtitle, icon, color = 'blue' }) => (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center">
        <div className={`flex-shrink-0 p-3 rounded-md bg-${color}-100`}>
          <span className="text-2xl">{icon}</span>
        </div>
        <div className="ml-4 flex-1">
          <div className="text-sm font-medium text-gray-500">{title}</div>
          <div className="text-2xl font-bold text-gray-900">{value}</div>
          {subtitle && (
            <div className="text-sm text-gray-600">{subtitle}</div>
          )}
        </div>
      </div>
    </div>
  );

  const TabButton = ({ id, label, isActive, onClick }) => (
    <button
      onClick={() => onClick(id)}
      className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
        isActive
          ? 'bg-primary-100 text-primary-700 border border-primary-200'
          : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
      }`}
    >
      {label}
    </button>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">📊 Analytics Dashboard</h1>
          <p className="mt-1 text-sm text-gray-600">
            Enhanced analytics for the Hauling QR Trip System
          </p>
        </div>
        
        <div className="mt-4 sm:mt-0 flex items-center space-x-3">
          {/* Time Range Selector */}
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="rounded-md border-gray-300 text-sm"
          >
            <option value="24h">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
            <option value="90d">Last 90 Days</option>
          </select>
          
          {/* Export Buttons */}
          <div className="flex space-x-2">
            <button
              onClick={() => exportData('csv')}
              className="btn btn-secondary text-xs"
            >
              📄 CSV
            </button>
            <button
              onClick={() => exportData('json')}
              className="btn btn-secondary text-xs"
            >
              📋 JSON
            </button>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="flex space-x-2 border-b border-gray-200">
        <TabButton
          id="overview"
          label="📈 Overview"
          isActive={activeTab === 'overview'}
          onClick={setActiveTab}
        />
        <TabButton
          id="return-travel"
          label="🔄 Return Travel"
          isActive={activeTab === 'return-travel'}
          onClick={setActiveTab}
        />
        <TabButton
          id="uncertainty"
          label="❓ Uncertainty"
          isActive={activeTab === 'uncertainty'}
          onClick={setActiveTab}
        />
        <TabButton
          id="performance"
          label="⚡ Performance"
          isActive={activeTab === 'performance'}
          onClick={setActiveTab}
        />
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && analytics && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <MetricCard
              title="Total Trips"
              value={analytics.totalTrips || 0}
              subtitle={`${analytics.workflowEfficiency?.completionRate || 0}% completed`}
              icon="🚚"
              color="blue"
            />
            <MetricCard
              title="Avg Trip Time"
              value={`${analytics.workflowEfficiency?.averageTotalTime || 0}m`}
              subtitle="End-to-end duration"
              icon="⏱️"
              color="green"
            />
            <MetricCard
              title="Return Travel"
              value={`${analytics.returnTravelMetrics?.percentageWithReturnTravel || 0}%`}
              subtitle="Enhanced workflow usage"
              icon="🔄"
              color="purple"
            />
            <MetricCard
              title="Dynamic Routes"
              value={`${analytics.uncertaintyMetrics?.percentageDynamic || 0}%`}
              subtitle="Auto-created assignments"
              icon="🗺️"
              color="orange"
            />
          </div>

          {/* Workflow Efficiency */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">🔧 Workflow Efficiency</h3>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {analytics.workflowEfficiency?.averageLoadingTime || 0}m
                </div>
                <div className="text-sm text-gray-500">Avg Loading</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {analytics.workflowEfficiency?.averageTravelTime || 0}m
                </div>
                <div className="text-sm text-gray-500">Avg Travel</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">
                  {analytics.workflowEfficiency?.averageUnloadingTime || 0}m
                </div>
                <div className="text-sm text-gray-500">Avg Unloading</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {analytics.workflowEfficiency?.averageTotalTime || 0}m
                </div>
                <div className="text-sm text-gray-500">Total Time</div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Return Travel Tab */}
      {activeTab === 'return-travel' && analytics?.returnTravelMetrics && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <MetricCard
              title="Trips with Return Travel"
              value={analytics.returnTravelMetrics.totalTripsWithReturnTravel}
              subtitle={`${analytics.returnTravelMetrics.percentageWithReturnTravel}% of completed trips`}
              icon="🔄"
              color="blue"
            />
            <MetricCard
              title="Avg Return Time"
              value={`${analytics.returnTravelMetrics.averageReturnTravelTime}m`}
              subtitle={`Range: ${analytics.returnTravelMetrics.minReturnTravelTime}-${analytics.returnTravelMetrics.maxReturnTravelTime}m`}
              icon="⏱️"
              color="green"
            />
            <MetricCard
              title="Total Return Time"
              value={`${analytics.returnTravelMetrics.totalReturnTravelTime}m`}
              subtitle="Cumulative return travel"
              icon="📊"
              color="purple"
            />
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">🎯 Return Travel Insights</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg">
                <div>
                  <div className="font-medium text-blue-900">Enhanced Workflow Adoption</div>
                  <div className="text-sm text-blue-700">
                    {analytics.returnTravelMetrics.percentageWithReturnTravel}% of trips use the enhanced workflow with return travel tracking
                  </div>
                </div>
                <div className="text-3xl">📈</div>
              </div>
              
              <div className="flex items-center justify-between p-4 bg-green-50 rounded-lg">
                <div>
                  <div className="font-medium text-green-900">Return Travel Efficiency</div>
                  <div className="text-sm text-green-700">
                    Average return travel time of {analytics.returnTravelMetrics.averageReturnTravelTime} minutes shows efficient route planning
                  </div>
                </div>
                <div className="text-3xl">⚡</div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Uncertainty Tab */}
      {activeTab === 'uncertainty' && analytics?.uncertaintyMetrics && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <MetricCard
              title="Dynamic Trips"
              value={analytics.uncertaintyMetrics.totalDynamicTrips}
              subtitle={`${analytics.uncertaintyMetrics.percentageDynamic}% of all trips`}
              icon="🗺️"
              color="orange"
            />
            <MetricCard
              title="Confirmed Locations"
              value={`${analytics.uncertaintyMetrics.confirmedLocationRate}%`}
              subtitle="Location certainty rate"
              icon="📍"
              color="green"
            />
            <MetricCard
              title="Uncertainty Rate"
              value={`${analytics.uncertaintyMetrics.uncertaintyRate}%`}
              subtitle="Predicted/unconfirmed locations"
              icon="❓"
              color="yellow"
            />
          </div>
        </div>
      )}

      {/* Performance Tab */}
      {activeTab === 'performance' && performance && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <MetricCard
              title="Total Scans"
              value={performance.scanPerformance?.totalScans || 0}
              subtitle="Last 24 hours"
              icon="📱"
              color="blue"
            />
            <MetricCard
              title="Avg Processing"
              value={`${performance.scanPerformance?.averageProcessingTime || 0}ms`}
              subtitle="Scan processing time"
              icon="⚡"
              color="green"
            />
            <MetricCard
              title="Performance Score"
              value={`${performance.scanPerformance?.performanceScore || 0}%`}
              subtitle="System performance"
              icon="🎯"
              color="purple"
            />
            <MetricCard
              title="Health Score"
              value={`${performance.systemHealth?.healthScore || 0}%`}
              subtitle="System health"
              icon="💚"
              color="green"
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default AnalyticsDashboard;
