const winston = require('winston');
const DailyRotateFile = require('winston-daily-rotate-file');
const path = require('path');
const crypto = require('crypto');

// Import configuration
const { getServerConfig } = require('../config/unified-config');

// Log deduplication system
class LogDeduplicator {
  constructor() {
    this.messageCache = new Map();
    this.cleanupInterval = setInterval(() => this.cleanup(), 60000); // Cleanup every minute
  }

  /**
   * Check if a message should be logged based on deduplication rules
   * @param {string} message Log message
   * @param {string} context Log context
   * @param {Object} config Deduplication configuration
   * @returns {boolean} Whether the message should be logged
   */
  shouldLog(message, context, config = {}) {
    const { windowMs = 300000, maxDuplicates = 3 } = config;
    const messageHash = this.createMessageHash(message, context);
    const now = Date.now();

    if (!this.messageCache.has(messageHash)) {
      this.messageCache.set(messageHash, {
        count: 1,
        firstSeen: now,
        lastSeen: now
      });
      return true;
    }

    const entry = this.messageCache.get(messageHash);

    // Reset count if outside window
    if (now - entry.firstSeen > windowMs) {
      entry.count = 1;
      entry.firstSeen = now;
      entry.lastSeen = now;
      return true;
    }

    // Check if we've exceeded max duplicates
    if (entry.count >= maxDuplicates) {
      entry.lastSeen = now;
      return false;
    }

    entry.count++;
    entry.lastSeen = now;
    return true;
  }

  /**
   * Create a hash for message deduplication
   * @param {string} message Log message
   * @param {string} context Log context
   * @returns {string} Message hash
   */
  createMessageHash(message, context) {
    const content = `${context}:${message}`;
    return crypto.createHash('md5').update(content).digest('hex');
  }

  /**
   * Clean up old entries from cache
   */
  cleanup() {
    const now = Date.now();
    const maxAge = 600000; // 10 minutes

    for (const [hash, entry] of this.messageCache.entries()) {
      if (now - entry.lastSeen > maxAge) {
        this.messageCache.delete(hash);
      }
    }
  }

  /**
   * Get deduplication statistics
   * @returns {Object} Statistics
   */
  getStats() {
    return {
      cachedMessages: this.messageCache.size,
      oldestEntry: Math.min(...Array.from(this.messageCache.values()).map(e => e.firstSeen)),
      newestEntry: Math.max(...Array.from(this.messageCache.values()).map(e => e.lastSeen))
    };
  }
}

// Global deduplicator instance
const logDeduplicator = new LogDeduplicator();

// Performance-optimized async logging buffer
class AsyncLogBuffer {
  constructor() {
    this.buffer = [];
    this.maxBufferSize = 100;
    this.flushInterval = 1000; // 1 second
    this.isProcessing = false;
    this.flushTimer = null;
    this.stats = {
      bufferedLogs: 0,
      flushedLogs: 0,
      flushOperations: 0,
      averageFlushTime: 0
    };

    this.startFlushTimer();
  }

  /**
   * Add log entry to buffer for async processing
   * @param {Function} logFunction Winston log function
   * @param {Array} args Log arguments
   */
  addToBuffer(logFunction, args) {
    this.buffer.push({ logFunction, args, timestamp: Date.now() });
    this.stats.bufferedLogs++;

    // Force flush if buffer is full
    if (this.buffer.length >= this.maxBufferSize) {
      this.flush();
    }
  }

  /**
   * Flush buffer to winston logger
   */
  async flush() {
    if (this.isProcessing || this.buffer.length === 0) {
      return;
    }

    this.isProcessing = true;
    const flushStart = Date.now();
    const logsToFlush = [...this.buffer];
    this.buffer = [];

    try {
      // Process logs asynchronously
      await Promise.all(logsToFlush.map(async ({ logFunction, args }) => {
        try {
          logFunction.apply(null, args);
        } catch (error) {
          console.error('Log buffer flush error:', error);
        }
      }));

      // Update statistics
      this.stats.flushedLogs += logsToFlush.length;
      this.stats.flushOperations++;
      const flushTime = Date.now() - flushStart;
      this.stats.averageFlushTime = (this.stats.averageFlushTime * (this.stats.flushOperations - 1) + flushTime) / this.stats.flushOperations;

    } catch (error) {
      console.error('Critical log buffer error:', error);
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Start periodic flush timer
   */
  startFlushTimer() {
    this.flushTimer = setInterval(() => {
      this.flush();
    }, this.flushInterval);
  }

  /**
   * Stop flush timer and flush remaining logs
   */
  async stop() {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
      this.flushTimer = null;
    }
    await this.flush();
  }

  /**
   * Get buffer statistics
   * @returns {Object} Buffer statistics
   */
  getStats() {
    return {
      ...this.stats,
      currentBufferSize: this.buffer.length,
      isProcessing: this.isProcessing
    };
  }
}

// Global async log buffer (only in production for performance)
const serverConfig = getServerConfig();
const asyncLogBuffer = serverConfig.IS_PRODUCTION ? new AsyncLogBuffer() : null;

// Custom log format for enhanced readability
const logFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss.SSS' }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf(({ timestamp, level, message, service, context, data, error, stack, ...meta }) => {
    const logEntry = {
      timestamp,
      level: level.toUpperCase(),
      service: service || 'HAULING-SYSTEM',
      context: context || 'GENERAL',
      message,
      ...(data && { data }),
      ...(error && { error }),
      ...(stack && { stack }),
      ...meta
    };
    return JSON.stringify(logEntry, null, 2);
  })
);

// Get configuration
const config = getServerConfig();
const logConfig = config.LOG_CONFIG;

// Enhanced winston logger with environment-specific configuration
const createLogger = () => {
  const transports = [];

  // Console transport (always enabled but level varies by environment)
  if (logConfig.transports.console.enabled) {
    transports.push(new winston.transports.Console({
      level: logConfig.transports.console.level,
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    }));
  }

  // Error file transport with daily rotation
  if (logConfig.transports.error.enabled) {
    transports.push(new DailyRotateFile({
      filename: path.join(__dirname, '../logs/error-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      level: logConfig.transports.error.level,
      maxSize: logConfig.rotation.maxSize,
      maxFiles: Math.floor(logConfig.rotation.maxFiles / 2), // Half for errors
      zippedArchive: logConfig.rotation.compress,
      auditFile: path.join(__dirname, '../logs/error-audit.json')
    }));
  }

  // Combined file transport with enhanced daily rotation
  if (logConfig.transports.file.enabled) {
    transports.push(new DailyRotateFile({
      filename: path.join(__dirname, '../logs/combined-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      level: logConfig.transports.file.level,
      maxSize: logConfig.rotation.maxSize,
      maxFiles: logConfig.rotation.maxFiles,
      zippedArchive: logConfig.rotation.compress,
      auditFile: path.join(__dirname, '../logs/combined-audit.json')
    }));
  }

  // Monitoring-specific transport with daily rotation (separate file for monitoring logs)
  if (logConfig.monitoring.enabled) {
    transports.push(new DailyRotateFile({
      filename: path.join(__dirname, '../logs/monitoring-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      level: logConfig.monitoring.level,
      maxSize: Math.floor(logConfig.rotation.maxSize / 2), // Smaller for monitoring
      maxFiles: 5,
      zippedArchive: logConfig.rotation.compress,
      auditFile: path.join(__dirname, '../logs/monitoring-audit.json'),
      // Only log monitoring-related contexts
      format: winston.format((info) => {
        const isMonitoringLog = info.context && (
          info.context.includes('SYNC_MONITOR') ||
          info.context.includes('STATUS_SYNC') ||
          info.context.includes('SHIFT_SYNC') ||
          info.context.includes('MONITORING')
        );
        return isMonitoringLog ? info : false;
      })()
    }));
  }

  return winston.createLogger({
    level: logConfig.level,
    format: logFormat,
    defaultMeta: { service: 'hauling-qr-system' },
    transports
  });
};

// Create logger instance
const logger = createLogger();

// Enhanced logging functions with context and structured data
class EnhancedLogger {
  static logScanRequest(context, scanData, userInfo) {
    logger.info('Processing scan request', {
      context: `SCANNER.${context}`,
      data: {
        scan_type: scanData.scan_type,
        user_id: userInfo.id,
        user_role: userInfo.role,
        ip_address: scanData.ip_address,
        user_agent: scanData.user_agent,
        session_id: userInfo.session_id,
        request_id: scanData.request_id || this.generateRequestId()
      },
      performance: {
        request_start: Date.now()
      }
    });
  }

  static logDatabaseQuery(queryText, params, duration, rowCount, context = 'DB_QUERY') {
    const isSlowQuery = duration > 1000; // Mark queries over 1 second as slow
    const level = isSlowQuery ? 'warn' : 'info';
    
    logger.log(level, `Database query executed`, {
      context: `DATABASE.${context}`,
      data: {
        query_type: this.extractQueryType(queryText),
        duration_ms: duration,
        row_count: rowCount,
        is_slow_query: isSlowQuery,
        params_count: params ? params.length : 0
      },
      query: {
        text: queryText.replace(/\s+/g, ' ').trim(),
        ...(params && params.length > 0 && { parameters: params })
      },
      performance: {
        execution_time_ms: duration,
        rows_per_ms: rowCount / duration
      }
    });
  }

  static logDatabaseError(error, queryText, params, context = 'DB_ERROR') {
    logger.error('Database query failed', {
      context: `DATABASE.${context}`,
      error: {
        message: error.message,
        code: error.code,
        constraint: error.constraint,
        table: error.table,
        column: error.column,
        severity: error.severity
      },
      query: {
        text: queryText ? queryText.replace(/\s+/g, ' ').trim() : 'Unknown',
        ...(params && params.length > 0 && { parameters: params })
      },
      stack: error.stack,
      troubleshooting: this.getDatabaseErrorSuggestions(error)
    });
  }

  static logScanError(context, error, scanData, userInfo) {
    logger.error('Scan processing failed', {
      context: `SCANNER.${context}`,
      error: {
        message: error.message,
        type: error.constructor.name,
        code: error.code
      },
      data: {
        scan_type: scanData.scan_type,
        user_id: userInfo?.id,
        ip_address: scanData.ip_address,
        qr_data_valid: this.isValidJSON(scanData.scanned_data)
      },
      stack: error.stack,
      troubleshooting: this.getScanErrorSuggestions(error, scanData)
    });
  }

  static logAssignmentLookup(truckId, searchCriteria, results, context = 'ASSIGNMENT_LOOKUP') {
    logger.info('Assignment lookup performed', {
      context: `SCANNER.${context}`,
      data: {
        truck_id: truckId,
        search_criteria: searchCriteria,
        results_found: results.length,
        assignments: results.map(r => ({
          id: r.id,
          status: r.status,
          assigned_date: r.assigned_date,
          loading_location: r.loading_location_name,
          unloading_location: r.unloading_location_name
        }))
      },
      troubleshooting: results.length === 0 ? this.getNoAssignmentSuggestions(truckId) : null
    });
  }

  static logRouteDeviation(truckId, expectedLocation, actualLocation, severity = 'medium') {
    logger.warn('Route deviation detected', {
      context: 'SCANNER.ROUTE_DEVIATION',
      data: {
        truck_id: truckId,
        expected_location: {
          id: expectedLocation.id,
          name: expectedLocation.name,
          type: expectedLocation.type
        },
        actual_location: {
          id: actualLocation.id,
          name: actualLocation.name,
          type: actualLocation.type
        },
        severity,
        deviation_type: this.getDeviationType(expectedLocation, actualLocation)
      },
      impact: {
        requires_approval: true,
        affects_schedule: severity !== 'low',
        cost_implications: severity === 'high' || severity === 'critical'
      }
    });
  }

  static logPerformanceMetrics(context, metrics) {
    logger.info('Performance metrics captured', {
      context: `PERFORMANCE.${context}`,
      data: metrics,
      analysis: {
        is_performance_issue: metrics.processing_time_ms > 5000,
        bottleneck_detected: this.identifyBottleneck(metrics)
      }
    });
  }

  static logConnectionPoolStatus(poolStats) {
    const isHealthy = poolStats.idleCount > 0 && poolStats.totalCount < poolStats.max;
    const level = isHealthy ? 'info' : 'warn';
    
    logger.log(level, 'Database connection pool status', {
      context: 'DATABASE.POOL',
      data: {
        total_connections: poolStats.totalCount,
        idle_connections: poolStats.idleCount,
        waiting_clients: poolStats.waitingCount,
        max_connections: poolStats.max,
        pool_utilization_percent: Math.round((poolStats.totalCount / poolStats.max) * 100)
      },
      health: {
        is_healthy: isHealthy,
        warnings: this.getPoolWarnings(poolStats)
      }
    });
  }

  // Helper methods
  static generateRequestId() {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  static extractQueryType(queryText) {
    if (!queryText) return 'UNKNOWN';
    const trimmed = queryText.trim().toUpperCase();
    if (trimmed.startsWith('SELECT')) return 'SELECT';
    if (trimmed.startsWith('INSERT')) return 'INSERT';
    if (trimmed.startsWith('UPDATE')) return 'UPDATE';
    if (trimmed.startsWith('DELETE')) return 'DELETE';
    if (trimmed.startsWith('WITH')) return 'CTE';
    return 'OTHER';
  }

  static isValidJSON(str) {
    try {
      JSON.parse(str);
      return true;
    } catch (e) {
      return false;
    }
  }

  static getDatabaseErrorSuggestions(error) {
    const suggestions = [];
    
    if (error.message.includes('connection timeout')) {
      suggestions.push('Check database connection pool configuration');
      suggestions.push('Verify database server is responding');
      suggestions.push('Consider increasing connection timeout');
    }
    
    if (error.message.includes('column') && error.message.includes('ambiguous')) {
      suggestions.push('Use table aliases to resolve column ambiguity');
      suggestions.push('Specify table names explicitly in SELECT statements');
    }
    
    if (error.code === '23505') { // Unique violation
      suggestions.push('Check for duplicate entries before insertion');
      suggestions.push('Use ON CONFLICT clauses for upsert operations');
    }
    
    return suggestions;
  }

  static getScanErrorSuggestions(error, scanData) {
    const suggestions = [];
    
    if (error.message.includes('No active assignment')) {
      suggestions.push('Verify truck has assignment for current date');
      suggestions.push('Check assignment status is "assigned" or "in_progress"');
      suggestions.push('Ensure truck status is "active"');
    }
    
    if (error.message.includes('QR code')) {
      suggestions.push('Verify QR code format and structure');
      suggestions.push('Check QR code matches expected type');
      suggestions.push('Ensure QR code data is valid JSON');
    }
    
    return suggestions;
  }

  static getNoAssignmentSuggestions(truckId) {
    return [
      `Create an assignment for truck ${truckId} for today`,
      'Verify truck status is active in database',
      'Check if assignment exists with different status',
      'Ensure assignment date matches current date'
    ];
  }

  static getDeviationType(expected, actual) {
    if (expected.type !== actual.type) {
      return 'LOCATION_TYPE_MISMATCH';
    }
    if (expected.id !== actual.id) {
      return 'LOCATION_CHANGE';
    }
    return 'UNKNOWN';
  }

  static identifyBottleneck(metrics) {
    if (metrics.database_time_ms > metrics.processing_time_ms * 0.7) {
      return 'DATABASE';
    }
    if (metrics.validation_time_ms > metrics.processing_time_ms * 0.3) {
      return 'VALIDATION';
    }
    if (metrics.network_time_ms > metrics.processing_time_ms * 0.2) {
      return 'NETWORK';
    }
    return 'NONE';
  }

  static getPoolWarnings(poolStats) {
    const warnings = [];
    
    if (poolStats.totalCount >= poolStats.max * 0.9) {
      warnings.push('Pool utilization is high (>90%)');
    }
    
    if (poolStats.waitingCount > 0) {
      warnings.push(`${poolStats.waitingCount} clients waiting for connections`);
    }
    
    if (poolStats.idleCount === 0) {
      warnings.push('No idle connections available');
    }
    
    return warnings;
  }
}

// Create logs directory if it doesn't exist
const fs = require('fs');
const logsDir = path.join(__dirname, '../logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Enhanced convenience functions with deduplication and async logging
const logError = (context, error, data = {}) => {
  const message = error.message || error;
  const logArgs = [message, {
    context,
    error: {
      message,
      stack: error.stack,
      name: error.name
    },
    data
  }];

  // Always log errors immediately (no buffering for critical issues)
  logger.error(...logArgs);
};

const logInfo = (context, message, data = {}) => {
  // Apply deduplication for monitoring contexts
  const isMonitoringContext = context && (
    context.includes('SYNC_MONITOR') ||
    context.includes('STATUS_SYNC') ||
    context.includes('SHIFT_SYNC') ||
    context.includes('MONITORING')
  );

  if (isMonitoringContext && logConfig.monitoring.deduplication.enabled) {
    if (!logDeduplicator.shouldLog(message, context, logConfig.monitoring.deduplication)) {
      return; // Skip duplicate log
    }
  }

  const logArgs = [message, { context, data }];

  // Use async buffer in production for non-critical logs
  if (asyncLogBuffer && !isMonitoringContext) {
    asyncLogBuffer.addToBuffer(logger.info.bind(logger), logArgs);
  } else {
    logger.info(...logArgs);
  }
};

const logWarn = (context, message, data = {}) => {
  // Apply deduplication for warning messages
  if (logConfig.monitoring.deduplication.enabled) {
    if (!logDeduplicator.shouldLog(message, context, logConfig.monitoring.deduplication)) {
      return; // Skip duplicate warning
    }
  }

  const logArgs = [message, { context, data }];

  // Use async buffer for warnings in production
  if (asyncLogBuffer) {
    asyncLogBuffer.addToBuffer(logger.warn.bind(logger), logArgs);
  } else {
    logger.warn(...logArgs);
  }
};

const logDebug = (context, message, data = {}) => {
  // Only log debug in development or when explicitly enabled
  if (config.IS_DEVELOPMENT || logConfig.level === 'debug') {
    const logArgs = [message, { context, data }];

    // Debug logs can be buffered
    if (asyncLogBuffer) {
      asyncLogBuffer.addToBuffer(logger.debug.bind(logger), logArgs);
    } else {
      logger.debug(...logArgs);
    }
  }
};

// Graceful shutdown handler for async log buffer
const gracefulShutdown = async () => {
  if (asyncLogBuffer) {
    console.log('🔄 Flushing log buffer before shutdown...');
    await asyncLogBuffer.stop();
    console.log('✅ Log buffer flushed successfully');
  }

  if (logDeduplicator) {
    logDeduplicator.cleanup();
  }
};

// Register shutdown handlers
process.on('SIGTERM', gracefulShutdown);
process.on('SIGINT', gracefulShutdown);
process.on('beforeExit', gracefulShutdown);

module.exports = {
  logger,
  EnhancedLogger,
  logError,
  logInfo,
  logWarn,
  logDebug,
  logDeduplicator,
  asyncLogBuffer,
  gracefulShutdown
};