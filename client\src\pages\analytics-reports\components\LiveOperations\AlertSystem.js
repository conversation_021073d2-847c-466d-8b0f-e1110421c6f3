import React from 'react';

const AlertCard = ({ alert, type }) => {
  const getAlertConfig = (type) => {
    switch (type) {
      case 'stopped':
        return {
          icon: '⏹️',
          title: 'Stopped Alert',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          textColor: 'text-red-800',
          iconBg: 'bg-red-100'
        };
      case 'overdue':
        return {
          icon: '⚠️',
          title: 'Overdue Alert',
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-200',
          textColor: 'text-yellow-800',
          iconBg: 'bg-yellow-100'
        };
      case 'exception':
        return {
          icon: '⚡',
          title: 'Exception Alert',
          bgColor: 'bg-orange-50',
          borderColor: 'border-orange-200',
          textColor: 'text-orange-800',
          iconBg: 'bg-orange-100'
        };
      default:
        return {
          icon: '📢',
          title: 'System Alert',
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200',
          textColor: 'text-blue-800',
          iconBg: 'bg-blue-100'
        };
    }
  };

  const config = getAlertConfig(type);

  return (
    <div className={`${config.bgColor} ${config.borderColor} border rounded-lg p-4`}>
      <div className="flex items-start">
        <div className={`${config.iconBg} rounded-full p-2 mr-3`}>
          <span className="text-lg">{config.icon}</span>
        </div>
        <div className="flex-1">
          <h4 className={`font-medium ${config.textColor} mb-1`}>
            {alert.truckNumber} - {config.title}
          </h4>
          <p className={`text-sm ${config.textColor} opacity-90 mb-2`}>
            {alert.message || `${alert.driverName} needs attention`}
          </p>
          <div className="flex items-center justify-between">
            <div className={`text-xs ${config.textColor} opacity-75`}>
              {alert.currentLocationDisplay || alert.location}
            </div>
            <div className={`text-xs ${config.textColor} opacity-75`}>
              {alert.timeInPhaseMinutes && 
                `${Math.floor(alert.timeInPhaseMinutes / 60)}h ${Math.round(alert.timeInPhaseMinutes % 60)}m`
              }
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const AlertSummary = ({ summary }) => {
  if (!summary || !summary.alerts) {
    return null;
  }

  const alerts = summary.alerts;
  const totalAlerts = alerts.stopped + alerts.overdue + alerts.exception;

  if (totalAlerts === 0) {
    return (
      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <div className="flex items-center">
          <div className="bg-green-100 rounded-full p-2 mr-3">
            <span className="text-lg">✅</span>
          </div>
          <div>
            <h4 className="font-medium text-green-800">All Systems Normal</h4>
            <p className="text-sm text-green-700">No active alerts at this time</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="flex items-center">
          <div className="bg-red-100 rounded-full p-2 mr-3">
            <span className="text-lg">🔧</span>
          </div>
          <div>
            <div className="text-2xl font-bold text-red-800">{alerts.stopped}</div>
            <div className="text-sm text-red-600">Stopped Alerts</div>
          </div>
        </div>
      </div>

      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div className="flex items-center">
          <div className="bg-yellow-100 rounded-full p-2 mr-3">
            <span className="text-lg">⚠️</span>
          </div>
          <div>
            <div className="text-2xl font-bold text-yellow-800">{alerts.overdue}</div>
            <div className="text-sm text-yellow-600">Overdue Alerts</div>
          </div>
        </div>
      </div>

      <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
        <div className="flex items-center">
          <div className="bg-orange-100 rounded-full p-2 mr-3">
            <span className="text-lg">⚡</span>
          </div>
          <div>
            <div className="text-2xl font-bold text-orange-800">{alerts.exception}</div>
            <div className="text-sm text-orange-600">Exception Alerts</div>
          </div>
        </div>
      </div>
    </div>
  );
};

const AlertSystem = ({ data, loading }) => {
  if (loading) {
    return (
      <div className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="animate-pulse bg-secondary-200 h-20 rounded-lg"></div>
          ))}
        </div>
        <div className="space-y-3">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="animate-pulse bg-secondary-200 h-16 rounded-lg"></div>
          ))}
        </div>
      </div>
    );
  }

  if (!data || !data.operations) {
    return (
      <div className="bg-white rounded-lg shadow border border-secondary-200 p-8 text-center">
        <span className="text-4xl block mb-2">🚨</span>
        <p className="text-secondary-500">No alert data available</p>
      </div>
    );
  }

  // Filter operations by alert type
  const stoppedAlerts = data.operations.filter(op => op.alertStatus === 'stopped');
  const overdueAlerts = data.operations.filter(op => op.alertStatus === 'overdue');
  const exceptionAlerts = data.operations.filter(op => op.alertStatus === 'exception');

  const allAlerts = [...stoppedAlerts, ...overdueAlerts, ...exceptionAlerts];

  return (
    <div className="space-y-6">
      {/* Alert Summary */}
      <AlertSummary summary={data.summary} />

      {/* Active Alerts */}
      {allAlerts.length > 0 ? (
        <div className="bg-white rounded-lg shadow border border-secondary-200 p-6">
          <h3 className="text-lg font-medium text-secondary-900 mb-4">
            Active Alerts ({allAlerts.length})
          </h3>
          
          <div className="space-y-4">
            {/* Stopped Alerts */}
            {stoppedAlerts.map((alert) => (
              <AlertCard
                key={`stopped-${alert.truckId}`}
                alert={alert}
                type="stopped"
              />
            ))}
            
            {/* Overdue Alerts */}
            {overdueAlerts.map((alert) => (
              <AlertCard 
                key={`overdue-${alert.truckId}`} 
                alert={alert} 
                type="overdue" 
              />
            ))}
            
            {/* Exception Alerts */}
            {exceptionAlerts.map((alert) => (
              <AlertCard 
                key={`exception-${alert.truckId}`} 
                alert={alert} 
                type="exception" 
              />
            ))}
          </div>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow border border-secondary-200 p-8 text-center">
          <span className="text-4xl block mb-2">✅</span>
          <h3 className="text-lg font-medium text-secondary-900 mb-2">
            No Active Alerts
          </h3>
          <p className="text-secondary-500">
            All trucks are operating normally
          </p>
        </div>
      )}

      {/* Alert Guidelines */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="font-medium text-blue-800 mb-2">Alert Guidelines</h4>
        <div className="text-sm text-blue-700 space-y-1">
          <div>⏹️ <strong>Stopped:</strong> Truck reported mechanical issues or accidents</div>
          <div>⚠️ <strong>Overdue:</strong> Truck has been in current phase for more than 2 hours</div>
          <div>⚡ <strong>Exception:</strong> Trip has encountered routing or assignment exceptions</div>
        </div>
      </div>
    </div>
  );
};

export default AlertSystem;
