#!/usr/bin/env node

/**
 * Script Cleanup Utility
 * Safely removes unused script files while preserving core system functionality
 */

const fs = require('fs');
const path = require('path');

const SCRIPTS_DIR = path.join(__dirname);
const ARCHIVE_DIR = path.join(SCRIPTS_DIR, 'archive');

// Files to be archived (safe to remove)
const UNUSED_SCRIPTS = [
  'comprehensive-shift-fix.js',
  'final-fix.js',
  'final-shift-fix.js', 
  'final-shift-status-fix.js',
  'final-status-fix.js',
  'fix-function-conflict.js',
  'fix-shift-sync.js',
  'fix-shift-sync-improved.js',
  'create-auto-activation.js',
  'verify-ports.js',
  'restart-server.js'
];

// Essential scripts that must be preserved
const ESSENTIAL_SCRIPTS = [
  'start-dev.js',
  'start-dev-https.js',
  'start-prod.js', 
  'start-prod-https.js',
  'configure-env.js',
  'monitor-shift-status.js'
];

// Diagnostic scripts to evaluate (keep for now)
const DIAGNOSTIC_SCRIPTS = [
  'diagnose-analytics-data.js',
  'test-analytics-endpoints.js',
  'test-route-patterns.js',
  'fix-shift-cache.js'
];

async function cleanupScripts() {
  console.log('🧹 Script Cleanup Utility');
  console.log('========================\n');

  try {
    // Step 1: Create archive directory
    console.log('1. Creating archive directory...');
    if (!fs.existsSync(ARCHIVE_DIR)) {
      fs.mkdirSync(ARCHIVE_DIR, { recursive: true });
      console.log('   ✅ Archive directory created');
    } else {
      console.log('   ✅ Archive directory already exists');
    }

    // Step 2: Verify files exist before moving
    console.log('\n2. Verifying files to archive...');
    const filesToMove = [];
    const missingFiles = [];

    for (const filename of UNUSED_SCRIPTS) {
      const filePath = path.join(SCRIPTS_DIR, filename);
      if (fs.existsSync(filePath)) {
        filesToMove.push(filename);
        console.log(`   ✅ Found: ${filename}`);
      } else {
        missingFiles.push(filename);
        console.log(`   ⚠️  Missing: ${filename}`);
      }
    }

    if (missingFiles.length > 0) {
      console.log(`\n   Note: ${missingFiles.length} files were already removed or renamed`);
    }

    // Step 3: Move files to archive
    console.log('\n3. Moving files to archive...');
    let movedCount = 0;

    for (const filename of filesToMove) {
      const sourcePath = path.join(SCRIPTS_DIR, filename);
      const destPath = path.join(ARCHIVE_DIR, filename);

      try {
        // Read file content
        const content = fs.readFileSync(sourcePath, 'utf8');
        
        // Write to archive with timestamp header
        const timestamp = new Date().toISOString();
        const archivedContent = `// ARCHIVED ON: ${timestamp}\n// REASON: Unused script cleanup\n// ORIGINAL PATH: scripts/${filename}\n\n${content}`;
        
        fs.writeFileSync(destPath, archivedContent);
        
        // Remove original file
        fs.unlinkSync(sourcePath);
        
        movedCount++;
        console.log(`   ✅ Archived: ${filename}`);
      } catch (error) {
        console.log(`   ❌ Failed to archive ${filename}: ${error.message}`);
      }
    }

    // Step 4: Verify essential scripts are preserved
    console.log('\n4. Verifying essential scripts are preserved...');
    let essentialCount = 0;

    for (const filename of ESSENTIAL_SCRIPTS) {
      const filePath = path.join(SCRIPTS_DIR, filename);
      if (fs.existsSync(filePath)) {
        essentialCount++;
        console.log(`   ✅ Preserved: ${filename}`);
      } else {
        console.log(`   ❌ MISSING ESSENTIAL: ${filename}`);
      }
    }

    // Step 5: List diagnostic scripts (kept for evaluation)
    console.log('\n5. Diagnostic scripts (kept for evaluation)...');
    for (const filename of DIAGNOSTIC_SCRIPTS) {
      const filePath = path.join(SCRIPTS_DIR, filename);
      if (fs.existsSync(filePath)) {
        console.log(`   📋 Kept: ${filename}`);
      } else {
        console.log(`   ⚠️  Missing: ${filename}`);
      }
    }

    // Step 6: Create archive index
    console.log('\n6. Creating archive index...');
    const indexContent = `# Archived Scripts Index
Generated on: ${new Date().toISOString()}

## Reason for Archival
These scripts were one-time fixes or standalone utilities that are no longer needed for the core system operation.

## Archived Files
${filesToMove.map(f => `- ${f} - One-time fix/utility script`).join('\n')}

## Restoration
To restore any script, copy it back to the scripts directory:
\`\`\`bash
cp archive/${filesToMove[0] || 'filename.js'} ./
\`\`\`

## Safety
All archived scripts have been verified as unused by the core system:
- No imports found in server or client code
- No references in package.json scripts  
- No exported functions used elsewhere
`;

    fs.writeFileSync(path.join(ARCHIVE_DIR, 'README.md'), indexContent);
    console.log('   ✅ Archive index created');

    // Step 7: Summary
    console.log('\n📊 Cleanup Summary:');
    console.log('==================');
    console.log(`   Files archived: ${movedCount}`);
    console.log(`   Essential scripts preserved: ${essentialCount}`);
    console.log(`   Diagnostic scripts kept: ${DIAGNOSTIC_SCRIPTS.length}`);
    console.log(`   Archive location: ${ARCHIVE_DIR}`);

    if (movedCount > 0) {
      console.log('\n✅ Cleanup completed successfully!');
      console.log('\n📋 Next Steps:');
      console.log('   1. Test system startup: npm run dev');
      console.log('   2. Test production mode: npm run prod');
      console.log('   3. Verify configuration works');
      console.log('   4. If issues arise, restore from archive/');
    } else {
      console.log('\n✅ No files needed cleanup - system already clean!');
    }

  } catch (error) {
    console.error('❌ Cleanup failed:', error.message);
    console.error('\n🔄 Rollback instructions:');
    console.error('   If any issues occur, restore files from archive:');
    console.error('   cp scripts/archive/* scripts/');
    process.exit(1);
  }
}

// Run cleanup if executed directly
if (require.main === module) {
  cleanupScripts();
}

module.exports = { cleanupScripts, UNUSED_SCRIPTS, ESSENTIAL_SCRIPTS };